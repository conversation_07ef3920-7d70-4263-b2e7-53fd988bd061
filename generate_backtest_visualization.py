#!/usr/bin/env python3
"""
AstroA Backtest Visualization Generator
Creates a beautiful HTML visualization of backtesting results with interactive charts
"""
import json
import sys
from datetime import datetime
from pathlib import Path

def create_html_visualization(results_data):
    """Create comprehensive HTML visualization with CSS and SVG"""

    # Extract key metrics
    metrics = results_data.get('performance_metrics', {})
    snapshots = results_data.get('portfolio_snapshots', [])
    trades = results_data.get('trades_executed', [])
    risk_assessments = results_data.get('risk_assessments', [])

    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroA 2-Hour Backtest Results</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }}

        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}

        .header {{
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }}

        .header h1 {{
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}

        .header .subtitle {{
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }}

        .header .timestamp {{
            font-size: 1rem;
            opacity: 0.7;
        }}

        .dashboard {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}

        .card {{
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}

        .card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }}

        .card h3 {{
            font-size: 1.4rem;
            color: #4a5568;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }}

        .metric {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #f7fafc;
        }}

        .metric:last-child {{
            border-bottom: none;
        }}

        .metric-label {{
            font-weight: 500;
            color: #718096;
        }}

        .metric-value {{
            font-weight: 700;
            font-size: 1.1rem;
        }}

        .positive {{
            color: #38a169;
        }}

        .negative {{
            color: #e53e3e;
        }}

        .neutral {{
            color: #4a5568;
        }}

        .chart-container {{
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }}

        .chart-title {{
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }}

        .portfolio-chart {{
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
        }}

        .trades-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}

        .trades-table th,
        .trades-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }}

        .trades-table th {{
            background-color: #f7fafc;
            font-weight: 600;
            color: #4a5568;
        }}

        .trade-buy {{
            color: #38a169;
            font-weight: 600;
        }}

        .trade-sell {{
            color: #e53e3e;
            font-weight: 600;
        }}

        .progress-bar {{
            width: 100%;
            height: 20px;
            background-color: #e2e8f0;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }}

        .progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #38a169);
            border-radius: 10px;
            transition: width 0.3s ease;
        }}

        .risk-gauge {{
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }}

        .gauge {{
            width: 200px;
            height: 100px;
        }}

        .footer {{
            text-align: center;
            color: white;
            margin-top: 30px;
            opacity: 0.8;
        }}

        .grid-2 {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }}

        @media (max-width: 768px) {{
            .grid-2 {{
                grid-template-columns: 1fr;
            }}

            .header h1 {{
                font-size: 2rem;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🌟 AstroA Trading System</h1>
            <div class="subtitle">2-Hour Backtesting Results</div>
            <div class="timestamp">Generated on {datetime.now().strftime('%Y-%m-%d at %H:%M:%S UTC')}</div>
        </div>

        <!-- Key Metrics Dashboard -->
        <div class="dashboard">
            <div class="card">
                <h3>💰 Portfolio Performance</h3>
                <div class="metric">
                    <span class="metric-label">Initial Value</span>
                    <span class="metric-value neutral">${metrics.get('initial_value', 100000):,.2f}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Final Value</span>
                    <span class="metric-value neutral">${metrics.get('final_value', 100000):,.2f}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Return</span>
                    <span class="metric-value {'positive' if metrics.get('total_return_percent', 0) >= 0 else 'negative'}">{metrics.get('total_return_percent', 0):+.2f}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Max Drawdown</span>
                    <span class="metric-value negative">{metrics.get('max_drawdown_percent', 0):.2f}%</span>
                </div>
            </div>

            <div class="card">
                <h3>📊 Trading Activity</h3>
                <div class="metric">
                    <span class="metric-label">Total Trades</span>
                    <span class="metric-value neutral">{metrics.get('total_trades', 0)}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Analysis Cycles</span>
                    <span class="metric-value neutral">{metrics.get('analysis_cycles', 0)}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Positions</span>
                    <span class="metric-value neutral">{metrics.get('avg_positions', 0):.1f}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Sharpe Ratio</span>
                    <span class="metric-value {'positive' if metrics.get('sharpe_ratio', 0) > 0 else 'neutral'}">{metrics.get('sharpe_ratio', 0):.3f}</span>
                </div>
            </div>

            <div class="card">
                <h3>⚡ System Performance</h3>
                <div class="metric">
                    <span class="metric-label">Duration</span>
                    <span class="metric-value neutral">{metrics.get('duration_hours', 2):.1f} hours</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Data Points</span>
                    <span class="metric-value neutral">{results_data.get('data_points_collected', 0):,}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Errors</span>
                    <span class="metric-value {'negative' if metrics.get('error_count', 0) > 0 else 'positive'}">{metrics.get('error_count', 0)}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Success Rate</span>
                    <span class="metric-value positive">{100 - (metrics.get('error_count', 0) / max(metrics.get('analysis_cycles', 1), 1)) * 100:.1f}%</span>
                </div>
            </div>
        </div>

        <!-- Portfolio Value Chart -->
        <div class="chart-container">
            <div class="chart-title">📈 Portfolio Value Over Time</div>
            <svg class="portfolio-chart" viewBox="0 0 800 400">
                <!-- Chart background -->
                <rect width="800" height="400" fill="#f8fafc"/>

                <!-- Grid lines -->
                {generate_grid_lines()}

                <!-- Portfolio value line -->
                {generate_portfolio_line(snapshots)}

                <!-- Axis labels -->
                {generate_axis_labels(snapshots)}
            </svg>
        </div>

        <!-- Charts Grid -->
        <div class="grid-2">
            <!-- Risk Assessment -->
            <div class="chart-container">
                <div class="chart-title">⚠️ Risk Assessment</div>
                <div class="risk-gauge">
                    <svg class="gauge" viewBox="0 0 200 100">
                        {generate_risk_gauge(risk_assessments)}
                    </svg>
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <div style="font-size: 1.1rem; font-weight: 600; color: #4a5568;">
                        Current Risk Level: <span style="color: #e53e3e;">
                        {get_latest_risk_level(risk_assessments)}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Position Distribution -->
            <div class="chart-container">
                <div class="chart-title">🎯 Position Distribution</div>
                <svg width="100%" height="300" viewBox="0 0 300 300">
                    {generate_position_pie_chart(snapshots)}
                </svg>
            </div>
        </div>

        <!-- Recent Trades Table -->
        <div class="chart-container">
            <div class="chart-title">💼 Recent Trades</div>
            {generate_trades_table(trades)}
        </div>

        <!-- System Status -->
        <div class="chart-container">
            <div class="chart-title">🔧 System Status & Health</div>
            <div class="dashboard">
                <div style="text-align: center;">
                    <h4 style="color: #4a5568; margin-bottom: 15px;">Data Collection Status</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {min(100, (results_data.get('data_points_collected', 0) / 240) * 100)}%"></div>
                    </div>
                    <p style="color: #718096; margin-top: 5px;">
                        {results_data.get('data_points_collected', 0)} / 240 expected data points
                    </p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Generated by AstroA Autonomous Trading System</p>
            <p>Powered by DeepSeek AI & Mathematical Analysis Engines</p>
        </div>
    </div>
</body>
</html>
"""

    return html_content

def generate_grid_lines():
    """Generate SVG grid lines"""
    lines = []
    # Horizontal lines
    for i in range(0, 401, 80):
        lines.append(f'<line x1="50" y1="{i + 50}" x2="750" y2="{i + 50}" stroke="#e2e8f0" stroke-width="1"/>')

    # Vertical lines
    for i in range(50, 751, 140):
        lines.append(f'<line x1="{i}" y1="50" x2="{i}" y2="350" stroke="#e2e8f0" stroke-width="1"/>')

    return '\\n'.join(lines)

def generate_portfolio_line(snapshots):
    """Generate SVG line for portfolio value"""
    if not snapshots:
        return ''

    # Calculate points for the line
    points = []
    min_value = min(s['total_value'] for s in snapshots)
    max_value = max(s['total_value'] for s in snapshots)
    value_range = max_value - min_value if max_value != min_value else 1

    for i, snapshot in enumerate(snapshots):
        x = 50 + (i / max(len(snapshots) - 1, 1)) * 700
        y = 350 - ((snapshot['total_value'] - min_value) / value_range) * 300
        points.append(f"{x},{y}")

    path = f'<polyline points="{" ".join(points)}" fill="none" stroke="#4299e1" stroke-width="3"/>'

    # Add data points
    circles = []
    for i, snapshot in enumerate(snapshots):
        x = 50 + (i / max(len(snapshots) - 1, 1)) * 700
        y = 350 - ((snapshot['total_value'] - min_value) / value_range) * 300
        circles.append(f'<circle cx="{x}" cy="{y}" r="4" fill="#2b6cb0"/>')

    return path + '\\n' + '\\n'.join(circles)

def generate_axis_labels(snapshots):
    """Generate axis labels"""
    if not snapshots:
        return ''

    labels = []

    # Y-axis labels (values)
    min_value = min(s['total_value'] for s in snapshots) if snapshots else 100000
    max_value = max(s['total_value'] for s in snapshots) if snapshots else 100000

    for i in range(5):
        y = 350 - (i / 4) * 300
        value = min_value + (i / 4) * (max_value - min_value)
        labels.append(f'<text x="40" y="{y + 5}" text-anchor="end" font-size="12" fill="#718096">${value:,.0f}</text>')

    # X-axis labels (time)
    for i in range(6):
        x = 50 + (i / 5) * 700
        time_label = f"{i * 24}min"  # 2 hours = 120 min, so 5 intervals = 24 min each
        labels.append(f'<text x="{x}" y="375" text-anchor="middle" font-size="12" fill="#718096">{time_label}</text>')

    return '\\n'.join(labels)

def generate_risk_gauge(risk_assessments):
    """Generate risk gauge SVG"""
    # Calculate average risk
    avg_risk = 0.3  # Default medium risk
    if risk_assessments:
        total_risk = sum(r.get('risk_score', 0.3) for r in risk_assessments)
        avg_risk = total_risk / len(risk_assessments)

    # Clamp between 0 and 1
    avg_risk = max(0, min(1, avg_risk))

    # Calculate angle (0 to 180 degrees)
    angle = avg_risk * 180

    # Generate gauge arc
    gauge = f'''
    <!-- Gauge background -->
    <path d="M 20 80 A 80 80 0 0 1 180 80" fill="none" stroke="#e2e8f0" stroke-width="10"/>

    <!-- Risk zones -->
    <path d="M 20 80 A 80 80 0 0 1 100 10" fill="none" stroke="#38a169" stroke-width="8"/>
    <path d="M 100 10 A 80 80 0 0 1 180 80" fill="none" stroke="#e53e3e" stroke-width="8"/>

    <!-- Needle -->
    <line x1="100" y1="80" x2="{100 + 60 * (-1) * (1 if angle <= 90 else -1) * abs(90 - angle) / 90}" y2="{80 - 60 * (1 - abs(angle - 90) / 90)}" stroke="#2d3748" stroke-width="3"/>
    <circle cx="100" cy="80" r="5" fill="#2d3748"/>
    '''

    return gauge

def get_latest_risk_level(risk_assessments):
    """Get the latest risk level"""
    if not risk_assessments:
        return "Medium"

    latest_risk = risk_assessments[-1].get('risk_score', 0.3)

    if latest_risk < 0.3:
        return "Low"
    elif latest_risk < 0.7:
        return "Medium"
    else:
        return "High"

def generate_position_pie_chart(snapshots):
    """Generate position distribution pie chart"""
    if not snapshots or not snapshots[-1]['positions']:
        return '<text x="150" y="150" text-anchor="middle" font-size="16" fill="#718096">No active positions</text>'

    latest_snapshot = snapshots[-1]
    positions = latest_snapshot['positions']

    # Calculate position values
    total_value = sum(abs(p['quantity'] * p['current_price']) for p in positions)

    # Generate pie slices
    start_angle = 0
    slices = []
    colors = ['#4299e1', '#38a169', '#ed8936', '#e53e3e', '#9f7aea', '#38b2ac']

    for i, position in enumerate(positions[:6]):  # Limit to 6 positions
        value = abs(position['quantity'] * position['current_price'])
        percentage = value / total_value if total_value > 0 else 0
        angle = percentage * 360

        # Calculate slice path
        end_angle = start_angle + angle
        large_arc = 1 if angle > 180 else 0

        x1 = 150 + 80 * (angle / 360 * 6.28318)  # Simplified calculation
        y1 = 150 + 80 * (angle / 360 * 6.28318)  # Simplified calculation

        color = colors[i % len(colors)]

        # Add slice (simplified)
        slices.append(f'<circle cx="150" cy="150" r="{20 + i * 10}" fill="{color}" opacity="0.7"/>')

        start_angle = end_angle

    return '\\n'.join(slices)

def generate_trades_table(trades):
    """Generate trades table HTML"""
    if not trades:
        return '<p style="text-align: center; color: #718096; padding: 20px;">No trades executed during backtest</p>'

    # Show last 10 trades
    recent_trades = trades[-10:] if len(trades) > 10 else trades

    table_html = '''
    <table class="trades-table">
        <thead>
            <tr>
                <th>Symbol</th>
                <th>Action</th>
                <th>Quantity</th>
                <th>Price</th>
                <th>Time</th>
                <th>Strategy</th>
                <th>Confidence</th>
            </tr>
        </thead>
        <tbody>
    '''

    for trade in recent_trades:
        action_class = 'trade-buy' if trade['action'] in ['buy', 'strong_buy'] else 'trade-sell'
        time_str = trade['timestamp'][:16].replace('T', ' ')  # Format timestamp

        table_html += f'''
        <tr>
            <td>{trade['symbol']}</td>
            <td class="{action_class}">{trade['action'].upper()}</td>
            <td>{trade['quantity']:.2f}</td>
            <td>${trade['price']:.2f}</td>
            <td>{time_str}</td>
            <td>{trade['strategy_id']}</td>
            <td>{trade['confidence']:.1%}</td>
        </tr>
        '''

    table_html += '''
        </tbody>
    </table>
    '''

    return table_html

def main():
    """Main function to generate visualization"""
    if len(sys.argv) != 2:
        print("Usage: python generate_backtest_visualization.py <results_file.json>")
        sys.exit(1)

    results_file = Path(sys.argv[1])

    if not results_file.exists():
        print(f"Error: Results file {results_file} not found")
        sys.exit(1)

    # Load results data
    with open(results_file, 'r') as f:
        results_data = json.load(f)

    # Generate HTML
    html_content = create_html_visualization(results_data)

    # Save HTML file
    output_file = results_file.parent / f"backtest_visualization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

    with open(output_file, 'w') as f:
        f.write(html_content)

    print(f"✅ Visualization generated: {output_file}")
    print(f"🌐 Open the file in your browser to view the results")

if __name__ == '__main__':
    main()