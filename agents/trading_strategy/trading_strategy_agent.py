"""
Trading Strategy Agent - Main coordinator for trading strategies
"""
import asyncio
import psycopg2
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional
import os
import json
from dotenv import load_dotenv

from shared.utils.base_agent import BaseAgent
from shared.types.agent_types import AgentType, MessageType
from shared.types.strategy_types import TradingSignal, Position, Order, PortfolioSummary
from agents.trading_strategy.risk_management.risk_manager import RiskManager
from agents.trading_strategy.strategies.mean_reversion_strategy import MeanReversionStrategy
from agents.trading_strategy.strategies.momentum_strategy import MomentumStrategy

load_dotenv()

class TradingStrategyAgent(BaseAgent):
    """Agent responsible for trading strategy execution and portfolio management"""

    def __init__(self, agent_id: str = "trading_strategy_001"):
        super().__init__(agent_id, AgentType.TRADING_STRATEGY)

        # Initialize components
        self.risk_manager = RiskManager()
        self.strategies = {}
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []

        # Database connection
        self.db_connection = None

        # Portfolio settings
        self.initial_capital = 100000  # $100k initial capital
        self.current_cash = self.initial_capital
        self.portfolio_value = self.initial_capital

        # Strategy execution settings
        self.strategy_execution_interval = 300  # 5 minutes
        self.risk_assessment_interval = 60     # 1 minute
        self.portfolio_update_interval = 30    # 30 seconds

        # Last execution times
        self.last_strategy_execution = None
        self.last_risk_assessment = None
        self.last_portfolio_update = None

        # Performance tracking
        self.daily_returns = []
        self.portfolio_history = []

    async def initialize(self):
        """Initialize trading strategy agent"""
        self.logger.info("Initializing Trading Strategy Agent...")

        # Setup database connection
        try:
            self.db_connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'mathematical_trading'),
                user=os.getenv('DB_USER', 'trading_user'),
                password=os.getenv('DB_PASSWORD', 'hejhej')
            )
            self.logger.info("Database connection established")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise

        # Initialize strategies
        await self._initialize_strategies()

        # Load existing positions and portfolio state
        await self._load_portfolio_state()

        self.logger.info("Trading Strategy Agent initialized successfully")

    async def _initialize_strategies(self):
        """Initialize trading strategies"""
        try:
            # Get available symbols from database
            symbols = await self._get_available_symbols()

            if not symbols:
                self.logger.warning("No symbols found in database")
                return

            # Initialize mean reversion strategy
            mean_reversion = MeanReversionStrategy(
                strategy_id="mean_reversion_001",
                symbols=symbols[:5]  # Use first 5 symbols
            )
            await mean_reversion.activate()
            self.strategies["mean_reversion_001"] = mean_reversion

            # Initialize momentum strategy
            momentum = MomentumStrategy(
                strategy_id="momentum_001",
                symbols=symbols[5:10] if len(symbols) > 5 else symbols[:3]  # Use different symbols
            )
            await momentum.activate()
            self.strategies["momentum_001"] = momentum

            self.logger.info(f"Initialized {len(self.strategies)} trading strategies")

        except Exception as e:
            self.logger.error(f"Error initializing strategies: {e}")

    async def _get_available_symbols(self) -> List[str]:
        """Get available symbols from market data"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT DISTINCT symbol
                FROM market_data
                WHERE timestamp > NOW() - INTERVAL '7 days'
                ORDER BY symbol
                LIMIT 10
            """)

            symbols = [row[0] for row in cursor.fetchall()]
            cursor.close()
            return symbols

        except Exception as e:
            self.logger.error(f"Error getting available symbols: {e}")
            return []

    async def execute_main_logic(self):
        """Main execution logic for trading strategy agent"""
        current_time = datetime.now()

        # Update portfolio value
        if (self.last_portfolio_update is None or
            (current_time - self.last_portfolio_update).seconds >= self.portfolio_update_interval):
            await self._update_portfolio_value()
            self.last_portfolio_update = current_time

        # Risk assessment
        if (self.last_risk_assessment is None or
            (current_time - self.last_risk_assessment).seconds >= self.risk_assessment_interval):
            await self._assess_portfolio_risk()
            self.last_risk_assessment = current_time

        # Strategy execution
        if (self.last_strategy_execution is None or
            (current_time - self.last_strategy_execution).seconds >= self.strategy_execution_interval):
            await self._execute_strategies()
            self.last_strategy_execution = current_time

    async def _execute_strategies(self):
        """Execute all active strategies"""
        try:
            self.logger.info("Executing trading strategies...")

            # Get recent market data
            market_data = await self._get_recent_market_data()
            if market_data.empty:
                self.logger.warning("No recent market data available")
                return

            # Get analysis results
            analysis_results = await self._get_analysis_results()

            # Execute each strategy
            all_signals = []
            for strategy_id, strategy in self.strategies.items():
                if strategy.is_active:
                    try:
                        signals = await strategy.generate_signals(market_data, analysis_results)
                        all_signals.extend(signals)
                        self.logger.info(f"Strategy {strategy_id} generated {len(signals)} signals")
                    except Exception as e:
                        self.logger.error(f"Error executing strategy {strategy_id}: {e}")

            # Process signals
            if all_signals:
                await self._process_trading_signals(all_signals)

            # Check exit conditions for existing positions
            await self._check_exit_conditions(market_data, analysis_results)

            self.logger.info(f"Strategy execution completed. Generated {len(all_signals)} signals")

        except Exception as e:
            self.logger.error(f"Error in strategy execution: {e}")

    async def _process_trading_signals(self, signals: List[TradingSignal]):
        """Process and potentially execute trading signals"""
        for signal in signals:
            try:
                # Check if we already have a position in this symbol
                if signal.symbol in self.positions:
                    self.logger.info(f"Already have position in {signal.symbol}, skipping signal")
                    continue

                # Risk assessment
                risk_level = self.risk_manager.get_risk_assessment(
                    signal,
                    {"volatility": signal.metadata.get("volatility", 0.02)}
                )

                # Calculate position size
                position_size = self.risk_manager.calculate_position_size(
                    signal=signal,
                    portfolio_value=self.portfolio_value,
                    volatility=signal.metadata.get("volatility", 0.02),
                    price=signal.price
                )

                # Apply risk-based position size reduction
                risk_multiplier = self.risk_manager.should_reduce_position_size(risk_level)
                final_position_size = position_size * risk_multiplier

                # Check risk limits
                can_trade, risk_message = self.risk_manager.check_risk_limits(
                    signal=signal,
                    proposed_position_size=final_position_size,
                    current_positions=self.positions,
                    portfolio_value=self.portfolio_value
                )

                if not can_trade:
                    self.logger.warning(f"Risk check failed for {signal.symbol}: {risk_message}")
                    continue

                # Check if we have enough cash
                required_cash = final_position_size * signal.price
                if required_cash > self.current_cash:
                    self.logger.warning(f"Insufficient cash for {signal.symbol}. Required: ${required_cash:.2f}, Available: ${self.current_cash:.2f}")
                    continue

                # Execute trade (simulated)
                await self._execute_trade(signal, final_position_size)

                self.logger.info(f"Executed trade: {signal.signal_type.value} {final_position_size:.2f} shares of {signal.symbol} at ${signal.price:.2f}")

            except Exception as e:
                self.logger.error(f"Error processing signal for {signal.symbol}: {e}")

    async def _execute_trade(self, signal: TradingSignal, position_size: float):
        """Execute a trade (simulated)"""
        try:
            # Calculate stop loss and take profit
            stop_loss = signal.metadata.get('stop_loss_price')
            take_profit = signal.metadata.get('take_profit_price')

            # Create position
            position_type = 'long' if signal.signal_type.value in ['buy', 'strong_buy'] else 'short'

            position = Position(
                symbol=signal.symbol,
                quantity=position_size if position_type == 'long' else -position_size,
                entry_price=signal.price,
                current_price=signal.price,
                entry_time=datetime.now(),
                position_type=position_type,
                stop_loss=stop_loss,
                take_profit=take_profit
            )

            # Update portfolio
            trade_value = position_size * signal.price
            self.current_cash -= trade_value
            self.positions[signal.symbol] = position

            # Record trade in database
            await self._record_trade(signal, position)

            # Send notification to other agents
            await self.send_message(
                receiver_id="data_collector_001",
                message_type=MessageType.ALERT,
                payload={
                    "type": "trade_executed",
                    "symbol": signal.symbol,
                    "action": signal.signal_type.value,
                    "quantity": position_size,
                    "price": signal.price,
                    "timestamp": datetime.now().isoformat()
                }
            )

        except Exception as e:
            self.logger.error(f"Error executing trade for {signal.symbol}: {e}")

    async def _check_exit_conditions(self, market_data: pd.DataFrame, analysis_results: Dict[str, Any]):
        """Check exit conditions for existing positions"""
        positions_to_close = []

        for symbol, position in self.positions.items():
            try:
                # Get current market data for this symbol
                symbol_data = market_data[market_data['symbol'] == symbol]
                if symbol_data.empty:
                    continue

                current_price = symbol_data['close_price'].iloc[-1]

                # Update position current price and PnL
                position.current_price = current_price
                if position.position_type == 'long':
                    position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * abs(position.quantity)

                # Get strategy that created this position
                strategy = None
                for strat in self.strategies.values():
                    if symbol in strat.symbols:
                        strategy = strat
                        break

                if strategy:
                    # Prepare current data for strategy
                    current_data = {
                        'current_price': current_price,
                        'symbol_data': symbol_data
                    }

                    # Add analysis results if available
                    if symbol in analysis_results:
                        current_data.update(analysis_results[symbol])

                    # Check if strategy wants to exit
                    should_exit = await strategy.should_exit_position(position, current_data)

                    if should_exit:
                        positions_to_close.append(symbol)

            except Exception as e:
                self.logger.error(f"Error checking exit conditions for {symbol}: {e}")

        # Close positions that should be exited
        for symbol in positions_to_close:
            await self._close_position(symbol)

    async def _close_position(self, symbol: str):
        """Close a position"""
        try:
            if symbol not in self.positions:
                return

            position = self.positions[symbol]

            # Calculate final PnL
            trade_value = abs(position.quantity) * position.current_price
            position.realized_pnl = position.unrealized_pnl

            # Update cash
            if position.position_type == 'long':
                self.current_cash += trade_value
            else:
                self.current_cash += 2 * position.entry_price * abs(position.quantity) - trade_value

            # Record position closure
            await self._record_position_closure(position)

            # Remove position
            del self.positions[symbol]

            self.logger.info(f"Closed position in {symbol}. PnL: ${position.realized_pnl:.2f}")

        except Exception as e:
            self.logger.error(f"Error closing position for {symbol}: {e}")

    async def _update_portfolio_value(self):
        """Update current portfolio value"""
        try:
            positions_value = 0
            for position in self.positions.values():
                position_value = abs(position.quantity) * position.current_price
                positions_value += position_value

            self.portfolio_value = self.current_cash + positions_value

            # Record portfolio history
            self.portfolio_history.append({
                'timestamp': datetime.now(),
                'total_value': self.portfolio_value,
                'cash': self.current_cash,
                'positions_value': positions_value
            })

            # Keep only last 1000 records
            if len(self.portfolio_history) > 1000:
                self.portfolio_history = self.portfolio_history[-1000:]

        except Exception as e:
            self.logger.error(f"Error updating portfolio value: {e}")

    async def _assess_portfolio_risk(self):
        """Assess current portfolio risk"""
        try:
            risk_metrics = self.risk_manager.calculate_portfolio_risk(self.positions)

            # Log risk metrics
            self.logger.info(f"Portfolio Risk Assessment - Total Risk: {risk_metrics['total_risk']:.2%}, VaR 95%: ${risk_metrics['var_95']:.2f}")

            # Update risk manager with portfolio performance
            if len(self.portfolio_history) >= 2:
                last_value = self.portfolio_history[-2]['total_value']
                current_value = self.portfolio_history[-1]['total_value']
                daily_return = (current_value - last_value) / last_value
                self.risk_manager.update_historical_performance(daily_return)

        except Exception as e:
            self.logger.error(f"Error assessing portfolio risk: {e}")

    async def _get_recent_market_data(self) -> pd.DataFrame:
        """Get recent market data from database"""
        try:
            cursor = self.db_connection.cursor()

            # Get data from last 100 periods
            cursor.execute("""
                SELECT symbol, timestamp, open_price, high_price, low_price, close_price, volume
                FROM market_data
                WHERE timestamp > NOW() - INTERVAL '2 days'
                ORDER BY symbol, timestamp DESC
                LIMIT 1000
            """)

            data = cursor.fetchall()
            cursor.close()

            if data:
                df = pd.DataFrame(data, columns=[
                    'symbol', 'timestamp', 'open_price', 'high_price',
                    'low_price', 'close_price', 'volume'
                ])
                return df
            else:
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            return pd.DataFrame()

    async def _get_analysis_results(self) -> Dict[str, Any]:
        """Get analysis results from mathematical engine"""
        try:
            # Try to get from Redis cache first
            analysis_key = "mathematical_analysis:latest"
            cached_results = self.redis_client.get(analysis_key)

            if cached_results:
                return json.loads(cached_results)
            else:
                # Fallback to basic analysis
                return {}

        except Exception as e:
            self.logger.error(f"Error getting analysis results: {e}")
            return {}

    async def _record_trade(self, signal: TradingSignal, position: Position):
        """Record trade in database"""
        try:
            cursor = self.db_connection.cursor()

            cursor.execute("""
                INSERT INTO trades
                (symbol, action, quantity, price, timestamp, strategy_id, confidence, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                signal.symbol,
                signal.signal_type.value,
                abs(position.quantity),
                signal.price,
                datetime.now(),
                signal.strategy_id,
                signal.confidence,
                json.dumps(signal.metadata)
            ))

            self.db_connection.commit()
            cursor.close()

        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")

    async def _record_position_closure(self, position: Position):
        """Record position closure in database"""
        try:
            cursor = self.db_connection.cursor()

            cursor.execute("""
                INSERT INTO position_closures
                (symbol, entry_price, exit_price, quantity, entry_time, exit_time, realized_pnl)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                position.symbol,
                position.entry_price,
                position.current_price,
                position.quantity,
                position.entry_time,
                datetime.now(),
                position.realized_pnl
            ))

            self.db_connection.commit()
            cursor.close()

        except Exception as e:
            self.logger.error(f"Error recording position closure: {e}")

    async def _load_portfolio_state(self):
        """Load existing portfolio state from database"""
        try:
            # This would load any existing positions from database
            # For now, we start fresh
            self.logger.info("Starting with fresh portfolio state")

        except Exception as e:
            self.logger.error(f"Error loading portfolio state: {e}")

    def get_portfolio_summary(self) -> PortfolioSummary:
        """Get current portfolio summary"""
        positions_value = sum(abs(pos.quantity) * pos.current_price for pos in self.positions.values())
        total_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())

        # Calculate daily PnL
        daily_pnl = 0
        if len(self.portfolio_history) >= 2:
            today_value = self.portfolio_history[-1]['total_value']
            yesterday_value = self.portfolio_history[-2]['total_value']
            daily_pnl = today_value - yesterday_value

        return PortfolioSummary(
            total_value=self.portfolio_value,
            cash=self.current_cash,
            positions_value=positions_value,
            total_pnl=total_pnl,
            daily_pnl=daily_pnl,
            positions_count=len(self.positions),
            risk_score=0.0,  # Would calculate from risk manager
            max_drawdown=self.risk_manager.max_historical_drawdown
        )

    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            self.db_connection.close()
            self.logger.info("Database connection closed")