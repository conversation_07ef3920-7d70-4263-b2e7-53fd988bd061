"""
Risk Management Engine for Trading Strategies
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

from shared.types.strategy_types import Position, TradingSignal, RiskLevel

# Constants for risk calculations
TRADING_DAYS_PER_YEAR = 252
VAR_CONFIDENCE_LEVELS = {95: 1.645, 99: 2.326}

class RiskManager:
    """Advanced risk management for trading strategies"""

    def __init__(self,
                 max_portfolio_risk: float = 0.02,
                 max_position_risk: float = 0.01,
                 max_correlation_limit: float = 0.7,
                 max_drawdown_limit: float = 0.15):

        self.max_portfolio_risk = max_portfolio_risk  # 2% daily portfolio risk
        self.max_position_risk = max_position_risk    # 1% per position
        self.max_correlation_limit = max_correlation_limit  # 70% max correlation
        self.max_drawdown_limit = max_drawdown_limit  # 15% max drawdown

        self.logger = logging.getLogger("risk_manager")

        # Risk tracking
        self.current_positions: Dict[str, Position] = {}
        self.historical_returns: List[float] = []
        self.max_historical_drawdown = 0.0

    def calculate_position_size(self,
                              signal: TradingSignal,
                              portfolio_value: float,
                              volatility: float,
                              price: float) -> float:
        """Calculate optimal position size using Kelly Criterion and risk limits"""

        # Base position size using risk-based approach
        risk_amount = portfolio_value * self.max_position_risk

        # Calculate position size based on stop loss distance
        if signal.metadata and 'stop_loss_price' in signal.metadata:
            stop_loss_price = signal.metadata['stop_loss_price']
            risk_per_share = abs(price - stop_loss_price)

            if risk_per_share > 0:
                base_position_size = risk_amount / risk_per_share
            else:
                base_position_size = 0
        else:
            # Use volatility-based sizing if no stop loss
            if volatility > 0:
                base_position_size = risk_amount / (price * volatility * 2)
            else:
                base_position_size = 0

        # Apply Kelly Criterion adjustment if we have confidence and win rate data
        if signal.metadata and 'win_rate' in signal.metadata:
            win_rate = signal.metadata['win_rate']
            avg_win = signal.metadata.get('avg_win', 0.02)
            avg_loss = signal.metadata.get('avg_loss', 0.01)

            if avg_loss > 0:
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
                base_position_size *= kelly_fraction

        # Apply portfolio concentration limits
        max_concentration = portfolio_value * 0.1  # Max 10% in any single position
        max_position_value = max_concentration / price

        final_position_size = min(base_position_size, max_position_value)

        self.logger.info(f"Position size calculated for {signal.symbol}: {final_position_size:.2f} shares")
        return max(0, final_position_size)

    def calculate_portfolio_risk(self, positions: Dict[str, Position]) -> Dict[str, float]:
        """Calculate current portfolio risk metrics"""
        if not positions:
            return {"total_risk": 0.0, "var_95": 0.0, "expected_shortfall": 0.0}

        # Calculate position values and weights
        total_value = sum(pos.quantity * pos.current_price for pos in positions.values())

        if total_value <= 0:
            return {"total_risk": 0.0, "var_95": 0.0, "expected_shortfall": 0.0}

        # Calculate weighted portfolio risk
        portfolio_weights = []
        individual_risks = []

        for symbol, position in positions.items():
            weight = (position.quantity * position.current_price) / total_value
            portfolio_weights.append(weight)

            # Estimate individual position risk (simplified)
            position_risk = abs(position.unrealized_pnl / total_value) if total_value > 0 else 0
            individual_risks.append(position_risk)

        # Portfolio risk metrics
        weighted_risk = sum(w * r for w, r in zip(portfolio_weights, individual_risks))

        # VaR calculation (simplified - would need historical returns in practice)
        if self.historical_returns:
            returns_array = np.array(self.historical_returns)
            var_95 = np.percentile(returns_array, 5) * total_value
            var_99 = np.percentile(returns_array, 1) * total_value

            # Expected Shortfall (Conditional VaR)
            tail_returns = returns_array[returns_array <= np.percentile(returns_array, 5)]
            expected_shortfall = np.mean(tail_returns) * total_value if len(tail_returns) > 0 else 0
        else:
            var_95 = weighted_risk * total_value * 2.33  # Approximation
            var_99 = weighted_risk * total_value * 3.09
            expected_shortfall = var_95 * 1.3

        return {
            "total_risk": weighted_risk,
            "var_95": abs(var_95),
            "var_99": abs(var_99),
            "expected_shortfall": abs(expected_shortfall),
            "positions_count": len(positions),
            "concentration_risk": max(portfolio_weights) if portfolio_weights else 0
        }

    def check_risk_limits(self,
                         signal: TradingSignal,
                         proposed_position_size: float,
                         current_positions: Dict[str, Position],
                         portfolio_value: float) -> Tuple[bool, str]:
        """Check if a proposed trade violates risk limits"""

        # Check portfolio concentration
        position_value = proposed_position_size * signal.price
        concentration = position_value / portfolio_value if portfolio_value > 0 else 0

        if concentration > 0.15:  # Max 15% concentration
            return False, f"Position concentration ({concentration:.1%}) exceeds limit (15%)"

        # Check correlation limits (simplified check)
        if len(current_positions) > 0:
            symbols_in_portfolio = list(current_positions.keys())
            if signal.symbol in symbols_in_portfolio:
                return False, "Position already exists for this symbol"

        # Check total portfolio risk
        risk_metrics = self.calculate_portfolio_risk(current_positions)
        if risk_metrics["total_risk"] > self.max_portfolio_risk:
            return False, f"Portfolio risk ({risk_metrics['total_risk']:.1%}) exceeds limit ({self.max_portfolio_risk:.1%})"

        # Check maximum drawdown
        if self.max_historical_drawdown > self.max_drawdown_limit:
            return False, f"Maximum drawdown ({self.max_historical_drawdown:.1%}) exceeds limit ({self.max_drawdown_limit:.1%})"

        return True, "Risk checks passed"

    def calculate_stop_loss(self,
                          entry_price: float,
                          signal: TradingSignal,
                          volatility: float,
                          atr: float = None) -> float:
        """Calculate dynamic stop loss based on volatility and market conditions"""

        # Base stop loss using ATR (Average True Range)
        if atr and atr > 0:
            stop_distance = atr * 2.0  # 2x ATR stop
        else:
            # Fallback to volatility-based stop
            stop_distance = entry_price * volatility * 2.0

        # Adjust based on signal confidence
        confidence_multiplier = 1.0 + (signal.confidence - 0.5) * 0.5
        stop_distance *= confidence_multiplier

        # Calculate stop loss price
        if signal.signal_type.value in ['buy', 'strong_buy']:
            stop_loss_price = entry_price - stop_distance
        else:
            stop_loss_price = entry_price + stop_distance

        # Ensure minimum stop distance (0.5%)
        min_stop_distance = entry_price * 0.005
        if abs(entry_price - stop_loss_price) < min_stop_distance:
            if signal.signal_type.value in ['buy', 'strong_buy']:
                stop_loss_price = entry_price - min_stop_distance
            else:
                stop_loss_price = entry_price + min_stop_distance

        return stop_loss_price

    def calculate_take_profit(self,
                            entry_price: float,
                            stop_loss_price: float,
                            signal: TradingSignal,
                            risk_reward_ratio: float = 2.0) -> float:
        """Calculate take profit level based on risk-reward ratio"""

        risk_amount = abs(entry_price - stop_loss_price)
        reward_amount = risk_amount * risk_reward_ratio

        # Adjust based on signal strength
        if signal.signal_type.value in ['strong_buy', 'strong_sell']:
            reward_amount *= 1.5  # Increase target for strong signals

        # Calculate take profit price
        if signal.signal_type.value in ['buy', 'strong_buy']:
            take_profit_price = entry_price + reward_amount
        else:
            take_profit_price = entry_price - reward_amount

        return take_profit_price

    def update_historical_performance(self, daily_return: float):
        """Update historical performance tracking"""
        self.historical_returns.append(daily_return)

        # Keep only last 252 days (1 year)
        if len(self.historical_returns) > 252:
            self.historical_returns = self.historical_returns[-252:]

        # Update maximum drawdown
        if len(self.historical_returns) >= 2:
            cumulative_returns = np.cumprod(1 + np.array(self.historical_returns))
            peak = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - peak) / peak
            self.max_historical_drawdown = abs(np.min(drawdown))

    def get_risk_assessment(self,
                          signal: TradingSignal,
                          current_market_conditions: Dict[str, any]) -> RiskLevel:
        """Assess overall risk level for a trading signal"""

        risk_score = 0

        # Signal confidence factor
        if signal.confidence < 0.3:
            risk_score += 2
        elif signal.confidence < 0.6:
            risk_score += 1

        # Market volatility factor
        if current_market_conditions.get('volatility', 0) > 0.3:
            risk_score += 2
        elif current_market_conditions.get('volatility', 0) > 0.2:
            risk_score += 1

        # Market trend factor
        if current_market_conditions.get('trend_strength', 0) < 0.3:
            risk_score += 1

        # Current drawdown factor
        if self.max_historical_drawdown > 0.1:
            risk_score += 2
        elif self.max_historical_drawdown > 0.05:
            risk_score += 1

        # Convert score to risk level
        if risk_score >= 5:
            return RiskLevel.EXTREME
        elif risk_score >= 3:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def should_reduce_position_size(self, risk_level: RiskLevel) -> float:
        """Get position size multiplier based on risk level"""
        multipliers = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.7,
            RiskLevel.HIGH: 0.4,
            RiskLevel.EXTREME: 0.1
        }
        return multipliers.get(risk_level, 0.5)