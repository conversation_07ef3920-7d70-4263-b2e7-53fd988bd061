"""
Mean Reversion Trading Strategy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime
from decimal import Decimal

from shared.utils.base_strategy import BaseStrategy
from shared.types.strategy_types import (
    StrategyType, TradingSignal, SignalType, Position
)

class MeanReversionStrategy(BaseStrategy):
    """Mean reversion strategy using statistical analysis"""

    def __init__(self, strategy_id: str, symbols: List[str]):
        default_params = {
            'lookback_period': 20,
            'z_score_entry': 2.0,
            'z_score_exit': 0.5,
            'min_confidence': 0.6,
            'max_holding_period': 10  # days
        }

        super().__init__(
            strategy_id=strategy_id,
            strategy_type=StrategyType.MEAN_REVERSION,
            symbols=symbols,
            parameters=default_params
        )

    async def generate_signals(self,
                             market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate mean reversion signals"""
        signals = []

        for symbol in self.symbols:
            try:
                # Filter data for this symbol
                symbol_data = market_data[market_data['symbol'] == symbol].copy()
                if len(symbol_data) < self.parameters['lookback_period']:
                    continue

                # Sort by timestamp
                symbol_data = symbol_data.sort_values('timestamp')

                # Convert Decimal to float for calculations
                symbol_data['close_price'] = symbol_data['close_price'].astype(float)
                symbol_data['high_price'] = symbol_data['high_price'].astype(float)
                symbol_data['low_price'] = symbol_data['low_price'].astype(float)

                # Calculate rolling statistics
                close_prices = symbol_data['close_price']
                rolling_mean = close_prices.rolling(window=self.parameters['lookback_period']).mean()
                rolling_std = close_prices.rolling(window=self.parameters['lookback_period']).std()

                # Calculate Z-score
                current_price = close_prices.iloc[-1]
                current_mean = rolling_mean.iloc[-1]
                current_std = rolling_std.iloc[-1]

                if current_std > 0:
                    z_score = (current_price - current_mean) / current_std
                else:
                    continue

                # Generate signals based on Z-score
                signal = None
                confidence = 0.0

                if z_score <= -self.parameters['z_score_entry']:
                    # Price is below mean - BUY signal
                    signal = SignalType.BUY
                    confidence = min(abs(z_score) / 3.0, 1.0)  # Higher Z-score = higher confidence

                elif z_score >= self.parameters['z_score_entry']:
                    # Price is above mean - SELL signal
                    signal = SignalType.SELL
                    confidence = min(abs(z_score) / 3.0, 1.0)

                # Only generate signal if confidence is sufficient
                if signal and confidence >= self.parameters['min_confidence']:

                    # Get additional data from analysis results
                    volatility = 0.02  # Default
                    if symbol in analysis_results and 'return_stats' in analysis_results[symbol]:
                        volatility = analysis_results[symbol]['return_stats'].get('std_return', 0.02)

                    # Calculate stop loss and take profit
                    atr = self._calculate_atr(symbol_data)
                    stop_loss_price = self._calculate_stop_loss(current_price, signal, atr)
                    take_profit_price = self._calculate_take_profit(current_price, stop_loss_price, signal)

                    trading_signal = TradingSignal(
                        symbol=symbol,
                        signal_type=signal,
                        confidence=confidence,
                        timestamp=datetime.now(),
                        price=current_price,
                        strategy_id=self.strategy_id,
                        metadata={
                            'z_score': z_score,
                            'rolling_mean': current_mean,
                            'rolling_std': current_std,
                            'volatility': volatility,
                            'stop_loss_price': stop_loss_price,
                            'take_profit_price': take_profit_price,
                            'atr': atr,
                            'lookback_period': self.parameters['lookback_period']
                        }
                    )

                    signals.append(trading_signal)
                    self.logger.info(f"Generated {signal.value} signal for {symbol} (Z-score: {z_score:.2f}, Confidence: {confidence:.2f})")

            except Exception as e:
                self.logger.error(f"Error generating signal for {symbol}: {e}")
                continue

        return signals

    async def calculate_position_size(self,
                                    signal: TradingSignal,
                                    portfolio_value: float,
                                    risk_budget: float) -> float:
        """Calculate position size based on volatility and confidence"""

        # Base position size from risk budget
        base_risk_amount = portfolio_value * risk_budget

        # Adjust for signal confidence
        confidence_adjusted_risk = base_risk_amount * signal.confidence

        # Calculate position size based on stop loss distance
        if signal.metadata and 'stop_loss_price' in signal.metadata:
            stop_loss_price = signal.metadata['stop_loss_price']
            risk_per_share = abs(signal.price - stop_loss_price)

            if risk_per_share > 0:
                position_size = confidence_adjusted_risk / risk_per_share
            else:
                position_size = 0
        else:
            # Fallback to volatility-based sizing
            volatility = signal.metadata.get('volatility', 0.02)
            position_size = confidence_adjusted_risk / (signal.price * volatility * 2)

        return max(0, position_size)

    async def should_exit_position(self,
                                 position: Position,
                                 current_data: Dict[str, Any]) -> bool:
        """Determine if position should be exited based on mean reversion logic"""

        try:
            # Get current market data for the symbol
            current_price = current_data.get('current_price', position.current_price)

            # Calculate current Z-score if we have enough data
            if 'rolling_mean' in current_data and 'rolling_std' in current_data:
                rolling_mean = current_data['rolling_mean']
                rolling_std = current_data['rolling_std']

                if rolling_std > 0:
                    z_score = (current_price - rolling_mean) / rolling_std

                    # Exit if Z-score has reverted (crossed back towards mean)
                    if abs(z_score) <= self.parameters['z_score_exit']:
                        self.logger.info(f"Exiting position for {position.symbol} - mean reversion complete (Z-score: {z_score:.2f})")
                        return True

            # Exit if position has been held too long
            days_held = (datetime.now() - position.entry_time).days
            if days_held >= self.parameters['max_holding_period']:
                self.logger.info(f"Exiting position for {position.symbol} - maximum holding period reached ({days_held} days)")
                return True

            # Exit if stop loss or take profit hit
            if position.position_type == 'long':
                if (position.stop_loss and current_price <= position.stop_loss) or \
                   (position.take_profit and current_price >= position.take_profit):
                    return True
            else:  # short position
                if (position.stop_loss and current_price >= position.stop_loss) or \
                   (position.take_profit and current_price <= position.take_profit):
                    return True

        except Exception as e:
            self.logger.error(f"Error checking exit condition for {position.symbol}: {e}")

        return False

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high = data['high_price']
            low = data['low_price']
            close = data['close_price']

            tr1 = high - low
            tr2 = np.abs(high - close.shift(1))
            tr3 = np.abs(low - close.shift(1))

            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return atr if not np.isnan(atr) else 0.02 * close.iloc[-1]
        except:
            return 0.02 * data['close_price'].iloc[-1]  # Fallback to 2% of price

    def _calculate_stop_loss(self, entry_price: float, signal: SignalType, atr: float) -> float:
        """Calculate stop loss based on ATR"""
        stop_distance = atr * 1.5  # 1.5x ATR stop

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    def _calculate_take_profit(self, entry_price: float, stop_loss: float, signal: SignalType) -> float:
        """Calculate take profit with 2:1 risk-reward ratio"""
        risk = abs(entry_price - stop_loss)
        reward = risk * 2.0  # 2:1 risk-reward

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price + reward
        else:
            return entry_price - reward

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        return self.parameters.copy()