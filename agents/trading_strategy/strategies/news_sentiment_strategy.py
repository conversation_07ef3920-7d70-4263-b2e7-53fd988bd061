"""
News Sentiment Trading Strategy
Generates trading signals based on news sentiment analysis for assets
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
import numpy as np

from shared.utils.base_strategy import BaseStrategy
from shared.types.strategy_types import TradingSignal, SignalType, StrategyType
from agents.data_collector.collectors.news_collector import NewsDataCollector
from config.deepseek_client import DeepSeekClient

class NewsSentimentStrategy(BaseStrategy):
    """Trading strategy based on news sentiment analysis"""

    def __init__(self, strategy_id: str, symbols: List[str]):
        default_params = {
            'sentiment_threshold_buy': 0.3,      # Positive sentiment threshold for buy
            'sentiment_threshold_sell': -0.3,    # Negative sentiment threshold for sell
            'relevance_threshold': 0.5,          # Minimum relevance score
            'news_lookback_hours': 24,           # Hours to look back for news
            'min_news_articles': 2,              # Minimum articles needed for signal
            'confidence_multiplier': 1.5,        # Boost confidence for news signals
            'ai_analysis_weight': 0.6,           # Weight for AI analysis vs TextBlob
            'volume_confirmation': True,         # Require volume confirmation
            'max_holding_period': 7              # days
        }

        super().__init__(
            strategy_id=strategy_id,
            strategy_type=StrategyType.NEWS_SENTIMENT,
            symbols=symbols,
            parameters=default_params
        )

        # Initialize news collector and AI client
        self.news_collector = NewsDataCollector()
        self.ai_client = DeepSeekClient()
        
        # News cache
        self.news_cache = {}
        self.last_news_update = {}

    async def generate_signals(self,
                             market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate trading signals based on news sentiment"""
        signals = []

        for symbol in self.symbols:
            try:
                # Get recent news for this symbol
                news_data = await self._get_symbol_news(symbol)
                
                if not news_data or len(news_data) < self.parameters['min_news_articles']:
                    continue

                # Analyze news sentiment
                sentiment_analysis = await self._analyze_news_sentiment(symbol, news_data)
                
                if not sentiment_analysis:
                    continue

                # Get current market data for the symbol
                symbol_data = market_data[market_data['symbol'] == symbol]
                if symbol_data.empty:
                    continue

                current_price = float(symbol_data.iloc[-1]['close_price'])
                
                # Generate signal based on sentiment
                signal = await self._generate_sentiment_signal(
                    symbol, current_price, sentiment_analysis, symbol_data
                )
                
                if signal:
                    signals.append(signal)
                    self.logger.info(f"Generated news sentiment signal for {symbol}: {signal.signal_type.value}")

            except Exception as e:
                self.logger.error(f"Error generating signal for {symbol}: {e}")
                continue

        return signals

    async def _get_symbol_news(self, symbol: str) -> List[Dict]:
        """Get recent news for a specific symbol"""
        try:
            # Check cache first
            cache_key = symbol
            now = datetime.now()
            
            if (cache_key in self.news_cache and 
                cache_key in self.last_news_update and
                (now - self.last_news_update[cache_key]).seconds < 3600):  # 1 hour cache
                return self.news_cache[cache_key]

            # Extract base symbol (remove /USDT, etc.)
            base_symbol = symbol.split('/')[0] if '/' in symbol else symbol
            
            # Search for news containing the symbol
            news_data = await self.news_collector.collect_financial_news(days_back=1, page_size=100)
            
            # Filter news relevant to this symbol
            symbol_news = []
            for news_item in news_data:
                # Check if symbol appears in title, content, or extracted symbols
                if (base_symbol.upper() in news_item.title.upper() or
                    base_symbol.upper() in news_item.content.upper() or
                    base_symbol in news_item.symbols):
                    
                    # Only include if relevance score is high enough
                    if news_item.relevance_score >= self.parameters['relevance_threshold']:
                        symbol_news.append({
                            'title': news_item.title,
                            'content': news_item.content,
                            'sentiment_score': news_item.sentiment_score,
                            'relevance_score': news_item.relevance_score,
                            'published_at': news_item.published_at,
                            'source': news_item.source,
                            'url': news_item.url
                        })

            # Cache results
            self.news_cache[cache_key] = symbol_news
            self.last_news_update[cache_key] = now
            
            self.logger.info(f"Found {len(symbol_news)} relevant news articles for {symbol}")
            return symbol_news

        except Exception as e:
            self.logger.error(f"Error getting news for {symbol}: {e}")
            return []

    async def _analyze_news_sentiment(self, symbol: str, news_data: List[Dict]) -> Optional[Dict]:
        """Analyze sentiment of news articles using both TextBlob and AI"""
        try:
            if not news_data:
                return None

            # Calculate TextBlob sentiment scores
            textblob_scores = [item['sentiment_score'] for item in news_data if item['sentiment_score'] is not None]
            textblob_avg = np.mean(textblob_scores) if textblob_scores else 0.0

            # Get AI sentiment analysis
            ai_analysis = await self.ai_client.analyze_news_sentiment(news_data)
            ai_sentiment_score = ai_analysis.get('sentiment_score', 0.0)

            # Combine scores with weighting
            ai_weight = self.parameters['ai_analysis_weight']
            textblob_weight = 1 - ai_weight
            
            combined_sentiment = (ai_sentiment_score * ai_weight) + (textblob_avg * textblob_weight)

            # Calculate confidence based on consistency and article count
            score_consistency = 1 - abs(ai_sentiment_score - textblob_avg)  # Higher if scores agree
            article_count_factor = min(len(news_data) / 10, 1.0)  # More articles = higher confidence
            
            confidence = (score_consistency * 0.6) + (article_count_factor * 0.4)

            analysis_result = {
                'combined_sentiment': combined_sentiment,
                'textblob_sentiment': textblob_avg,
                'ai_sentiment': ai_sentiment_score,
                'confidence': confidence,
                'article_count': len(news_data),
                'ai_analysis': ai_analysis,
                'recent_articles': news_data[:5]  # Keep top 5 for reference
            }

            self.logger.info(f"News sentiment for {symbol}: {combined_sentiment:.3f} (confidence: {confidence:.3f})")
            return analysis_result

        except Exception as e:
            self.logger.error(f"Error analyzing news sentiment for {symbol}: {e}")
            return None

    async def _generate_sentiment_signal(self, symbol: str, current_price: float, 
                                       sentiment_analysis: Dict, symbol_data: pd.DataFrame) -> Optional[TradingSignal]:
        """Generate trading signal based on sentiment analysis"""
        try:
            sentiment_score = sentiment_analysis['combined_sentiment']
            base_confidence = sentiment_analysis['confidence']
            
            signal_type = None
            confidence = 0.0

            # Determine signal type based on sentiment thresholds
            if sentiment_score >= self.parameters['sentiment_threshold_buy']:
                signal_type = SignalType.BUY
                # Scale confidence based on how far above threshold
                sentiment_strength = min((sentiment_score - self.parameters['sentiment_threshold_buy']) / 0.5, 1.0)
                confidence = base_confidence * sentiment_strength * self.parameters['confidence_multiplier']
                
            elif sentiment_score <= self.parameters['sentiment_threshold_sell']:
                signal_type = SignalType.SELL
                # Scale confidence based on how far below threshold
                sentiment_strength = min((self.parameters['sentiment_threshold_sell'] - sentiment_score) / 0.5, 1.0)
                confidence = base_confidence * sentiment_strength * self.parameters['confidence_multiplier']

            if not signal_type:
                return None

            # Volume confirmation if enabled
            if self.parameters['volume_confirmation']:
                recent_volume = symbol_data['volume'].tail(5).mean()
                avg_volume = symbol_data['volume'].mean()
                
                if recent_volume < avg_volume * 0.8:  # Low volume, reduce confidence
                    confidence *= 0.7

            # Cap confidence at 1.0
            confidence = min(confidence, 1.0)

            # Calculate stop loss and take profit based on volatility
            volatility = symbol_data['close_price'].pct_change().std()
            stop_loss_distance = max(volatility * 2, 0.02)  # At least 2%
            take_profit_distance = stop_loss_distance * 2

            if signal_type == SignalType.BUY:
                stop_loss_price = current_price * (1 - stop_loss_distance)
                take_profit_price = current_price * (1 + take_profit_distance)
            else:
                stop_loss_price = current_price * (1 + stop_loss_distance)
                take_profit_price = current_price * (1 - take_profit_distance)

            # Create trading signal
            trading_signal = TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                timestamp=datetime.now(),
                price=current_price,
                strategy_id=self.strategy_id,
                metadata={
                    'sentiment_score': sentiment_score,
                    'news_confidence': base_confidence,
                    'article_count': sentiment_analysis['article_count'],
                    'ai_sentiment': sentiment_analysis['ai_sentiment'],
                    'textblob_sentiment': sentiment_analysis['textblob_sentiment'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'volatility': volatility,
                    'news_summary': sentiment_analysis['ai_analysis'].get('summary', ''),
                    'key_themes': sentiment_analysis['ai_analysis'].get('key_themes', [])
                }
            )

            return trading_signal

        except Exception as e:
            self.logger.error(f"Error generating sentiment signal for {symbol}: {e}")
            return None

    async def calculate_position_size(self, signal: TradingSignal, 
                                    portfolio_value: float, risk_budget: float) -> float:
        """Calculate position size for news-based signals"""
        try:
            # Base position size from risk budget
            base_risk_amount = portfolio_value * risk_budget
            
            # Adjust for signal confidence and news quality
            news_confidence = signal.metadata.get('news_confidence', 0.5)
            article_count = signal.metadata.get('article_count', 1)
            
            # Boost size for high-confidence news with many articles
            confidence_multiplier = signal.confidence * news_confidence
            article_multiplier = min(article_count / 5, 1.2)  # Max 20% boost
            
            adjusted_risk = base_risk_amount * confidence_multiplier * article_multiplier
            
            # Calculate position size based on stop loss
            if 'stop_loss_price' in signal.metadata:
                stop_loss_price = signal.metadata['stop_loss_price']
                risk_per_share = abs(signal.price - stop_loss_price)
                
                if risk_per_share > 0:
                    position_size = adjusted_risk / risk_per_share
                else:
                    position_size = 0
            else:
                # Fallback to volatility-based sizing
                volatility = signal.metadata.get('volatility', 0.02)
                position_size = adjusted_risk / (signal.price * volatility * 2)

            return max(0, position_size)

        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 0

    async def should_exit_position(self, position, current_data: Dict[str, Any]) -> bool:
        """Determine if position should be closed based on new news"""
        try:
            # Get fresh news for the symbol
            fresh_news = await self._get_symbol_news(position.symbol)
            
            if fresh_news:
                # Analyze current sentiment
                current_sentiment = await self._analyze_news_sentiment(position.symbol, fresh_news)
                
                if current_sentiment:
                    sentiment_score = current_sentiment['combined_sentiment']
                    
                    # Exit if sentiment has reversed significantly
                    if position.position_type == 'long' and sentiment_score < -0.2:
                        self.logger.info(f"Exiting long position in {position.symbol} - sentiment turned negative: {sentiment_score:.3f}")
                        return True
                    elif position.position_type == 'short' and sentiment_score > 0.2:
                        self.logger.info(f"Exiting short position in {position.symbol} - sentiment turned positive: {sentiment_score:.3f}")
                        return True

            # Standard exit conditions (time-based)
            holding_days = (datetime.now() - position.entry_time).days
            if holding_days >= self.parameters['max_holding_period']:
                self.logger.info(f"Exiting position in {position.symbol} - max holding period reached")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking exit conditions for {position.symbol}: {e}")
            return False

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        return self.parameters.copy()
