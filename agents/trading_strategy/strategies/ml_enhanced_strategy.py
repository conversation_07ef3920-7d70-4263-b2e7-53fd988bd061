"""
ML-Enhanced Trading Strategy
Integrates LSTM, Transformer, and Ensemble models for advanced signal generation
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from shared.utils.base_strategy import BaseStrategy
from shared.types.strategy_types import TradingSignal, SignalType, StrategyType
from ml_models.model_manager import ModelManager
from ml_models.lstm_predictor import LSTMPredictor
from ml_models.transformer_predictor import TransformerPredictor
from ml_models.ensemble_predictor import EnsemblePredictor

class MLEnhancedStrategy(BaseStrategy):
    """Trading strategy enhanced with machine learning models"""

    def __init__(self, strategy_id: str, symbols: List[str]):
        default_params = {
            'prediction_horizon': 24,           # Hours to predict ahead
            'min_confidence_threshold': 0.6,    # Minimum ML confidence for signal
            'ensemble_weight': 0.5,             # Weight for ensemble vs individual models
            'lstm_weight': 0.3,                 # Weight for LSTM predictions
            'transformer_weight': 0.2,          # Weight for Transformer predictions
            'lookback_periods': 100,            # Historical periods for training
            'retrain_frequency': 168,           # Hours between model retraining
            'feature_importance_threshold': 0.1, # Minimum feature importance
            'volatility_adjustment': True,      # Adjust signals based on volatility
            'max_holding_period': 5             # days
        }

        super().__init__(
            strategy_id=strategy_id,
            strategy_type=StrategyType.STATISTICAL_ARBITRAGE,  # Using existing type
            symbols=symbols,
            parameters=default_params
        )

        # Initialize ML components
        self.model_manager = ModelManager()
        self.models = {}
        self.last_training = {}
        self.feature_cache = {}

    async def initialize_models(self):
        """Initialize ML models for each symbol"""
        try:
            for symbol in self.symbols:
                self.logger.info(f"Initializing ML models for {symbol}")
                
                # Initialize individual models
                lstm_model = LSTMPredictor(
                    model_id=f"lstm_{symbol}_{self.strategy_id}",
                    sequence_length=50,
                    prediction_horizon=self.parameters['prediction_horizon']
                )
                
                transformer_model = TransformerPredictor(
                    model_id=f"transformer_{symbol}_{self.strategy_id}",
                    sequence_length=100,
                    prediction_horizon=self.parameters['prediction_horizon']
                )
                
                ensemble_model = EnsemblePredictor(
                    model_id=f"ensemble_{symbol}_{self.strategy_id}",
                    base_models=[lstm_model, transformer_model]
                )

                self.models[symbol] = {
                    'lstm': lstm_model,
                    'transformer': transformer_model,
                    'ensemble': ensemble_model
                }
                
                # Register with model manager
                await self.model_manager.register_model(lstm_model)
                await self.model_manager.register_model(transformer_model)
                await self.model_manager.register_model(ensemble_model)

            self.logger.info(f"Initialized ML models for {len(self.symbols)} symbols")

        except Exception as e:
            self.logger.error(f"Error initializing ML models: {e}")

    async def generate_signals(self,
                             market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate ML-enhanced trading signals"""
        signals = []

        for symbol in self.symbols:
            try:
                # Get symbol-specific data
                symbol_data = market_data[market_data['symbol'] == symbol].copy()
                if len(symbol_data) < self.parameters['lookback_periods']:
                    continue

                # Check if models need retraining
                await self._check_and_retrain_models(symbol, symbol_data)

                # Generate ML predictions
                ml_predictions = await self._generate_ml_predictions(symbol, symbol_data)
                
                if not ml_predictions:
                    continue

                # Create trading signal based on ML predictions
                signal = await self._create_ml_signal(symbol, symbol_data, ml_predictions, analysis_results)
                
                if signal:
                    signals.append(signal)
                    self.logger.info(f"Generated ML signal for {symbol}: {signal.signal_type.value} (confidence: {signal.confidence:.3f})")

            except Exception as e:
                self.logger.error(f"Error generating ML signal for {symbol}: {e}")
                continue

        return signals

    async def _check_and_retrain_models(self, symbol: str, symbol_data: pd.DataFrame):
        """Check if models need retraining and retrain if necessary"""
        try:
            now = datetime.now()
            last_train = self.last_training.get(symbol, datetime.min)
            
            hours_since_training = (now - last_train).total_seconds() / 3600
            
            if hours_since_training >= self.parameters['retrain_frequency']:
                self.logger.info(f"Retraining models for {symbol}")
                
                # Prepare training data
                features, targets = self._prepare_training_data(symbol_data)
                
                if len(features) > 100:  # Minimum data requirement
                    # Retrain each model
                    for model_name, model in self.models[symbol].items():
                        try:
                            await model.train(features, targets)
                            self.logger.info(f"Retrained {model_name} model for {symbol}")
                        except Exception as e:
                            self.logger.error(f"Error retraining {model_name} for {symbol}: {e}")
                    
                    self.last_training[symbol] = now

        except Exception as e:
            self.logger.error(f"Error checking/retraining models for {symbol}: {e}")

    def _prepare_training_data(self, symbol_data: pd.DataFrame) -> tuple:
        """Prepare features and targets for ML training"""
        try:
            # Sort by timestamp
            data = symbol_data.sort_values('timestamp').copy()
            
            # Create features
            features = []
            targets = []
            
            # Technical indicators as features
            data['returns'] = data['close_price'].pct_change()
            data['sma_10'] = data['close_price'].rolling(10).mean()
            data['sma_30'] = data['close_price'].rolling(30).mean()
            data['rsi'] = self._calculate_rsi(data['close_price'], 14)
            data['volatility'] = data['returns'].rolling(20).std()
            data['volume_sma'] = data['volume'].rolling(10).mean()
            data['price_volume_trend'] = data['close_price'] * data['volume']
            
            # Create feature matrix
            feature_columns = ['returns', 'sma_10', 'sma_30', 'rsi', 'volatility', 'volume_sma', 'price_volume_trend']
            
            for i in range(50, len(data) - self.parameters['prediction_horizon']):
                # Features: last 50 periods
                feature_window = data.iloc[i-50:i][feature_columns].values.flatten()
                
                # Target: future price direction (1 for up, 0 for down)
                current_price = data.iloc[i]['close_price']
                future_price = data.iloc[i + self.parameters['prediction_horizon']]['close_price']
                target = 1 if future_price > current_price else 0
                
                if not np.isnan(feature_window).any():
                    features.append(feature_window)
                    targets.append(target)
            
            return np.array(features), np.array(targets)

        except Exception as e:
            self.logger.error(f"Error preparing training data: {e}")
            return np.array([]), np.array([])

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    async def _generate_ml_predictions(self, symbol: str, symbol_data: pd.DataFrame) -> Optional[Dict]:
        """Generate predictions from all ML models"""
        try:
            if symbol not in self.models:
                return None

            # Prepare current features
            features, _ = self._prepare_training_data(symbol_data)
            if len(features) == 0:
                return None

            current_features = features[-1:]  # Most recent feature set
            
            predictions = {}
            
            # Get predictions from each model
            for model_name, model in self.models[symbol].items():
                try:
                    prediction = await model.predict(current_features)
                    confidence = await model.get_prediction_confidence(current_features)
                    
                    predictions[model_name] = {
                        'prediction': prediction[0] if len(prediction) > 0 else 0.5,
                        'confidence': confidence
                    }
                    
                except Exception as e:
                    self.logger.error(f"Error getting prediction from {model_name}: {e}")
                    predictions[model_name] = {'prediction': 0.5, 'confidence': 0.0}

            # Calculate ensemble prediction
            ensemble_prediction = self._calculate_ensemble_prediction(predictions)
            
            return {
                'individual_predictions': predictions,
                'ensemble_prediction': ensemble_prediction,
                'timestamp': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"Error generating ML predictions for {symbol}: {e}")
            return None

    def _calculate_ensemble_prediction(self, predictions: Dict) -> Dict:
        """Calculate weighted ensemble prediction"""
        try:
            weights = {
                'lstm': self.parameters['lstm_weight'],
                'transformer': self.parameters['transformer_weight'],
                'ensemble': self.parameters['ensemble_weight']
            }
            
            weighted_prediction = 0.0
            weighted_confidence = 0.0
            total_weight = 0.0
            
            for model_name, pred_data in predictions.items():
                if model_name in weights:
                    weight = weights[model_name] * pred_data['confidence']
                    weighted_prediction += pred_data['prediction'] * weight
                    weighted_confidence += pred_data['confidence'] * weight
                    total_weight += weight
            
            if total_weight > 0:
                final_prediction = weighted_prediction / total_weight
                final_confidence = weighted_confidence / total_weight
            else:
                final_prediction = 0.5
                final_confidence = 0.0
            
            return {
                'prediction': final_prediction,
                'confidence': final_confidence,
                'signal_strength': abs(final_prediction - 0.5) * 2  # 0 to 1 scale
            }

        except Exception as e:
            self.logger.error(f"Error calculating ensemble prediction: {e}")
            return {'prediction': 0.5, 'confidence': 0.0, 'signal_strength': 0.0}

    async def _create_ml_signal(self, symbol: str, symbol_data: pd.DataFrame, 
                              ml_predictions: Dict, analysis_results: Dict) -> Optional[TradingSignal]:
        """Create trading signal based on ML predictions"""
        try:
            ensemble = ml_predictions['ensemble_prediction']
            prediction = ensemble['prediction']
            confidence = ensemble['confidence']
            signal_strength = ensemble['signal_strength']
            
            # Check minimum confidence threshold
            if confidence < self.parameters['min_confidence_threshold']:
                return None

            # Determine signal type
            if prediction > 0.6:  # Strong buy signal
                signal_type = SignalType.BUY
            elif prediction < 0.4:  # Strong sell signal
                signal_type = SignalType.SELL
            else:
                return None  # Neutral, no signal

            # Get current price
            current_price = float(symbol_data.iloc[-1]['close_price'])
            
            # Adjust confidence based on signal strength and volatility
            adjusted_confidence = confidence * signal_strength
            
            if self.parameters['volatility_adjustment']:
                volatility = symbol_data['close_price'].pct_change().std()
                volatility_factor = min(1.0, 0.02 / volatility) if volatility > 0 else 1.0
                adjusted_confidence *= volatility_factor

            # Calculate stop loss and take profit
            volatility = symbol_data['close_price'].pct_change().std()
            stop_loss_distance = max(volatility * 2.5, 0.02)
            take_profit_distance = stop_loss_distance * 2

            if signal_type == SignalType.BUY:
                stop_loss_price = current_price * (1 - stop_loss_distance)
                take_profit_price = current_price * (1 + take_profit_distance)
            else:
                stop_loss_price = current_price * (1 + stop_loss_distance)
                take_profit_price = current_price * (1 - take_profit_distance)

            # Create trading signal
            trading_signal = TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=min(adjusted_confidence, 1.0),
                timestamp=datetime.now(),
                price=current_price,
                strategy_id=self.strategy_id,
                metadata={
                    'ml_prediction': prediction,
                    'ml_confidence': confidence,
                    'signal_strength': signal_strength,
                    'individual_predictions': ml_predictions['individual_predictions'],
                    'stop_loss_price': stop_loss_price,
                    'take_profit_price': take_profit_price,
                    'volatility': volatility,
                    'prediction_horizon_hours': self.parameters['prediction_horizon']
                }
            )

            return trading_signal

        except Exception as e:
            self.logger.error(f"Error creating ML signal for {symbol}: {e}")
            return None

    async def calculate_position_size(self, signal: TradingSignal, 
                                    portfolio_value: float, risk_budget: float) -> float:
        """Calculate position size based on ML confidence"""
        try:
            base_risk_amount = portfolio_value * risk_budget
            
            # Adjust for ML confidence and signal strength
            ml_confidence = signal.metadata.get('ml_confidence', 0.5)
            signal_strength = signal.metadata.get('signal_strength', 0.5)
            
            confidence_multiplier = ml_confidence * signal_strength
            adjusted_risk = base_risk_amount * confidence_multiplier
            
            # Calculate position size based on stop loss
            if 'stop_loss_price' in signal.metadata:
                stop_loss_price = signal.metadata['stop_loss_price']
                risk_per_share = abs(signal.price - stop_loss_price)
                
                if risk_per_share > 0:
                    position_size = adjusted_risk / risk_per_share
                else:
                    position_size = 0
            else:
                volatility = signal.metadata.get('volatility', 0.02)
                position_size = adjusted_risk / (signal.price * volatility * 2)

            return max(0, position_size)

        except Exception as e:
            self.logger.error(f"Error calculating ML position size: {e}")
            return 0

    async def should_exit_position(self, position, current_data: Dict[str, Any]) -> bool:
        """ML-based exit decision"""
        try:
            # Get fresh ML predictions
            symbol_data = current_data.get('symbol_data')
            if symbol_data is not None and len(symbol_data) > 50:
                fresh_predictions = await self._generate_ml_predictions(position.symbol, symbol_data)
                
                if fresh_predictions:
                    prediction = fresh_predictions['ensemble_prediction']['prediction']
                    confidence = fresh_predictions['ensemble_prediction']['confidence']
                    
                    # Exit if ML prediction has reversed with high confidence
                    if (position.position_type == 'long' and prediction < 0.3 and confidence > 0.7):
                        self.logger.info(f"ML exit signal for long {position.symbol}: prediction={prediction:.3f}")
                        return True
                    elif (position.position_type == 'short' and prediction > 0.7 and confidence > 0.7):
                        self.logger.info(f"ML exit signal for short {position.symbol}: prediction={prediction:.3f}")
                        return True

            # Time-based exit
            holding_days = (datetime.now() - position.entry_time).days
            if holding_days >= self.parameters['max_holding_period']:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error in ML exit decision for {position.symbol}: {e}")
            return False

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        return self.parameters.copy()
