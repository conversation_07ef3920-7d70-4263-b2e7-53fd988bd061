"""
Data Agent for AstroA
Handles fetching and processing of cryptocurrency market data, news, and cross-asset data
"""
import asyncio
import aiohttp
import ccxt.async_support as ccxt
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import yfinance as yf
from newsapi import NewsApiClient

from agents.base_agent import BaseAgent, AgentResult
from config.settings import Config

class DataAgent(BaseAgent):
    """
    Agent responsible for fetching market data, news, and cross-asset correlations
    """

    def __init__(self):
        super().__init__(
            name="DataAgent",
            description="Fetches crypto prices, news sentiment, and cross-asset data"
        )
        self.exchanges = {}
        self.news_client = None
        self._init_exchanges()
        self._init_news_client()

    def _init_exchanges(self):
        """Initialize cryptocurrency exchanges"""
        try:
            # Initialize exchanges with API keys if available
            if Config.BINANCE_API_KEY and Config.BINANCE_SECRET_KEY:
                self.exchanges['binance'] = ccxt.binance({
                    'apiKey': Config.BINANCE_API_KEY,
                    'secret': Config.BINANCE_SECRET_KEY,
                    'sandbox': False,
                    'rateLimit': 1200,
                })
            else:
                # Use public endpoints only
                self.exchanges['binance'] = ccxt.binance({
                    'rateLimit': 1200,
                })

            # Add more exchanges (coinbasepro is deprecated, use coinbase instead)
            self.exchanges['coinbase'] = ccxt.coinbase({
                'rateLimit': 1000,
            })

            self.logger.info(f"Initialized {len(self.exchanges)} exchanges")

        except Exception as e:
            self.logger.error(f"Failed to initialize exchanges: {str(e)}")

    def _init_news_client(self):
        """Initialize news API client"""
        if Config.NEWS_API_KEY:
            try:
                self.news_client = NewsApiClient(api_key=Config.NEWS_API_KEY)
                self.logger.info("News API client initialized")
            except Exception as e:
                self.logger.error(f"Failed to initialize news client: {str(e)}")

    async def execute(self, symbols: List[str] = None, timeframes: List[str] = None) -> AgentResult:
        """
        Main execution method for data fetching

        Args:
            symbols: List of crypto symbols to fetch (e.g., ['BTC/USDT', 'ETH/USDT'])
            timeframes: List of timeframes (e.g., ['1h', '4h', '1d'])

        Returns:
            AgentResult with fetched data
        """
        symbols = symbols or ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'SOL/USDT', 'DOT/USDT']
        timeframes = timeframes or ['1h', '4h', '1d']

        try:
            # Fetch data concurrently
            tasks = [
                self.fetch_crypto_data(symbols, timeframes),
                self.fetch_news_data(symbols),
                self.fetch_cross_asset_data()
            ]

            crypto_data, news_data, cross_asset_data = await asyncio.gather(*tasks)

            # Analyze the data with DeepSeek AI
            ai_analysis = await self.analyze_market_conditions({
                'crypto_data': crypto_data,
                'news_data': news_data,
                'cross_asset_data': cross_asset_data
            })

            return AgentResult(
                agent_name=self.name,
                status='success',
                data={
                    'crypto_data': crypto_data,
                    'news_data': news_data,
                    'cross_asset_data': cross_asset_data,
                    'ai_analysis': ai_analysis,
                    'symbols': symbols,
                    'timeframes': timeframes
                },
                metadata={
                    'total_symbols': len(symbols),
                    'total_timeframes': len(timeframes),
                    'data_timestamp': datetime.now().isoformat()
                }
            )

        except Exception as e:
            return AgentResult(
                agent_name=self.name,
                status='failed',
                error_message=str(e)
            )

    async def fetch_crypto_data(self, symbols: List[str], timeframes: List[str]) -> Dict[str, Any]:
        """Fetch cryptocurrency OHLCV data"""
        crypto_data = {}

        for exchange_name, exchange in self.exchanges.items():
            exchange_data = {}

            for symbol in symbols:
                symbol_data = {}

                for timeframe in timeframes:
                    try:
                        # Fetch OHLCV data
                        ohlcv = await exchange.fetch_ohlcv(
                            symbol=symbol,
                            timeframe=timeframe,
                            limit=100
                        )

                        # Convert to DataFrame
                        df = pd.DataFrame(
                            ohlcv,
                            columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                        )
                        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

                        # Calculate basic indicators
                        df['price_change'] = df['close'].pct_change()
                        df['volume_ma'] = df['volume'].rolling(window=20).mean()
                        df['price_ma'] = df['close'].rolling(window=20).mean()

                        symbol_data[timeframe] = {
                            'ohlcv': df.to_dict('records'),
                            'latest_price': float(df['close'].iloc[-1]),
                            'price_change_24h': float(df['price_change'].iloc[-24:].sum()) if len(df) >= 24 else 0,
                            'volume_24h': float(df['volume'].iloc[-24:].sum()) if len(df) >= 24 else 0,
                        }

                    except Exception as e:
                        self.logger.error(f"Error fetching {symbol} {timeframe} from {exchange_name}: {str(e)}")
                        symbol_data[timeframe] = {'error': str(e)}

                exchange_data[symbol] = symbol_data

            crypto_data[exchange_name] = exchange_data

        # Close exchange connections
        for exchange in self.exchanges.values():
            await exchange.close()

        return crypto_data

    async def fetch_news_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Fetch cryptocurrency news data"""
        if not self.news_client:
            return {'error': 'News API not configured'}

        news_data = {}

        # Extract coin names from symbols
        coin_keywords = []
        for symbol in symbols:
            base = symbol.split('/')[0].lower()
            if base == 'btc':
                coin_keywords.extend(['bitcoin', 'btc'])
            elif base == 'eth':
                coin_keywords.extend(['ethereum', 'eth'])
            elif base == 'ada':
                coin_keywords.extend(['cardano', 'ada'])
            else:
                coin_keywords.append(base)

        try:
            # Fetch general crypto news
            crypto_news = self.news_client.get_everything(
                q='cryptocurrency OR bitcoin OR ethereum',
                language='en',
                sort_by='publishedAt',
                page_size=50
            )

            # Analyze news with AI
            if crypto_news['articles']:
                sentiment_analysis = await self.deepseek_client.analyze_news_sentiment(
                    crypto_news['articles'][:10]
                )

                news_data = {
                    'articles': crypto_news['articles'][:20],  # Limit to 20 articles
                    'total_results': crypto_news['totalResults'],
                    'sentiment_analysis': sentiment_analysis,
                    'keywords': coin_keywords
                }
            else:
                news_data = {'articles': [], 'total_results': 0}

        except Exception as e:
            self.logger.error(f"Error fetching news: {str(e)}")
            news_data = {'error': str(e)}

        return news_data

    async def fetch_cross_asset_data(self) -> Dict[str, Any]:
        """Fetch cross-asset data for correlation analysis"""
        cross_assets = {
            'gold': 'GC=F',
            'sp500': '^GSPC',
            'nasdaq': '^IXIC',
            'dxy': 'DX-Y.NYB',  # Dollar Index
            'vix': '^VIX',      # Volatility Index
            'oil': 'CL=F',      # Crude Oil
        }

        cross_asset_data = {}

        try:
            for asset_name, ticker in cross_assets.items():
                try:
                    # Fetch 30 days of data
                    data = yf.download(
                        ticker,
                        period='30d',
                        interval='1d',
                        progress=False
                    )

                    if not data.empty:
                        # Calculate basic metrics
                        latest_price = float(data['Close'].iloc[-1])
                        price_change = float(data['Close'].pct_change().iloc[-1])
                        volatility = float(data['Close'].pct_change().std())

                        cross_asset_data[asset_name] = {
                            'ticker': ticker,
                            'latest_price': latest_price,
                            'price_change_daily': price_change,
                            'volatility': volatility,
                            'data_points': len(data)
                        }
                    else:
                        cross_asset_data[asset_name] = {'error': 'No data available'}

                except Exception as e:
                    self.logger.error(f"Error fetching {asset_name}: {str(e)}")
                    cross_asset_data[asset_name] = {'error': str(e)}

        except Exception as e:
            self.logger.error(f"Error in cross-asset data fetch: {str(e)}")
            return {'error': str(e)}

        return cross_asset_data

    async def analyze_market_conditions(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze overall market conditions using DeepSeek AI"""
        system_prompt = """You are AstroA's market condition analyzer. Analyze the provided market data
        and assess current conditions, trends, and potential opportunities.

        Focus on:
        1. Overall market sentiment
        2. Cross-asset correlations
        3. Risk factors
        4. Trading opportunities
        5. Market structure analysis

        Return your analysis in structured JSON format."""

        # Prepare summary data for AI analysis
        summary_data = {
            'crypto_summary': self._summarize_crypto_data(data.get('crypto_data', {})),
            'news_summary': data.get('news_data', {}).get('sentiment_analysis', {}),
            'cross_asset_summary': data.get('cross_asset_data', {}),
            'timestamp': datetime.now().isoformat()
        }

        try:
            analysis = await self.analyze_with_ai(summary_data, 'market_condition_analysis')
            return analysis
        except Exception as e:
            return {'error': str(e), 'raw_data_available': True}

    def _summarize_crypto_data(self, crypto_data: Dict) -> Dict:
        """Create a summary of crypto data for AI analysis"""
        summary = {}

        for exchange, exchange_data in crypto_data.items():
            exchange_summary = {}

            for symbol, symbol_data in exchange_data.items():
                if '1d' in symbol_data and 'latest_price' in symbol_data['1d']:
                    exchange_summary[symbol] = {
                        'price': symbol_data['1d']['latest_price'],
                        'change_24h': symbol_data['1d'].get('price_change_24h', 0),
                        'volume_24h': symbol_data['1d'].get('volume_24h', 0)
                    }

            summary[exchange] = exchange_summary

        return summary