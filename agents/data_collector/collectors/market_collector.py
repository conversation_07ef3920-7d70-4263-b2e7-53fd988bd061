"""
Market Data Collector for Cryptocurrency and Traditional Markets
"""
import asyncio
import ccxt
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

from shared.types.agent_types import MarketData

class MarketDataCollector:
    """Collects market data from various sources"""

    def __init__(self):
        self.logger = logging.getLogger("market_collector")
        self.exchanges = self._initialize_exchanges()
        self.crypto_symbols = [
            'BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT',
            'LINK/USDT', 'SOL/USDT', 'MATIC/USDT', 'AVAX/USDT'
        ]
        self.stock_symbols = [
            'AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA',
            'META', 'AMZN', 'NFLX', 'SPY', 'QQQ'
        ]

    def _initialize_exchanges(self) -> Dict:
        """Initialize cryptocurrency exchanges"""
        exchanges = {}

        try:
            # Binance (most liquid crypto exchange)
            exchanges['binance'] = ccxt.binance({
                'apiKey': '',  # Add from .env if needed
                'secret': '',  # Add from .env if needed
                'sandbox': False,
                'rateLimit': 1200,  # Binance rate limit
            })

            # Coinbase Pro - Note: coinbasepro was renamed to coinbase
            try:
                exchanges['coinbase'] = ccxt.coinbase({
                    'apiKey': '',
                    'secret': '',
                    'passphrase': '',
                    'sandbox': False,
                    'rateLimit': 1000,
                })
            except AttributeError:
                # Fallback if coinbase is not available
                self.logger.warning("Coinbase exchange not available in this ccxt version")

        except Exception as e:
            self.logger.error(f"Error initializing exchanges: {e}")

        return exchanges

    async def collect_crypto_data(self, timeframe: str = '1h', limit: int = 100) -> List[MarketData]:
        """Collect cryptocurrency market data"""
        market_data = []

        for exchange_name, exchange in self.exchanges.items():
            for symbol in self.crypto_symbols:
                try:
                    # Fetch OHLCV data
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

                    for candle in ohlcv:
                        data = MarketData(
                            symbol=symbol.replace('/', ''),
                            timestamp=datetime.fromtimestamp(candle[0] / 1000),
                            open_price=float(candle[1]) if candle[1] is not None else 0.0,
                            high_price=float(candle[2]) if candle[2] is not None else 0.0,
                            low_price=float(candle[3]) if candle[3] is not None else 0.0,
                            close_price=float(candle[4]) if candle[4] is not None else 0.0,
                            volume=float(candle[5]) if candle[5] is not None else 0.0,
                            exchange=exchange_name,
                            timeframe=timeframe
                        )
                        market_data.append(data)

                    self.logger.info(f"Collected {len(ohlcv)} candles for {symbol} from {exchange_name}")

                except Exception as e:
                    self.logger.error(f"Error collecting {symbol} from {exchange_name}: {e}")
                    continue

        return market_data

    async def collect_stock_data(self, period: str = '1d', interval: str = '1h') -> List[MarketData]:
        """Collect traditional stock market data"""
        market_data = []

        for symbol in self.stock_symbols:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period=period, interval=interval)

                for timestamp, row in hist.iterrows():
                    data = MarketData(
                        symbol=symbol,
                        timestamp=timestamp.to_pydatetime(),
                        open_price=float(row['Open']),
                        high_price=float(row['High']),
                        low_price=float(row['Low']),
                        close_price=float(row['Close']),
                        volume=float(row['Volume']),
                        exchange='yahoo_finance',
                        timeframe=interval
                    )
                    market_data.append(data)

                self.logger.info(f"Collected {len(hist)} data points for {symbol}")

            except Exception as e:
                self.logger.error(f"Error collecting {symbol}: {e}")
                continue

        return market_data

    async def collect_all_market_data(self) -> List[MarketData]:
        """Collect data from all sources"""
        self.logger.info("Starting market data collection...")

        # Collect crypto and stock data concurrently
        crypto_task = asyncio.create_task(self.collect_crypto_data())
        stock_task = asyncio.create_task(self.collect_stock_data())

        crypto_data, stock_data = await asyncio.gather(crypto_task, stock_task)

        all_data = crypto_data + stock_data
        self.logger.info(f"Collected total {len(all_data)} market data points")

        return all_data