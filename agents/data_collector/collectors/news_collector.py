"""
News Data Collector for Market Sentiment Analysis
"""
import requests
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
import logging
from textblob import TextBlob

from shared.types.agent_types import NewsData

class NewsDataCollector:
    """Collects financial news from various sources"""

    def __init__(self, news_api_key: Optional[str] = None):
        self.logger = logging.getLogger("news_collector")
        self.news_api_key = news_api_key
        self.base_url = "https://newsapi.org/v2"

        # Financial keywords for relevance scoring
        self.financial_keywords = [
            'cryptocurrency', 'bitcoin', 'ethereum', 'trading', 'market',
            'stock', 'investment', 'finance', 'economy', 'monetary',
            'federal reserve', 'inflation', 'recession', 'bull market',
            'bear market', 'volatility', 'portfolio', 'dividend'
        ]

    def calculate_sentiment(self, text: str) -> float:
        """Calculate sentiment score using TextBlob"""
        try:
            blob = TextBlob(text)
            return blob.sentiment.polarity  # Returns -1 to 1
        except Exception as e:
            self.logger.error(f"Error calculating sentiment: {e}")
            return 0.0

    def calculate_relevance(self, text: str) -> float:
        """Calculate relevance score based on financial keywords"""
        text_lower = text.lower()
        matches = sum(1 for keyword in self.financial_keywords if keyword in text_lower)
        return min(matches / len(self.financial_keywords), 1.0)

    async def collect_financial_news(self, days_back: int = 1, page_size: int = 50) -> List[NewsData]:
        """Collect financial news from NewsAPI"""
        if not self.news_api_key:
            self.logger.warning("No NewsAPI key provided, skipping news collection")
            return []

        news_data = []
        from_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')

        queries = [
            'cryptocurrency AND trading',
            'bitcoin OR ethereum',
            'stock market',
            'financial markets',
            'trading signals'
        ]

        for query in queries:
            try:
                url = f"{self.base_url}/everything"
                params = {
                    'q': query,
                    'from': from_date,
                    'sortBy': 'publishedAt',
                    'pageSize': page_size,
                    'language': 'en',
                    'apiKey': self.news_api_key
                }

                response = requests.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                articles = data.get('articles', [])

                for article in articles:
                    title = article.get('title', '')
                    content = article.get('content', '') or article.get('description', '')

                    if not title or not content:
                        continue

                    # Calculate sentiment and relevance
                    full_text = f"{title} {content}"
                    sentiment_score = self.calculate_sentiment(full_text)
                    relevance_score = self.calculate_relevance(full_text)

                    # Extract potential symbols (simple pattern matching)
                    symbols = self._extract_symbols(full_text)

                    news_item = NewsData(
                        title=title,
                        content=content,
                        url=article.get('url', ''),
                        source=article.get('source', {}).get('name', 'Unknown'),
                        published_at=datetime.fromisoformat(
                            article.get('publishedAt', '').replace('Z', '+00:00')
                        ),
                        sentiment_score=sentiment_score,
                        relevance_score=relevance_score,
                        symbols=symbols
                    )

                    news_data.append(news_item)

                self.logger.info(f"Collected {len(articles)} articles for query: {query}")

            except Exception as e:
                self.logger.error(f"Error collecting news for query '{query}': {e}")
                continue

        # Remove duplicates based on URL
        unique_news = {item.url: item for item in news_data}.values()

        self.logger.info(f"Collected {len(unique_news)} unique news articles")
        return list(unique_news)

    def _extract_symbols(self, text: str) -> List[str]:
        """Extract potential trading symbols from text"""
        text_upper = text.upper()
        symbols = []

        # Common crypto symbols
        crypto_symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'SOL', 'MATIC', 'AVAX']
        # Common stock symbols
        stock_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'META', 'AMZN', 'NFLX']

        all_symbols = crypto_symbols + stock_symbols

        for symbol in all_symbols:
            if symbol in text_upper:
                symbols.append(symbol)

        return list(set(symbols))  # Remove duplicates