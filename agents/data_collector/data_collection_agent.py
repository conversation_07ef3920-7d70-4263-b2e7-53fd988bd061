"""
Data Collection Agent - Main coordinator for data gathering
"""
import asyncio
import psycopg2
from datetime import datetime
from typing import List, Dict, Any
import os
from dotenv import load_dotenv

from agents.base_agent import BaseAgent

# For type hints - create simple data classes if they don't exist
from typing import NamedTuple

class MarketData(NamedTuple):
    symbol: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    exchange: str
    timeframe: str

class NewsData(NamedTuple):
    title: str
    content: str
    timestamp: datetime
    source: str
    sentiment: float

# Try to import collectors, create dummy ones if they don't exist
try:
    from agents.data_collector.collectors.market_collector import MarketDataCollector
except ImportError:
    class MarketDataCollector:
        def __init__(self):
            pass
        async def collect_data(self, symbols, timeframes):
            return []

try:
    from agents.data_collector.collectors.news_collector import NewsDataCollector
except ImportError:
    class NewsDataCollector:
        def __init__(self, news_api_key=None):
            pass
        async def collect_news(self, symbols):
            return []

load_dotenv()

class DataCollectionAgent(BaseAgent):
    """Agent responsible for collecting market data and news"""

    def __init__(self, agent_id: str = "data_collector_001"):
        super().__init__(
            name=agent_id,
            description="Agent responsible for collecting market data and news"
        )

        # Initialize collectors
        self.market_collector = MarketDataCollector()
        self.news_collector = NewsDataCollector(
            news_api_key=os.getenv('NEWS_API_KEY')
        )

        # Database connection
        self.db_connection = None

        # Collection intervals (in seconds)
        self.market_data_interval = 3600  # 1 hour
        self.news_data_interval = 7200   # 2 hours

        # Last collection times
        self.last_market_collection = None
        self.last_news_collection = None

    async def initialize(self):
        """Initialize database connection and collectors"""
        self.logger.info("Initializing Data Collection Agent...")

        # Setup database connection
        try:
            self.db_connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'mathematical_trading'),
                user=os.getenv('DB_USER', 'trading_user'),
                password=os.getenv('DB_PASSWORD', 'hejhej')
            )
            self.logger.info("Database connection established")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise

        self.logger.info("Data Collection Agent initialized successfully")

    async def execute(self, *args, **kwargs):
        """Execute method required by BaseAgent"""
        from agents.base_agent import AgentResult

        try:
            await self.execute_main_logic()
            return AgentResult(
                agent_name=self.name,
                status='success',
                data={'message': 'Data collection completed successfully'}
            )
        except Exception as e:
            return AgentResult(
                agent_name=self.name,
                status='failed',
                error_message=str(e)
            )

    async def execute_main_logic(self):
        """Main execution logic for data collection"""
        current_time = datetime.now()

        # Check if it's time to collect market data
        if (self.last_market_collection is None or
            (current_time - self.last_market_collection).seconds >= self.market_data_interval):

            await self.collect_and_store_market_data()
            self.last_market_collection = current_time

        # Check if it's time to collect news data
        if (self.last_news_collection is None or
            (current_time - self.last_news_collection).seconds >= self.news_data_interval):

            await self.collect_and_store_news_data()
            self.last_news_collection = current_time

    async def collect_and_store_market_data(self):
        """Collect and store market data"""
        try:
            self.logger.info("Starting market data collection...")
            market_data = await self.market_collector.collect_all_market_data()

            if market_data:
                await self.store_market_data(market_data)

                # Notify other agents about new data
                await self.send_message(
                    receiver_id="mathematical_engine_001",
                    message_type=MessageType.DATA_RESPONSE,
                    payload={
                        "data_type": "market_data",
                        "count": len(market_data),
                        "timestamp": datetime.now().isoformat(),
                        "symbols": list(set([data.symbol for data in market_data]))
                    }
                )

                self.logger.info(f"Collected and stored {len(market_data)} market data points")

        except Exception as e:
            self.logger.error(f"Error in market data collection: {e}")

    async def collect_and_store_news_data(self):
        """Collect and store news data"""
        try:
            self.logger.info("Starting news data collection...")
            news_data = await self.news_collector.collect_financial_news()

            if news_data:
                await self.store_news_data(news_data)

                # Notify other agents about new news
                await self.send_message(
                    receiver_id="mathematical_engine_001",
                    message_type=MessageType.DATA_RESPONSE,
                    payload={
                        "data_type": "news_data",
                        "count": len(news_data),
                        "timestamp": datetime.now().isoformat(),
                        "average_sentiment": sum([n.sentiment_score or 0 for n in news_data]) / len(news_data)
                    }
                )

                self.logger.info(f"Collected and stored {len(news_data)} news articles")

        except Exception as e:
            self.logger.error(f"Error in news data collection: {e}")

    async def store_market_data(self, market_data: List[MarketData]):
        """Store market data in database"""
        cursor = self.db_connection.cursor()

        try:
            for data in market_data:
                cursor.execute("""
                    INSERT INTO market_data
                    (symbol, timestamp, open_price, high_price, low_price, close_price, volume, exchange, timeframe)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (symbol, timestamp, exchange, timeframe) DO NOTHING
                """, (
                    data.symbol, data.timestamp, data.open_price, data.high_price,
                    data.low_price, data.close_price, data.volume, data.exchange, data.timeframe
                ))

            self.db_connection.commit()
            self.logger.info(f"Stored {len(market_data)} market data records")

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error storing market data: {e}")
            raise
        finally:
            cursor.close()

    async def store_news_data(self, news_data: List[NewsData]):
        """Store news data in database"""
        cursor = self.db_connection.cursor()

        try:
            for data in news_data:
                # Convert symbols list to JSON string
                symbols_json = ','.join(data.symbols) if data.symbols else None

                cursor.execute("""
                    INSERT INTO news_data
                    (title, content, url, source, published_at, sentiment_score, relevance_score, symbol)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (url) DO NOTHING
                """, (
                    data.title, data.content, data.url, data.source,
                    data.published_at, data.sentiment_score, data.relevance_score, symbols_json
                ))

            self.db_connection.commit()
            self.logger.info(f"Stored {len(news_data)} news records")

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Error storing news data: {e}")
            raise
        finally:
            cursor.close()

    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            self.db_connection.close()
            self.logger.info("Database connection closed")