"""
Ensemble Predictor combining multiple ML algorithms
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import Random<PERSON><PERSON>tRegressor, GradientBoostingRegressor, VotingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from typing import Dict, List, Tuple, Any, Optional
import joblib
import asyncio

from .base_model import BaseMLModel
from .lstm_predictor import LSTMPredictor

class EnsemblePredictor(BaseMLModel):
    """Ensemble model combining multiple prediction algorithms"""

    def __init__(self, model_id: str):
        super().__init__(model_id, "Ensemble", "1.0")

        self.scaler = StandardScaler()
        self.individual_models = {}
        self.ensemble_weights = {}

        # Initialize base models
        self._initialize_base_models()

        # Ensemble hyperparameters
        self.hyperparameters = {
            'voting_strategy': 'weighted',  # 'uniform' or 'weighted'
            'weight_calculation': 'performance',  # 'performance' or 'equal'
            'min_models_for_prediction': 2,
            'retrain_threshold': 0.1,  # Retrain if performance drops by 10%
            'lstm_enabled': True,
            'feature_engineering': True
        }

        self.metadata['hyperparameters'] = self.hyperparameters

    def _initialize_base_models(self):
        """Initialize all base models in the ensemble"""
        # Traditional ML models
        self.individual_models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'svr': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale'
            ),
            'ridge': Ridge(
                alpha=1.0,
                random_state=42
            ),
            'linear': LinearRegression()
        }

        # Add LSTM if enabled
        if self.hyperparameters['lstm_enabled']:
            self.individual_models['lstm'] = LSTMPredictor(
                model_id=f"{self.model_id}_lstm",
                sequence_length=30,
                prediction_horizon=1
            )

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train all models in the ensemble"""
        try:
            self.logger.info(f"Starting ensemble training with {len(training_data)} samples")

            # Update hyperparameters
            self.hyperparameters.update(kwargs)

            # Feature engineering
            engineered_data = self._engineer_features(training_data)

            # Prepare training data
            X_train, y_train = self._prepare_ensemble_data(engineered_data, is_training=True)

            if X_train is None or len(X_train) == 0:
                raise ValueError("Insufficient training data")

            # Prepare validation data
            X_val, y_val = None, None
            if validation_data is not None:
                val_engineered = self._engineer_features(validation_data)
                X_val, y_val = self._prepare_ensemble_data(val_engineered, is_training=False)

            # Train each model
            model_performances = {}
            trained_models = {}

            for model_name, model in self.individual_models.items():
                try:
                    self.logger.info(f"Training {model_name}")

                    if model_name == 'lstm':
                        # Train LSTM with original data format
                        result = await model.train(engineered_data, validation_data)
                        if result['status'] == 'success':
                            trained_models[model_name] = model
                            model_performances[model_name] = result['metrics']
                    else:
                        # Train sklearn models
                        model.fit(X_train, y_train.ravel())
                        trained_models[model_name] = model

                        # Calculate performance
                        train_pred = model.predict(X_train)
                        train_mae = mean_absolute_error(y_train, train_pred)
                        train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
                        train_r2 = r2_score(y_train, train_pred)

                        performance = {
                            'train_mae': train_mae,
                            'train_rmse': train_rmse,
                            'train_r2': train_r2
                        }

                        if X_val is not None:
                            val_pred = model.predict(X_val)
                            val_mae = mean_absolute_error(y_val, val_pred)
                            val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
                            val_r2 = r2_score(y_val, val_pred)

                            performance.update({
                                'val_mae': val_mae,
                                'val_rmse': val_rmse,
                                'val_r2': val_r2
                            })

                        model_performances[model_name] = performance

                except Exception as e:
                    self.logger.error(f"Error training {model_name}: {e}")
                    continue

            # Update trained models
            self.individual_models = trained_models

            # Calculate ensemble weights
            self._calculate_ensemble_weights(model_performances)

            # Create voting regressor for sklearn models
            self._create_voting_regressor()

            self.is_trained = True

            # Calculate overall performance
            overall_metrics = self._calculate_ensemble_performance(
                X_train, y_train, X_val, y_val, engineered_data, validation_data
            )

            self.update_performance_metrics(overall_metrics)

            # Update metadata
            self.metadata['training_samples'] = len(X_train)
            self.metadata['last_updated'] = pd.Timestamp.now()
            self.metadata['active_models'] = list(self.individual_models.keys())

            self.logger.info(f"Ensemble training completed with {len(self.individual_models)} models")

            return {
                'status': 'success',
                'metrics': overall_metrics,
                'training_samples': len(X_train),
                'active_models': len(self.individual_models),
                'model_performances': model_performances
            }

        except Exception as e:
            self.logger.error(f"Error during ensemble training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict(self,
                     input_data: pd.DataFrame,
                     return_individual: bool = False,
                     **kwargs) -> np.ndarray:
        """Make ensemble predictions"""
        try:
            if not self.is_trained:
                raise ValueError("Ensemble must be trained before making predictions")

            # Feature engineering
            engineered_data = self._engineer_features(input_data)

            # Prepare data for sklearn models
            X_pred = self._prepare_prediction_data(engineered_data)

            individual_predictions = {}

            # Get predictions from each model
            for model_name, model in self.individual_models.items():
                try:
                    if model_name == 'lstm':
                        # LSTM prediction
                        pred = await model.predict(engineered_data)
                    else:
                        # Sklearn model prediction
                        pred = model.predict(X_pred)

                    individual_predictions[model_name] = pred

                except Exception as e:
                    self.logger.error(f"Error getting prediction from {model_name}: {e}")
                    continue

            if not individual_predictions:
                raise ValueError("No models produced valid predictions")

            # Calculate ensemble prediction
            ensemble_pred = self._combine_predictions(individual_predictions)

            if return_individual:
                return ensemble_pred, individual_predictions
            else:
                return ensemble_pred

        except Exception as e:
            self.logger.error(f"Error during ensemble prediction: {e}")
            return np.array([])

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate ensemble performance"""
        try:
            # Feature engineering
            engineered_data = self._engineer_features(test_data)

            # Prepare test data
            X_test, y_test = self._prepare_ensemble_data(engineered_data, is_training=False)

            if X_test is None or len(X_test) == 0:
                return {'error': 'Insufficient test data'}

            # Get ensemble predictions
            predictions = await self.predict(engineered_data)

            if len(predictions) == 0:
                return {'error': 'No predictions generated'}

            # Calculate metrics
            mae = mean_absolute_error(y_test, predictions)
            rmse = np.sqrt(mean_squared_error(y_test, predictions))
            r2 = r2_score(y_test, predictions)

            # Calculate individual model performance
            individual_metrics = {}
            for model_name, model in self.individual_models.items():
                try:
                    if model_name == 'lstm':
                        model_pred = await model.predict(engineered_data)
                    else:
                        model_pred = model.predict(X_test)

                    model_mae = mean_absolute_error(y_test, model_pred)
                    individual_metrics[f"{model_name}_mae"] = model_mae

                except Exception as e:
                    self.logger.error(f"Error evaluating {model_name}: {e}")

            metrics = {
                'ensemble_mae': mae,
                'ensemble_rmse': rmse,
                'ensemble_r2': r2,
                **individual_metrics
            }

            self.logger.info(f"Ensemble evaluation - MAE: {mae:.4f}, RMSE: {rmse:.4f}, R2: {r2:.4f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}

    def _engineer_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Engineer additional features for better predictions"""
        if not self.hyperparameters['feature_engineering']:
            return data.copy()

        try:
            engineered = data.copy()

            # Technical indicators
            if 'close_price' in engineered.columns:
                close_prices = engineered['close_price']

                # Moving averages
                engineered['ma_5'] = close_prices.rolling(5).mean()
                engineered['ma_10'] = close_prices.rolling(10).mean()
                engineered['ma_20'] = close_prices.rolling(20).mean()

                # Price ratios
                engineered['price_ma5_ratio'] = close_prices / engineered['ma_5']
                engineered['price_ma20_ratio'] = close_prices / engineered['ma_20']

                # Volatility
                engineered['volatility_5'] = close_prices.rolling(5).std()
                engineered['volatility_20'] = close_prices.rolling(20).std()

                # Momentum
                engineered['momentum_5'] = close_prices.pct_change(5)
                engineered['momentum_10'] = close_prices.pct_change(10)

                # RSI-like indicator
                delta = close_prices.diff()
                gain = delta.where(delta > 0, 0).rolling(14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
                rs = gain / loss
                engineered['rsi'] = 100 - (100 / (1 + rs))

            # Volume indicators
            if 'volume' in engineered.columns:
                volume = engineered['volume']
                engineered['volume_ma_5'] = volume.rolling(5).mean()
                engineered['volume_ratio'] = volume / engineered['volume_ma_5']

            # Price patterns
            if all(col in engineered.columns for col in ['open_price', 'high_price', 'low_price', 'close_price']):
                # Candlestick patterns
                engineered['body_size'] = abs(engineered['close_price'] - engineered['open_price'])
                engineered['upper_shadow'] = engineered['high_price'] - engineered[['open_price', 'close_price']].max(axis=1)
                engineered['lower_shadow'] = engineered[['open_price', 'close_price']].min(axis=1) - engineered['low_price']

            # Fill NaN values
            engineered = engineered.fillna(method='bfill').fillna(method='ffill')

            return engineered

        except Exception as e:
            self.logger.error(f"Error in feature engineering: {e}")
            return data.copy()

    def _prepare_ensemble_data(self, data: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for ensemble training/prediction"""
        try:
            # Define feature columns (excluding target)
            exclude_cols = ['timestamp', 'symbol', 'exchange', 'timeframe']
            feature_cols = [col for col in data.columns if col not in exclude_cols]

            # Target is close_price
            if 'close_price' not in data.columns:
                self.logger.error("close_price column required for training")
                return None, None

            # Separate features and target
            X = data[feature_cols].drop('close_price', axis=1, errors='ignore')
            y = data['close_price'].values.reshape(-1, 1)

            if is_training:
                # Fit scaler
                X_scaled = self.scaler.fit_transform(X)
                self.feature_columns = X.columns.tolist()
            else:
                # Use fitted scaler
                X_scaled = self.scaler.transform(X[self.feature_columns])

            return X_scaled, y

        except Exception as e:
            self.logger.error(f"Error preparing ensemble data: {e}")
            return None, None

    def _prepare_prediction_data(self, data: pd.DataFrame) -> np.ndarray:
        """Prepare data for prediction"""
        try:
            # Use only training features
            X = data[self.feature_columns]
            return self.scaler.transform(X)

        except Exception as e:
            self.logger.error(f"Error preparing prediction data: {e}")
            return np.array([])

    def _calculate_ensemble_weights(self, model_performances: Dict[str, Dict]):
        """Calculate weights for ensemble voting"""
        if self.hyperparameters['weight_calculation'] == 'equal':
            # Equal weights
            n_models = len(model_performances)
            self.ensemble_weights = {model: 1.0/n_models for model in model_performances.keys()}
        else:
            # Performance-based weights
            weights = {}
            total_score = 0

            for model_name, metrics in model_performances.items():
                # Use validation MAE if available, otherwise training MAE
                mae = metrics.get('val_mae', metrics.get('train_mae', 1.0))
                # Convert MAE to score (lower is better)
                score = 1.0 / (1.0 + mae)
                weights[model_name] = score
                total_score += score

            # Normalize weights
            self.ensemble_weights = {model: weight/total_score for model, weight in weights.items()}

        self.logger.info(f"Ensemble weights: {self.ensemble_weights}")

    def _combine_predictions(self, individual_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Combine individual model predictions into ensemble prediction"""
        try:
            if not individual_predictions:
                return np.array([])

            # Get predictions and weights for available models
            predictions = []
            weights = []

            for model_name, pred in individual_predictions.items():
                if model_name in self.ensemble_weights:
                    predictions.append(pred)
                    weights.append(self.ensemble_weights[model_name])

            if not predictions:
                return np.array([])

            # Stack predictions
            pred_array = np.column_stack(predictions)
            weight_array = np.array(weights)

            # Weighted average
            if self.hyperparameters['voting_strategy'] == 'weighted':
                ensemble_pred = np.average(pred_array, axis=1, weights=weight_array)
            else:
                # Uniform average
                ensemble_pred = np.mean(pred_array, axis=1)

            return ensemble_pred

        except Exception as e:
            self.logger.error(f"Error combining predictions: {e}")
            return np.array([])

    def _create_voting_regressor(self):
        """Create sklearn VotingRegressor for traditional ML models"""
        try:
            sklearn_models = [(name, model) for name, model in self.individual_models.items()
                            if name != 'lstm']

            if len(sklearn_models) >= 2:
                self.voting_regressor = VotingRegressor(
                    estimators=sklearn_models,
                    weights=[self.ensemble_weights.get(name, 1.0) for name, _ in sklearn_models]
                )
            else:
                self.voting_regressor = None

        except Exception as e:
            self.logger.error(f"Error creating voting regressor: {e}")
            self.voting_regressor = None

    def _calculate_ensemble_performance(self, X_train, y_train, X_val, y_val,
                                      engineered_train, engineered_val) -> Dict[str, float]:
        """Calculate overall ensemble performance"""
        try:
            metrics = {}

            # Training performance
            train_pred = asyncio.run(self.predict(engineered_train))
            if len(train_pred) > 0:
                train_mae = mean_absolute_error(y_train, train_pred)
                train_rmse = np.sqrt(mean_squared_error(y_train, train_pred))
                metrics.update({
                    'ensemble_train_mae': train_mae,
                    'ensemble_train_rmse': train_rmse
                })

            # Validation performance
            if X_val is not None and engineered_val is not None:
                val_pred = asyncio.run(self.predict(engineered_val))
                if len(val_pred) > 0:
                    val_mae = mean_absolute_error(y_val, val_pred)
                    val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
                    metrics.update({
                        'ensemble_val_mae': val_mae,
                        'ensemble_val_rmse': val_rmse
                    })

            return metrics

        except Exception as e:
            self.logger.error(f"Error calculating ensemble performance: {e}")
            return {}

    def get_model_contributions(self, input_data: pd.DataFrame) -> Dict[str, float]:
        """Get individual model contributions to final prediction"""
        try:
            predictions, individual = asyncio.run(self.predict(input_data, return_individual=True))

            contributions = {}
            for model_name, pred in individual.items():
                weight = self.ensemble_weights.get(model_name, 0)
                contribution = weight * pred[0] if len(pred) > 0 else 0
                contributions[model_name] = contribution

            return contributions

        except Exception as e:
            self.logger.error(f"Error calculating model contributions: {e}")
            return {}

    def get_ensemble_info(self) -> Dict[str, Any]:
        """Get comprehensive ensemble information"""
        return {
            **self.get_model_info(),
            'ensemble_weights': self.ensemble_weights,
            'active_models': list(self.individual_models.keys()),
            'voting_strategy': self.hyperparameters['voting_strategy']
        }