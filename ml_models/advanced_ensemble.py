"""
Advanced Ensemble Methods for Trading Strategy Combination
Implements sophisticated ensemble techniques including stacking, blending, and dynamic weighting
"""

import numpy as np
import pandas as pd
import asyncio
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.neural_network import MLPRegressor
import xgboost as xgb
import lightgbm as lgb
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
from dataclasses import dataclass
import joblib
import warnings
warnings.filterwarnings('ignore')

from .base_model import BaseMLModel
from .lstm_predictor import LSTMPredictor
from .transformer_predictor import TransformerPredictor

@dataclass
class ModelPerformance:
    model_name: str
    mae: float
    rmse: float
    r2: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    confidence: float

class AdvancedEnsemble(BaseMLModel):
    """Advanced ensemble methods for combining multiple prediction models"""

    def __init__(self, model_id: str, ensemble_method: str = "stacking"):
        super().__init__(model_id, "AdvancedEnsemble", "1.0")

        self.ensemble_method = ensemble_method  # 'stacking', 'blending', 'dynamic', 'bayesian'
        self.base_models = {}
        self.meta_model = None
        self.model_performances = {}
        self.dynamic_weights = {}
        self.scaler = RobustScaler()

        # Initialize scalers for different model types
        self.traditional_scaler = StandardScaler()
        self.neural_scaler = RobustScaler()

        # Ensemble hyperparameters
        self.hyperparameters = {
            'ensemble_method': ensemble_method,
            'meta_model_type': 'ridge',  # 'ridge', 'xgboost', 'neural'
            'cv_folds': 5,
            'blend_ratio': 0.8,  # For blending method
            'performance_window': 100,  # For dynamic weighting
            'confidence_threshold': 0.6,
            'diversity_penalty': 0.1,
            'min_correlation_threshold': 0.3,
            'rebalance_frequency': 50,  # How often to update weights
            'enable_deep_models': True,
            'enable_traditional_models': True
        }

        self.metadata['hyperparameters'] = self.hyperparameters
        self._initialize_base_models()

    def _initialize_base_models(self):
        """Initialize diverse set of base models"""

        # Traditional ML models
        if self.hyperparameters['enable_traditional_models']:
            self.base_models.update({
                'xgboost': xgb.XGBRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                ),
                'lightgbm': lgb.LGBMRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42,
                    verbose=-1
                ),
                'random_forest': RandomForestRegressor(
                    n_estimators=200,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                    n_jobs=-1
                ),
                'gradient_boosting': GradientBoostingRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    random_state=42
                ),
                'svr_rbf': SVR(
                    kernel='rbf',
                    C=1.0,
                    gamma='scale',
                    epsilon=0.01
                ),
                'svr_linear': SVR(
                    kernel='linear',
                    C=1.0,
                    epsilon=0.01
                ),
                'ridge': Ridge(
                    alpha=1.0,
                    random_state=42
                ),
                'lasso': Lasso(
                    alpha=0.1,
                    random_state=42,
                    max_iter=2000
                ),
                'elastic_net': ElasticNet(
                    alpha=0.1,
                    l1_ratio=0.5,
                    random_state=42,
                    max_iter=2000
                ),
                'mlp': MLPRegressor(
                    hidden_layer_sizes=(100, 50),
                    activation='relu',
                    solver='adam',
                    alpha=0.001,
                    learning_rate='adaptive',
                    random_state=42,
                    max_iter=500
                )
            })

        # Deep learning models
        if self.hyperparameters['enable_deep_models']:
            self.base_models.update({
                'lstm': LSTMPredictor(
                    model_id=f"{self.model_id}_lstm",
                    sequence_length=60,
                    prediction_horizon=1
                ),
                'transformer': TransformerPredictor(
                    model_id=f"{self.model_id}_transformer",
                    sequence_length=100,
                    prediction_horizon=1
                )
            })

        # Initialize meta-model
        self._initialize_meta_model()

    def _initialize_meta_model(self):
        """Initialize the meta-model for stacking"""
        meta_model_type = self.hyperparameters['meta_model_type']

        if meta_model_type == 'ridge':
            self.meta_model = Ridge(alpha=1.0, random_state=42)
        elif meta_model_type == 'xgboost':
            self.meta_model = xgb.XGBRegressor(
                n_estimators=50,
                max_depth=3,
                learning_rate=0.1,
                random_state=42
            )
        elif meta_model_type == 'neural':
            self.meta_model = MLPRegressor(
                hidden_layer_sizes=(50, 25),
                activation='relu',
                solver='adam',
                alpha=0.001,
                random_state=42,
                max_iter=300
            )

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the ensemble using the specified method"""
        try:
            self.logger.info(f"Starting advanced ensemble training with {len(training_data)} samples")

            # Update hyperparameters
            self.hyperparameters.update(kwargs)

            if self.ensemble_method == "stacking":
                return await self._train_stacking(training_data, validation_data)
            elif self.ensemble_method == "blending":
                return await self._train_blending(training_data, validation_data)
            elif self.ensemble_method == "dynamic":
                return await self._train_dynamic_weighting(training_data, validation_data)
            elif self.ensemble_method == "bayesian":
                return await self._train_bayesian_ensemble(training_data, validation_data)
            else:
                raise ValueError(f"Unknown ensemble method: {self.ensemble_method}")

        except Exception as e:
            self.logger.error(f"Error during ensemble training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def _train_stacking(self,
                             training_data: pd.DataFrame,
                             validation_data: pd.DataFrame = None) -> Dict[str, Any]:
        """Train ensemble using stacking method"""
        self.logger.info("Training ensemble using stacking method")

        # Feature engineering
        engineered_data = self._engineer_advanced_features(training_data)

        # Prepare data for traditional models
        X_traditional, y = self._prepare_traditional_data(engineered_data)
        X_traditional_scaled = self.traditional_scaler.fit_transform(X_traditional)

        # Cross-validation for base model predictions
        cv = KFold(n_splits=self.hyperparameters['cv_folds'], shuffle=True, random_state=42)

        base_predictions = np.zeros((len(X_traditional), len(self.base_models)))
        model_scores = {}

        # Train traditional models with cross-validation
        traditional_models = {k: v for k, v in self.base_models.items()
                            if k not in ['lstm', 'transformer']}

        for idx, (model_name, model) in enumerate(traditional_models.items()):
            self.logger.info(f"Training base model: {model_name}")

            cv_predictions = np.zeros(len(X_traditional))
            cv_scores = []

            for train_idx, val_idx in cv.split(X_traditional):
                X_fold_train, X_fold_val = X_traditional_scaled[train_idx], X_traditional_scaled[val_idx]
                y_fold_train, y_fold_val = y[train_idx], y[val_idx]

                # Train model on fold
                model.fit(X_fold_train, y_fold_train)

                # Predict on validation fold
                fold_predictions = model.predict(X_fold_val)
                cv_predictions[val_idx] = fold_predictions

                # Calculate fold score
                fold_score = r2_score(y_fold_val, fold_predictions)
                cv_scores.append(fold_score)

            base_predictions[:, idx] = cv_predictions
            model_scores[model_name] = np.mean(cv_scores)

            # Retrain on full data
            model.fit(X_traditional_scaled, y)

        # Handle deep learning models separately
        deep_model_predictions = {}

        if 'lstm' in self.base_models:
            self.logger.info("Training LSTM model")
            lstm_result = await self.base_models['lstm'].train(engineered_data, validation_data)
            if lstm_result['status'] == 'success':
                lstm_predictions = await self.base_models['lstm'].predict(engineered_data)
                # Align predictions with traditional model predictions
                if len(lstm_predictions) >= len(base_predictions):
                    deep_model_predictions['lstm'] = lstm_predictions[-len(base_predictions):]
                model_scores['lstm'] = lstm_result['metrics'].get('train_mae', 0.1)

        if 'transformer' in self.base_models:
            self.logger.info("Training Transformer model")
            transformer_result = await self.base_models['transformer'].train(engineered_data, validation_data)
            if transformer_result['status'] == 'success':
                transformer_predictions = await self.base_models['transformer'].predict(engineered_data)
                # Align predictions with traditional model predictions
                if len(transformer_predictions) >= len(base_predictions):
                    deep_model_predictions['transformer'] = transformer_predictions[-len(base_predictions):]
                model_scores['transformer'] = transformer_result['metrics'].get('train_mae', 0.1)

        # Combine all base predictions
        all_predictions = base_predictions.copy()

        # Add deep learning predictions if available
        for model_name, predictions in deep_model_predictions.items():
            if len(predictions) == len(all_predictions):
                all_predictions = np.column_stack([all_predictions, predictions])

        # Train meta-model
        self.logger.info("Training meta-model")
        self.meta_model.fit(all_predictions, y)

        # Calculate ensemble performance
        ensemble_predictions = self.meta_model.predict(all_predictions)
        ensemble_mae = mean_absolute_error(y, ensemble_predictions)
        ensemble_rmse = np.sqrt(mean_squared_error(y, ensemble_predictions))
        ensemble_r2 = r2_score(y, ensemble_predictions)

        self.is_trained = True
        self.model_performances = model_scores

        metrics = {
            'ensemble_mae': ensemble_mae,
            'ensemble_rmse': ensemble_rmse,
            'ensemble_r2': ensemble_r2,
            'base_model_scores': model_scores,
            'meta_model_type': self.hyperparameters['meta_model_type']
        }

        self.update_performance_metrics(metrics)

        self.logger.info(f"Stacking ensemble training completed. "
                        f"Ensemble MAE: {ensemble_mae:.4f}, R²: {ensemble_r2:.4f}")

        return {
            'status': 'success',
            'metrics': metrics,
            'training_samples': len(X_traditional),
            'ensemble_method': 'stacking'
        }

    async def _train_blending(self,
                             training_data: pd.DataFrame,
                             validation_data: pd.DataFrame = None) -> Dict[str, Any]:
        """Train ensemble using blending method"""
        self.logger.info("Training ensemble using blending method")

        blend_ratio = self.hyperparameters['blend_ratio']

        # Split data for blending
        split_idx = int(len(training_data) * blend_ratio)
        blend_train = training_data.iloc[:split_idx].copy()
        blend_holdout = training_data.iloc[split_idx:].copy()

        # Feature engineering
        blend_train_eng = self._engineer_advanced_features(blend_train)
        blend_holdout_eng = self._engineer_advanced_features(blend_holdout)

        # Prepare data
        X_train, y_train = self._prepare_traditional_data(blend_train_eng)
        X_holdout, y_holdout = self._prepare_traditional_data(blend_holdout_eng)

        X_train_scaled = self.traditional_scaler.fit_transform(X_train)
        X_holdout_scaled = self.traditional_scaler.transform(X_holdout)

        # Train base models on blend_train
        traditional_models = {k: v for k, v in self.base_models.items()
                            if k not in ['lstm', 'transformer']}

        holdout_predictions = np.zeros((len(X_holdout), len(traditional_models)))
        model_scores = {}

        for idx, (model_name, model) in enumerate(traditional_models.items()):
            self.logger.info(f"Training base model: {model_name}")

            model.fit(X_train_scaled, y_train)
            predictions = model.predict(X_holdout_scaled)

            holdout_predictions[:, idx] = predictions
            score = r2_score(y_holdout, predictions)
            model_scores[model_name] = score

        # Handle deep learning models
        deep_model_predictions = {}

        if 'lstm' in self.base_models:
            lstm_result = await self.base_models['lstm'].train(blend_train_eng)
            if lstm_result['status'] == 'success':
                lstm_pred = await self.base_models['lstm'].predict(blend_holdout_eng)
                if len(lstm_pred) == len(holdout_predictions):
                    deep_model_predictions['lstm'] = lstm_pred
                model_scores['lstm'] = lstm_result['metrics'].get('train_mae', 0.1)

        if 'transformer' in self.base_models:
            transformer_result = await self.base_models['transformer'].train(blend_train_eng)
            if transformer_result['status'] == 'success':
                transformer_pred = await self.base_models['transformer'].predict(blend_holdout_eng)
                if len(transformer_pred) == len(holdout_predictions):
                    deep_model_predictions['transformer'] = transformer_pred
                model_scores['transformer'] = transformer_result['metrics'].get('train_mae', 0.1)

        # Combine predictions
        all_holdout_predictions = holdout_predictions.copy()
        for model_name, predictions in deep_model_predictions.items():
            if len(predictions) == len(all_holdout_predictions):
                all_holdout_predictions = np.column_stack([all_holdout_predictions, predictions])

        # Train meta-model on holdout predictions
        self.meta_model.fit(all_holdout_predictions, y_holdout)

        # Calculate performance
        ensemble_predictions = self.meta_model.predict(all_holdout_predictions)
        ensemble_mae = mean_absolute_error(y_holdout, ensemble_predictions)
        ensemble_rmse = np.sqrt(mean_squared_error(y_holdout, ensemble_predictions))
        ensemble_r2 = r2_score(y_holdout, ensemble_predictions)

        self.is_trained = True
        self.model_performances = model_scores

        metrics = {
            'ensemble_mae': ensemble_mae,
            'ensemble_rmse': ensemble_rmse,
            'ensemble_r2': ensemble_r2,
            'base_model_scores': model_scores,
            'blend_ratio': blend_ratio
        }

        self.update_performance_metrics(metrics)

        self.logger.info(f"Blending ensemble training completed. "
                        f"Ensemble MAE: {ensemble_mae:.4f}, R²: {ensemble_r2:.4f}")

        return {
            'status': 'success',
            'metrics': metrics,
            'training_samples': len(X_train),
            'ensemble_method': 'blending'
        }

    async def _train_dynamic_weighting(self,
                                      training_data: pd.DataFrame,
                                      validation_data: pd.DataFrame = None) -> Dict[str, Any]:
        """Train ensemble using dynamic weighting based on recent performance"""
        self.logger.info("Training ensemble using dynamic weighting method")

        # Feature engineering
        engineered_data = self._engineer_advanced_features(training_data)

        # Prepare data
        X, y = self._prepare_traditional_data(engineered_data)
        X_scaled = self.traditional_scaler.fit_transform(X)

        # Train all base models
        model_predictions = {}
        model_scores = {}

        # Traditional models
        traditional_models = {k: v for k, v in self.base_models.items()
                            if k not in ['lstm', 'transformer']}

        for model_name, model in traditional_models.items():
            self.logger.info(f"Training base model: {model_name}")

            model.fit(X_scaled, y)
            predictions = model.predict(X_scaled)

            model_predictions[model_name] = predictions
            score = r2_score(y, predictions)
            model_scores[model_name] = score

        # Deep learning models
        if 'lstm' in self.base_models:
            lstm_result = await self.base_models['lstm'].train(engineered_data, validation_data)
            if lstm_result['status'] == 'success':
                lstm_pred = await self.base_models['lstm'].predict(engineered_data)
                if len(lstm_pred) >= len(y):
                    model_predictions['lstm'] = lstm_pred[-len(y):]
                    model_scores['lstm'] = lstm_result['metrics'].get('train_mae', 0.1)

        if 'transformer' in self.base_models:
            transformer_result = await self.base_models['transformer'].train(engineered_data, validation_data)
            if transformer_result['status'] == 'success':
                transformer_pred = await self.base_models['transformer'].predict(engineered_data)
                if len(transformer_pred) >= len(y):
                    model_predictions['transformer'] = transformer_pred[-len(y):]
                    model_scores['transformer'] = transformer_result['metrics'].get('train_mae', 0.1)

        # Calculate dynamic weights based on rolling performance
        self.dynamic_weights = self._calculate_dynamic_weights(model_predictions, y)

        # Calculate ensemble performance using dynamic weights
        ensemble_predictions = self._combine_predictions_dynamically(model_predictions, self.dynamic_weights)

        ensemble_mae = mean_absolute_error(y, ensemble_predictions)
        ensemble_rmse = np.sqrt(mean_squared_error(y, ensemble_predictions))
        ensemble_r2 = r2_score(y, ensemble_predictions)

        self.is_trained = True
        self.model_performances = model_scores

        metrics = {
            'ensemble_mae': ensemble_mae,
            'ensemble_rmse': ensemble_rmse,
            'ensemble_r2': ensemble_r2,
            'base_model_scores': model_scores,
            'dynamic_weights': self.dynamic_weights
        }

        self.update_performance_metrics(metrics)

        self.logger.info(f"Dynamic weighting ensemble training completed. "
                        f"Ensemble MAE: {ensemble_mae:.4f}, R²: {ensemble_r2:.4f}")

        return {
            'status': 'success',
            'metrics': metrics,
            'training_samples': len(X),
            'ensemble_method': 'dynamic_weighting'
        }

    async def predict(self,
                     input_data: pd.DataFrame,
                     return_individual: bool = False,
                     **kwargs) -> Union[np.ndarray, Dict[str, np.ndarray]]:
        """Make ensemble predictions"""
        try:
            if not self.is_trained:
                raise ValueError("Ensemble must be trained before making predictions")

            # Feature engineering
            engineered_data = self._engineer_advanced_features(input_data)

            if self.ensemble_method in ['stacking', 'blending']:
                return await self._predict_meta_model(engineered_data, return_individual)
            elif self.ensemble_method == 'dynamic':
                return await self._predict_dynamic(engineered_data, return_individual)
            else:
                raise ValueError(f"Prediction not implemented for method: {self.ensemble_method}")

        except Exception as e:
            self.logger.error(f"Error during prediction: {e}")
            return np.array([])

    async def _predict_meta_model(self,
                                 input_data: pd.DataFrame,
                                 return_individual: bool = False) -> Union[np.ndarray, Dict[str, np.ndarray]]:
        """Make predictions using meta-model approach"""
        # Prepare data for traditional models
        X, _ = self._prepare_traditional_data(input_data)
        X_scaled = self.traditional_scaler.transform(X)

        # Get predictions from traditional base models
        base_predictions = []
        individual_predictions = {}

        traditional_models = {k: v for k, v in self.base_models.items()
                            if k not in ['lstm', 'transformer']}

        for model_name, model in traditional_models.items():
            predictions = model.predict(X_scaled)
            base_predictions.append(predictions)
            individual_predictions[model_name] = predictions

        # Get predictions from deep learning models
        if 'lstm' in self.base_models and self.base_models['lstm'].is_trained:
            lstm_pred = await self.base_models['lstm'].predict(input_data)
            if len(lstm_pred) == len(base_predictions[0]):
                base_predictions.append(lstm_pred)
                individual_predictions['lstm'] = lstm_pred

        if 'transformer' in self.base_models and self.base_models['transformer'].is_trained:
            transformer_pred = await self.base_models['transformer'].predict(input_data)
            if len(transformer_pred) == len(base_predictions[0]):
                base_predictions.append(transformer_pred)
                individual_predictions['transformer'] = transformer_pred

        # Combine base predictions
        combined_predictions = np.column_stack(base_predictions)

        # Make ensemble prediction using meta-model
        ensemble_predictions = self.meta_model.predict(combined_predictions)

        if return_individual:
            individual_predictions['ensemble'] = ensemble_predictions
            return individual_predictions
        else:
            return ensemble_predictions

    async def _predict_dynamic(self,
                              input_data: pd.DataFrame,
                              return_individual: bool = False) -> Union[np.ndarray, Dict[str, np.ndarray]]:
        """Make predictions using dynamic weighting"""
        # Get individual model predictions
        individual_predictions = {}

        # Traditional models
        X, _ = self._prepare_traditional_data(input_data)
        X_scaled = self.traditional_scaler.transform(X)

        traditional_models = {k: v for k, v in self.base_models.items()
                            if k not in ['lstm', 'transformer']}

        for model_name, model in traditional_models.items():
            predictions = model.predict(X_scaled)
            individual_predictions[model_name] = predictions

        # Deep learning models
        if 'lstm' in self.base_models and self.base_models['lstm'].is_trained:
            lstm_pred = await self.base_models['lstm'].predict(input_data)
            individual_predictions['lstm'] = lstm_pred

        if 'transformer' in self.base_models and self.base_models['transformer'].is_trained:
            transformer_pred = await self.base_models['transformer'].predict(input_data)
            individual_predictions['transformer'] = transformer_pred

        # Combine using dynamic weights
        ensemble_predictions = self._combine_predictions_dynamically(
            individual_predictions, self.dynamic_weights
        )

        if return_individual:
            individual_predictions['ensemble'] = ensemble_predictions
            return individual_predictions
        else:
            return ensemble_predictions

    def _engineer_advanced_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Engineer advanced features for ensemble models"""
        engineered = data.copy()

        try:
            # Technical indicators
            if 'close_price' in engineered.columns:
                # Moving averages
                for window in [5, 10, 20, 50]:
                    engineered[f'sma_{window}'] = engineered['close_price'].rolling(window).mean()
                    engineered[f'ema_{window}'] = engineered['close_price'].ewm(span=window).mean()

                # Price ratios
                engineered['price_sma_ratio_20'] = engineered['close_price'] / engineered['sma_20']
                engineered['ema_ratio_12_26'] = engineered['ema_12'] / engineered['ema_26']

                # Volatility measures
                engineered['volatility_20'] = engineered['close_price'].rolling(20).std()
                engineered['price_change'] = engineered['close_price'].pct_change()
                engineered['price_change_volatility'] = engineered['price_change'].rolling(20).std()

                # RSI
                delta = engineered['close_price'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                engineered['rsi'] = 100 - (100 / (1 + rs))

                # MACD
                exp1 = engineered['close_price'].ewm(span=12).mean()
                exp2 = engineered['close_price'].ewm(span=26).mean()
                engineered['macd'] = exp1 - exp2
                engineered['macd_signal'] = engineered['macd'].ewm(span=9).mean()
                engineered['macd_histogram'] = engineered['macd'] - engineered['macd_signal']

                # Bollinger Bands
                sma_20 = engineered['close_price'].rolling(20).mean()
                std_20 = engineered['close_price'].rolling(20).std()
                engineered['bb_upper'] = sma_20 + (std_20 * 2)
                engineered['bb_lower'] = sma_20 - (std_20 * 2)
                engineered['bb_width'] = engineered['bb_upper'] - engineered['bb_lower']
                engineered['bb_position'] = (engineered['close_price'] - engineered['bb_lower']) / engineered['bb_width']

            # Volume features
            if 'volume' in engineered.columns:
                engineered['volume_sma_20'] = engineered['volume'].rolling(20).mean()
                engineered['volume_ratio'] = engineered['volume'] / engineered['volume_sma_20']
                engineered['volume_change'] = engineered['volume'].pct_change()

            # Time-based features
            if 'timestamp' in engineered.columns:
                engineered['timestamp'] = pd.to_datetime(engineered['timestamp'])
                engineered['hour'] = engineered['timestamp'].dt.hour
                engineered['day_of_week'] = engineered['timestamp'].dt.dayofweek
                engineered['month'] = engineered['timestamp'].dt.month

            # Fill NaN values
            engineered = engineered.fillna(method='bfill').fillna(method='ffill')

        except Exception as e:
            self.logger.warning(f"Error in feature engineering: {e}")

        return engineered

    def _prepare_traditional_data(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for traditional ML models"""
        # Select numerical features
        feature_cols = []
        for col in data.columns:
            if col not in ['timestamp', 'symbol'] and data[col].dtype in ['int64', 'float64']:
                feature_cols.append(col)

        # Remove target from features if present
        target_col = 'close_price'
        if target_col in feature_cols:
            feature_cols.remove(target_col)

        X = data[feature_cols].values
        y = data[target_col].values if target_col in data.columns else None

        return X, y

    def _calculate_dynamic_weights(self,
                                  model_predictions: Dict[str, np.ndarray],
                                  y_true: np.ndarray) -> Dict[str, np.ndarray]:
        """Calculate dynamic weights based on rolling performance"""
        window = self.hyperparameters['performance_window']
        weights = {}

        for model_name, predictions in model_predictions.items():
            if len(predictions) != len(y_true):
                continue

            model_weights = np.ones(len(predictions))

            # Calculate rolling performance
            for i in range(window, len(predictions)):
                start_idx = max(0, i - window)
                end_idx = i

                # Calculate MAE for this window
                window_mae = mean_absolute_error(
                    y_true[start_idx:end_idx],
                    predictions[start_idx:end_idx]
                )

                # Convert to weight (lower MAE = higher weight)
                weight = 1.0 / (1.0 + window_mae)
                model_weights[i] = weight

            weights[model_name] = model_weights

        return weights

    def _combine_predictions_dynamically(self,
                                        model_predictions: Dict[str, np.ndarray],
                                        weights: Dict[str, np.ndarray]) -> np.ndarray:
        """Combine predictions using dynamic weights"""
        # Find common length
        min_length = min(len(pred) for pred in model_predictions.values())

        weighted_predictions = np.zeros(min_length)
        total_weights = np.zeros(min_length)

        for model_name, predictions in model_predictions.items():
            if model_name in weights:
                model_weights = weights[model_name][-min_length:]
                model_preds = predictions[-min_length:]

                weighted_predictions += model_preds * model_weights
                total_weights += model_weights

        # Avoid division by zero
        total_weights = np.where(total_weights == 0, 1, total_weights)
        ensemble_predictions = weighted_predictions / total_weights

        return ensemble_predictions

    def get_model_importance(self) -> Dict[str, float]:
        """Get importance of each model in the ensemble"""
        if self.ensemble_method in ['stacking', 'blending'] and hasattr(self.meta_model, 'coef_'):
            # For linear meta-models, use coefficients as importance
            model_names = list(self.base_models.keys())
            coefficients = self.meta_model.coef_

            if len(coefficients) == len(model_names):
                # Normalize to sum to 1
                abs_coefs = np.abs(coefficients)
                normalized_coefs = abs_coefs / np.sum(abs_coefs)
                return dict(zip(model_names, normalized_coefs))

        elif self.ensemble_method == 'dynamic' and self.dynamic_weights:
            # For dynamic weighting, use average weights
            avg_weights = {}
            for model_name, weights in self.dynamic_weights.items():
                avg_weights[model_name] = np.mean(weights)

            # Normalize
            total_weight = sum(avg_weights.values())
            if total_weight > 0:
                return {k: v/total_weight for k, v in avg_weights.items()}

        # Fallback to equal weighting
        num_models = len(self.base_models)
        return {name: 1.0/num_models for name in self.base_models.keys()}

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate ensemble performance"""
        try:
            # Get ensemble predictions
            predictions = await self.predict(test_data)

            if len(predictions) == 0:
                return {'error': 'No predictions generated'}

            # Prepare target values
            engineered_data = self._engineer_advanced_features(test_data)
            _, y_true = self._prepare_traditional_data(engineered_data)

            # Align predictions and targets
            min_length = min(len(predictions), len(y_true))
            predictions = predictions[-min_length:]
            y_true = y_true[-min_length:]

            # Calculate metrics
            mae = mean_absolute_error(y_true, predictions)
            rmse = np.sqrt(mean_squared_error(y_true, predictions))
            r2 = r2_score(y_true, predictions)
            mape = np.mean(np.abs((y_true - predictions) / y_true)) * 100

            # Trading-specific metrics
            returns_actual = np.diff(y_true) / y_true[:-1]
            returns_predicted = np.diff(predictions) / predictions[:-1]

            # Directional accuracy
            direction_actual = np.sign(returns_actual)
            direction_predicted = np.sign(returns_predicted)
            directional_accuracy = np.mean(direction_actual == direction_predicted)

            # Calculate Sharpe ratio of strategy
            strategy_returns = returns_actual * direction_predicted
            sharpe_ratio = np.mean(strategy_returns) / np.std(strategy_returns) if np.std(strategy_returns) > 0 else 0

            metrics = {
                'test_mae': mae,
                'test_rmse': rmse,
                'test_r2': r2,
                'test_mape': mape,
                'directional_accuracy': directional_accuracy,
                'strategy_sharpe_ratio': sharpe_ratio
            }

            self.logger.info(f"Ensemble evaluation - MAE: {mae:.4f}, R²: {r2:.4f}, "
                           f"Directional Accuracy: {directional_accuracy:.3f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}

    def save_ensemble(self, filepath: str):
        """Save the entire ensemble"""
        ensemble_data = {
            'ensemble_method': self.ensemble_method,
            'hyperparameters': self.hyperparameters,
            'model_performances': self.model_performances,
            'dynamic_weights': getattr(self, 'dynamic_weights', {}),
            'is_trained': self.is_trained,
            'traditional_scaler': self.traditional_scaler,
            'neural_scaler': self.neural_scaler
        }

        joblib.dump(ensemble_data, filepath)

        # Save individual models
        for model_name, model in self.base_models.items():
            if hasattr(model, 'save_model'):
                model.save_model(f"{filepath}_{model_name}")

    def load_ensemble(self, filepath: str):
        """Load the entire ensemble"""
        ensemble_data = joblib.load(filepath)

        self.ensemble_method = ensemble_data['ensemble_method']
        self.hyperparameters = ensemble_data['hyperparameters']
        self.model_performances = ensemble_data['model_performances']
        self.dynamic_weights = ensemble_data.get('dynamic_weights', {})
        self.is_trained = ensemble_data['is_trained']
        self.traditional_scaler = ensemble_data['traditional_scaler']
        self.neural_scaler = ensemble_data['neural_scaler']

        # Load individual models
        for model_name, model in self.base_models.items():
            if hasattr(model, 'load_model'):
                try:
                    model.load_model(f"{filepath}_{model_name}")
                except FileNotFoundError:
                    self.logger.warning(f"Could not load model: {model_name}")