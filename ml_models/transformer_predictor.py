"""
Transformer Neural Network for Time Series Forecasting
Advanced attention-based model for sequential data prediction
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
from typing import Dict, List, Tuple, Any, Optional
import logging
import math

from .base_model import BaseMLModel

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer"""

    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)

        self.register_buffer('pe', pe)

    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class TimeSeriesTransformer(nn.Module):
    """Transformer model for time series prediction"""

    def __init__(self,
                 input_dim: int,
                 d_model: int = 128,
                 nhead: int = 8,
                 num_layers: int = 6,
                 dim_feedforward: int = 512,
                 dropout: float = 0.1,
                 max_len: int = 200,
                 output_dim: int = 1):
        super().__init__()

        self.d_model = d_model
        self.input_dim = input_dim
        self.output_dim = output_dim

        # Input projection
        self.input_projection = nn.Linear(input_dim, d_model)

        # Positional encoding
        self.pos_encoder = PositionalEncoding(d_model, max_len)

        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)

        # Output layers
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, dim_feedforward // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward // 2, output_dim)
        )

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)

    def forward(self, x, mask=None):
        """Forward pass"""
        # x shape: (batch_size, seq_len, input_dim)

        # Project input to model dimension
        x = self.input_projection(x) * math.sqrt(self.d_model)

        # Add positional encoding
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)

        # Apply transformer encoder
        encoded = self.transformer_encoder(x, src_key_padding_mask=mask)

        # Use last timestep for prediction
        output = self.output_projection(encoded[:, -1, :])

        return output

class TimeSeriesDataset(Dataset):
    """Dataset class for time series data"""

    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

class TransformerPredictor(BaseMLModel):
    """Transformer Neural Network for time series prediction"""

    def __init__(self,
                 model_id: str,
                 sequence_length: int = 100,
                 prediction_horizon: int = 1):
        super().__init__(model_id, "Transformer", "1.0")

        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.scaler = StandardScaler()
        self.target_scaler = StandardScaler()

        # Model hyperparameters
        self.hyperparameters = {
            'd_model': 128,
            'nhead': 8,
            'num_layers': 6,
            'dim_feedforward': 512,
            'dropout': 0.1,
            'learning_rate': 0.0001,
            'batch_size': 32,
            'epochs': 100,
            'patience': 15,
            'weight_decay': 1e-5
        }

        self.metadata['hyperparameters'] = self.hyperparameters
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the Transformer model"""
        try:
            self.logger.info(f"Starting Transformer training with {len(training_data)} samples")

            # Update hyperparameters
            self.hyperparameters.update(kwargs)

            # Prepare training data
            X_train, y_train = self._prepare_sequences(training_data, is_training=True)

            if X_train is None or len(X_train) == 0:
                raise ValueError("Insufficient training data for sequence creation")

            # Prepare validation data
            X_val, y_val = None, None
            if validation_data is not None:
                X_val, y_val = self._prepare_sequences(validation_data, is_training=False)

            # Create data loaders
            train_dataset = TimeSeriesDataset(X_train, y_train)
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.hyperparameters['batch_size'],
                shuffle=True,
                drop_last=True
            )

            val_loader = None
            if X_val is not None:
                val_dataset = TimeSeriesDataset(X_val, y_val)
                val_loader = DataLoader(
                    val_dataset,
                    batch_size=self.hyperparameters['batch_size'],
                    shuffle=False
                )

            # Initialize model
            input_dim = X_train.shape[2]
            self.model = TimeSeriesTransformer(
                input_dim=input_dim,
                d_model=self.hyperparameters['d_model'],
                nhead=self.hyperparameters['nhead'],
                num_layers=self.hyperparameters['num_layers'],
                dim_feedforward=self.hyperparameters['dim_feedforward'],
                dropout=self.hyperparameters['dropout'],
                max_len=self.sequence_length,
                output_dim=self.prediction_horizon
            ).to(self.device)

            # Setup optimizer and scheduler
            optimizer = torch.optim.AdamW(
                self.model.parameters(),
                lr=self.hyperparameters['learning_rate'],
                weight_decay=self.hyperparameters['weight_decay']
            )

            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, mode='min', factor=0.5, patience=10, min_lr=1e-7
            )

            criterion = nn.MSELoss()

            # Training loop
            train_losses = []
            val_losses = []
            best_val_loss = float('inf')
            patience_counter = 0

            for epoch in range(self.hyperparameters['epochs']):
                # Training phase
                self.model.train()
                epoch_train_loss = 0.0

                for batch_x, batch_y in train_loader:
                    batch_x = batch_x.to(self.device)
                    batch_y = batch_y.to(self.device)

                    optimizer.zero_grad()
                    outputs = self.model(batch_x)
                    loss = criterion(outputs, batch_y)
                    loss.backward()

                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)

                    optimizer.step()
                    epoch_train_loss += loss.item()

                avg_train_loss = epoch_train_loss / len(train_loader)
                train_losses.append(avg_train_loss)

                # Validation phase
                if val_loader is not None:
                    self.model.eval()
                    epoch_val_loss = 0.0

                    with torch.no_grad():
                        for batch_x, batch_y in val_loader:
                            batch_x = batch_x.to(self.device)
                            batch_y = batch_y.to(self.device)

                            outputs = self.model(batch_x)
                            loss = criterion(outputs, batch_y)
                            epoch_val_loss += loss.item()

                    avg_val_loss = epoch_val_loss / len(val_loader)
                    val_losses.append(avg_val_loss)

                    # Early stopping and learning rate scheduling
                    scheduler.step(avg_val_loss)

                    if avg_val_loss < best_val_loss:
                        best_val_loss = avg_val_loss
                        patience_counter = 0
                        # Save best model
                        self.best_model_state = self.model.state_dict().copy()
                    else:
                        patience_counter += 1

                    if patience_counter >= self.hyperparameters['patience']:
                        self.logger.info(f"Early stopping at epoch {epoch + 1}")
                        break

                    if (epoch + 1) % 10 == 0:
                        self.logger.info(f"Epoch {epoch + 1}/{self.hyperparameters['epochs']}, "
                                       f"Train Loss: {avg_train_loss:.6f}, "
                                       f"Val Loss: {avg_val_loss:.6f}")
                else:
                    if (epoch + 1) % 10 == 0:
                        self.logger.info(f"Epoch {epoch + 1}/{self.hyperparameters['epochs']}, "
                                       f"Train Loss: {avg_train_loss:.6f}")

            # Load best model if validation was used
            if hasattr(self, 'best_model_state'):
                self.model.load_state_dict(self.best_model_state)

            self.is_trained = True

            # Calculate final metrics
            self.model.eval()
            with torch.no_grad():
                # Train metrics
                train_predictions = []
                train_targets = []
                for batch_x, batch_y in train_loader:
                    batch_x = batch_x.to(self.device)
                    outputs = self.model(batch_x)
                    train_predictions.append(outputs.cpu().numpy())
                    train_targets.append(batch_y.numpy())

                train_predictions = np.concatenate(train_predictions)
                train_targets = np.concatenate(train_targets)

                # Inverse transform predictions
                train_predictions_inv = self.target_scaler.inverse_transform(train_predictions.reshape(-1, 1))
                train_targets_inv = self.target_scaler.inverse_transform(train_targets.reshape(-1, 1))

                train_mae = mean_absolute_error(train_targets_inv, train_predictions_inv)
                train_rmse = np.sqrt(mean_squared_error(train_targets_inv, train_predictions_inv))

            metrics = {
                'train_mae': train_mae,
                'train_rmse': train_rmse,
                'final_train_loss': train_losses[-1],
                'epochs_trained': len(train_losses)
            }

            if val_losses:
                metrics.update({
                    'final_val_loss': val_losses[-1],
                    'best_val_loss': best_val_loss
                })

            self.update_performance_metrics(metrics)

            # Update metadata
            self.metadata['training_samples'] = len(X_train)
            self.metadata['last_updated'] = pd.Timestamp.now()

            self.logger.info(f"Transformer training completed. "
                           f"Train MAE: {train_mae:.4f}, Train RMSE: {train_rmse:.4f}")

            return {
                'status': 'success',
                'metrics': metrics,
                'training_samples': len(X_train),
                'epochs_trained': len(train_losses)
            }

        except Exception as e:
            self.logger.error(f"Error during Transformer training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict(self,
                     input_data: pd.DataFrame,
                     return_sequences: bool = False,
                     **kwargs) -> np.ndarray:
        """Make predictions using the trained Transformer model"""
        try:
            if not self.is_trained or self.model is None:
                raise ValueError("Model must be trained before making predictions")

            # Prepare input sequences
            X_pred = self._prepare_prediction_sequences(input_data)

            if X_pred is None or len(X_pred) == 0:
                raise ValueError("Insufficient data for prediction sequences")

            self.model.eval()
            with torch.no_grad():
                X_pred_tensor = torch.FloatTensor(X_pred).to(self.device)
                scaled_predictions = self.model(X_pred_tensor).cpu().numpy()

            # Inverse transform predictions
            predictions = self.target_scaler.inverse_transform(scaled_predictions.reshape(-1, 1))

            if return_sequences:
                return predictions.reshape(-1, self.prediction_horizon)
            else:
                return predictions.flatten()

        except Exception as e:
            self.logger.error(f"Error during prediction: {e}")
            return np.array([])

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate model performance on test data"""
        try:
            # Prepare test sequences
            X_test, y_test = self._prepare_sequences(test_data, is_training=False)

            if X_test is None or len(X_test) == 0:
                return {'error': 'Insufficient test data'}

            # Create test data loader
            test_dataset = TimeSeriesDataset(X_test, y_test)
            test_loader = DataLoader(
                test_dataset,
                batch_size=self.hyperparameters['batch_size'],
                shuffle=False
            )

            self.model.eval()
            all_predictions = []
            all_targets = []

            with torch.no_grad():
                for batch_x, batch_y in test_loader:
                    batch_x = batch_x.to(self.device)
                    outputs = self.model(batch_x)
                    all_predictions.append(outputs.cpu().numpy())
                    all_targets.append(batch_y.numpy())

            predictions = np.concatenate(all_predictions)
            targets = np.concatenate(all_targets)

            # Inverse transform
            predictions_inv = self.target_scaler.inverse_transform(predictions.reshape(-1, 1))
            targets_inv = self.target_scaler.inverse_transform(targets.reshape(-1, 1))

            # Calculate metrics
            mae = mean_absolute_error(targets_inv, predictions_inv)
            rmse = np.sqrt(mean_squared_error(targets_inv, predictions_inv))
            mape = np.mean(np.abs((targets_inv - predictions_inv) / targets_inv)) * 100

            # Directional accuracy
            actual_direction = np.sign(np.diff(targets_inv.flatten()))
            pred_direction = np.sign(np.diff(predictions_inv.flatten()))
            directional_accuracy = np.mean(actual_direction == pred_direction)

            metrics = {
                'test_mae': mae,
                'test_rmse': rmse,
                'test_mape': mape,
                'directional_accuracy': directional_accuracy
            }

            self.logger.info(f"Transformer evaluation - MAE: {mae:.4f}, RMSE: {rmse:.4f}, "
                           f"Directional Accuracy: {directional_accuracy:.3f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}

    def _prepare_sequences(self, data: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare sequences for Transformer input"""
        try:
            # Define features and target
            feature_cols = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
            target_col = 'close_price'

            # Filter available columns
            available_features = [col for col in feature_cols if col in data.columns]
            if not available_features:
                self.logger.error("No required features found in data")
                return None, None

            # Add technical indicators if not present
            data_with_features = self._add_technical_features(data.copy())

            # Update available features with technical indicators
            technical_features = ['sma_20', 'ema_20', 'rsi', 'macd', 'bb_upper', 'bb_lower']
            available_features.extend([col for col in technical_features if col in data_with_features.columns])

            # Prepare feature data
            feature_data = data_with_features[available_features].values
            target_data = data_with_features[target_col].values.reshape(-1, 1)

            if is_training:
                # Fit scalers on training data
                scaled_features = self.scaler.fit_transform(feature_data)
                scaled_target = self.target_scaler.fit_transform(target_data)
                self.feature_columns = available_features
                self.target_columns = [target_col]
            else:
                # Use fitted scalers
                scaled_features = self.scaler.transform(feature_data)
                scaled_target = self.target_scaler.transform(target_data)

            # Create sequences
            X, y = [], []
            for i in range(self.sequence_length, len(scaled_features) - self.prediction_horizon + 1):
                X.append(scaled_features[i-self.sequence_length:i])
                y.append(scaled_target[i:i+self.prediction_horizon])

            return np.array(X), np.array(y).squeeze()

        except Exception as e:
            self.logger.error(f"Error preparing sequences: {e}")
            return None, None

    def _prepare_prediction_sequences(self, data: pd.DataFrame) -> np.ndarray:
        """Prepare sequences for prediction only"""
        try:
            # Add technical features
            data_with_features = self._add_technical_features(data.copy())

            # Use only the features from training
            feature_data = data_with_features[self.feature_columns].values

            # Scale features
            scaled_features = self.scaler.transform(feature_data)

            # Create sequences
            if len(scaled_features) < self.sequence_length:
                self.logger.warning(f"Insufficient data for prediction. "
                                  f"Need {self.sequence_length}, got {len(scaled_features)}")
                return None

            # Take the last sequence
            X = scaled_features[-self.sequence_length:].reshape(1, self.sequence_length, -1)

            return X

        except Exception as e:
            self.logger.error(f"Error preparing prediction sequences: {e}")
            return None

    def _add_technical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to the data"""
        try:
            # Simple Moving Average
            data['sma_20'] = data['close_price'].rolling(window=20).mean()

            # Exponential Moving Average
            data['ema_20'] = data['close_price'].ewm(span=20).mean()

            # RSI
            delta = data['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = data['close_price'].ewm(span=12).mean()
            exp2 = data['close_price'].ewm(span=26).mean()
            data['macd'] = exp1 - exp2

            # Bollinger Bands
            sma_20 = data['close_price'].rolling(window=20).mean()
            std_20 = data['close_price'].rolling(window=20).std()
            data['bb_upper'] = sma_20 + (std_20 * 2)
            data['bb_lower'] = sma_20 - (std_20 * 2)

            # Fill NaN values
            data = data.fillna(method='bfill').fillna(method='ffill')

            return data

        except Exception as e:
            self.logger.error(f"Error adding technical features: {e}")
            return data

    def get_attention_weights(self, input_data: pd.DataFrame) -> np.ndarray:
        """Extract attention weights for interpretability"""
        try:
            if not self.is_trained or self.model is None:
                return np.array([])

            X_pred = self._prepare_prediction_sequences(input_data)
            if X_pred is None:
                return np.array([])

            self.model.eval()

            # Hook to capture attention weights
            attention_weights = []

            def hook_fn(module, input, output):
                if hasattr(module, 'self_attn'):
                    attention_weights.append(output[1].detach().cpu().numpy())

            # Register hooks on transformer encoder layers
            hooks = []
            for layer in self.model.transformer_encoder.layers:
                hook = layer.register_forward_hook(hook_fn)
                hooks.append(hook)

            with torch.no_grad():
                X_pred_tensor = torch.FloatTensor(X_pred).to(self.device)
                _ = self.model(X_pred_tensor)

            # Remove hooks
            for hook in hooks:
                hook.remove()

            # Average attention weights across layers and heads
            if attention_weights:
                avg_attention = np.mean(attention_weights, axis=(0, 1))
                return avg_attention
            else:
                return np.array([])

        except Exception as e:
            self.logger.error(f"Error extracting attention weights: {e}")
            return np.array([])

    def calculate_prediction_confidence(self, predictions: np.ndarray) -> np.ndarray:
        """Calculate confidence scores using model uncertainty"""
        try:
            if len(predictions) < 2:
                return np.array([0.5])

            # For transformer, confidence can be based on attention consistency
            # and prediction variance across multiple forward passes (Monte Carlo Dropout)

            # Simple approach: use prediction variance
            variance = np.var(predictions[-min(10, len(predictions)):])
            confidence = 1.0 / (1.0 + variance)

            return np.array([confidence])

        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return np.array([0.5])

    async def predict_with_confidence(self, input_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Make predictions with confidence scores"""
        predictions = await self.predict(input_data)
        confidence = self.calculate_prediction_confidence(predictions)
        return predictions, confidence

    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance using attention weights"""
        if not self.feature_columns:
            return {}

        # For transformer, feature importance can be approximated by
        # averaging attention weights across time steps
        importance = 1.0 / len(self.feature_columns)
        return {col: importance for col in self.feature_columns}

    def save_model(self, filepath: str):
        """Save the trained model"""
        if self.model is not None:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'hyperparameters': self.hyperparameters,
                'scaler': self.scaler,
                'target_scaler': self.target_scaler,
                'feature_columns': self.feature_columns,
                'sequence_length': self.sequence_length,
                'prediction_horizon': self.prediction_horizon
            }, filepath)

    def load_model(self, filepath: str):
        """Load a trained model"""
        checkpoint = torch.load(filepath, map_location=self.device)

        self.hyperparameters = checkpoint['hyperparameters']
        self.scaler = checkpoint['scaler']
        self.target_scaler = checkpoint['target_scaler']
        self.feature_columns = checkpoint['feature_columns']
        self.sequence_length = checkpoint['sequence_length']
        self.prediction_horizon = checkpoint['prediction_horizon']

        # Recreate model
        input_dim = len(self.feature_columns)
        self.model = TimeSeriesTransformer(
            input_dim=input_dim,
            d_model=self.hyperparameters['d_model'],
            nhead=self.hyperparameters['nhead'],
            num_layers=self.hyperparameters['num_layers'],
            dim_feedforward=self.hyperparameters['dim_feedforward'],
            dropout=self.hyperparameters['dropout'],
            max_len=self.sequence_length,
            output_dim=self.prediction_horizon
        ).to(self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.is_trained = True