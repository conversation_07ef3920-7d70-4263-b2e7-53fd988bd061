"""
Adaptive Learning System for Live Market Data
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import asyncio
import logging
from collections import deque
from sklearn.metrics import mean_absolute_error
import json

from .base_model import BaseMLModel
from .ensemble_predictor import EnsemblePredictor

class AdaptiveLearner(BaseMLModel):
    """Adaptive learning system that continuously learns from live market data"""

    def __init__(self, model_id: str, base_model: BaseMLModel = None):
        super().__init__(model_id, "AdaptiveLearner", "1.0")

        # Base model for adaptive learning
        self.base_model = base_model or EnsemblePredictor(f"{model_id}_ensemble")

        # Adaptive learning parameters
        self.hyperparameters = {
            'adaptation_window': 1000,  # Number of samples for adaptation
            'performance_threshold': 0.1,  # Trigger retraining if performance degrades by 10%
            'min_samples_for_adaptation': 50,  # Minimum samples before adaptation
            'adaptation_frequency': 300,  # Seconds between adaptation checks
            'concept_drift_threshold': 0.15,  # Threshold for detecting concept drift
            'forgetting_factor': 0.95,  # Exponential forgetting for older data
            'confidence_threshold': 0.7,  # Minimum confidence for predictions
            'feedback_weight': 0.3,  # Weight of new feedback vs historical data
            'max_memory_size': 10000  # Maximum size of memory buffer
        }

        # Memory systems
        self.prediction_memory = deque(maxlen=self.hyperparameters['max_memory_size'])
        self.performance_memory = deque(maxlen=self.hyperparameters['adaptation_window'])
        self.concept_drift_memory = deque(maxlen=100)

        # Adaptation tracking
        self.last_adaptation_time = None
        self.baseline_performance = None
        self.adaptation_count = 0
        self.concept_drift_detected = False

        # Real-time learning buffer
        self.learning_buffer = []
        self.feedback_buffer = []

        self.metadata['hyperparameters'] = self.hyperparameters

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Initial training of the base model"""
        try:
            self.logger.info("Starting adaptive learner initial training")

            # Train the base model
            result = await self.base_model.train(training_data, validation_data, **kwargs)

            if result['status'] == 'success':
                self.is_trained = True
                self.baseline_performance = result['metrics'].get('val_mae', result['metrics'].get('train_mae', 0.0))

                # Initialize adaptation tracking
                self.last_adaptation_time = datetime.now()

                self.logger.info(f"Adaptive learner trained with baseline MAE: {self.baseline_performance:.4f}")

                return {
                    'status': 'success',
                    'base_model_metrics': result['metrics'],
                    'baseline_performance': self.baseline_performance
                }
            else:
                return result

        except Exception as e:
            self.logger.error(f"Error in adaptive learner training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict(self,
                     input_data: pd.DataFrame,
                     store_prediction: bool = True,
                     **kwargs) -> np.ndarray:
        """Make predictions and optionally store for learning"""
        try:
            if not self.is_trained:
                raise ValueError("Adaptive learner must be trained before making predictions")

            # Get prediction from base model
            prediction = await self.base_model.predict(input_data, **kwargs)

            if len(prediction) == 0:
                return prediction

            # Calculate prediction confidence
            confidence = self._calculate_prediction_confidence(input_data, prediction)

            # Store prediction for adaptive learning
            if store_prediction:
                prediction_record = {
                    'timestamp': datetime.now(),
                    'input_data_hash': hash(str(input_data.values.tobytes())),
                    'prediction': prediction.copy(),
                    'confidence': confidence,
                    'input_features': input_data.iloc[-1].to_dict() if not input_data.empty else {}
                }
                self.prediction_memory.append(prediction_record)

            # Check if adaptation is needed
            if self._should_trigger_adaptation():
                asyncio.create_task(self._perform_adaptation())

            return prediction

        except Exception as e:
            self.logger.error(f"Error in adaptive prediction: {e}")
            return np.array([])

    async def add_feedback(self,
                          actual_values: np.ndarray,
                          prediction_timestamp: datetime,
                          market_conditions: Dict[str, Any] = None):
        """Add feedback for adaptive learning"""
        try:
            # Find matching prediction
            matching_prediction = None
            for pred_record in reversed(self.prediction_memory):
                time_diff = abs((pred_record['timestamp'] - prediction_timestamp).total_seconds())
                if time_diff < 300:  # Within 5 minutes
                    matching_prediction = pred_record
                    break

            if matching_prediction is None:
                self.logger.warning("No matching prediction found for feedback")
                return

            # Calculate prediction error
            predicted_values = matching_prediction['prediction']
            if len(predicted_values) != len(actual_values):
                self.logger.warning("Prediction and actual values length mismatch")
                return

            error = mean_absolute_error(actual_values, predicted_values)

            # Create feedback record
            feedback_record = {
                'timestamp': datetime.now(),
                'prediction_timestamp': prediction_timestamp,
                'actual_values': actual_values.copy(),
                'predicted_values': predicted_values.copy(),
                'error': error,
                'confidence': matching_prediction['confidence'],
                'market_conditions': market_conditions or {}
            }

            self.feedback_buffer.append(feedback_record)
            self.performance_memory.append(error)

            # Detect concept drift
            self._detect_concept_drift(error)

            self.logger.info(f"Added feedback with error: {error:.4f}")

        except Exception as e:
            self.logger.error(f"Error adding feedback: {e}")

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate adaptive learner performance"""
        try:
            # Evaluate base model
            base_metrics = await self.base_model.evaluate(test_data, **kwargs)

            # Calculate adaptive learning metrics
            adaptive_metrics = self._calculate_adaptive_metrics()

            # Combine metrics
            metrics = {
                **base_metrics,
                **adaptive_metrics,
                'adaptation_count': self.adaptation_count,
                'concept_drift_detected': self.concept_drift_detected,
                'memory_utilization': len(self.prediction_memory) / self.hyperparameters['max_memory_size']
            }

            return metrics

        except Exception as e:
            self.logger.error(f"Error in adaptive evaluation: {e}")
            return {'error': str(e)}

    def _should_trigger_adaptation(self) -> bool:
        """Determine if adaptation should be triggered"""
        try:
            # Check time since last adaptation
            if self.last_adaptation_time:
                time_diff = (datetime.now() - self.last_adaptation_time).total_seconds()
                if time_diff < self.hyperparameters['adaptation_frequency']:
                    return False

            # Check if we have enough feedback
            if len(self.feedback_buffer) < self.hyperparameters['min_samples_for_adaptation']:
                return False

            # Check performance degradation
            if self._detect_performance_degradation():
                return True

            # Check concept drift
            if self.concept_drift_detected:
                return True

            # Regular adaptation check
            if len(self.feedback_buffer) >= self.hyperparameters['adaptation_window']:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking adaptation trigger: {e}")
            return False

    async def _perform_adaptation(self):
        """Perform adaptive learning"""
        try:
            self.logger.info("Starting adaptive learning process")

            if not self.feedback_buffer:
                self.logger.warning("No feedback available for adaptation")
                return

            # Prepare adaptive training data
            adaptive_data = self._prepare_adaptive_data()

            if adaptive_data is None or len(adaptive_data) < self.hyperparameters['min_samples_for_adaptation']:
                self.logger.warning("Insufficient data for adaptation")
                return

            # Perform incremental learning
            result = await self.base_model.retrain(
                adaptive_data,
                incremental=True,
                feedback_weight=self.hyperparameters['feedback_weight']
            )

            if result['status'] == 'success':
                self.adaptation_count += 1
                self.last_adaptation_time = datetime.now()
                self.concept_drift_detected = False

                # Clear old feedback buffer
                self.feedback_buffer = []

                self.logger.info(f"Adaptation #{self.adaptation_count} completed successfully")

            else:
                self.logger.error(f"Adaptation failed: {result.get('message', 'Unknown error')}")

        except Exception as e:
            self.logger.error(f"Error during adaptation: {e}")

    def _detect_performance_degradation(self) -> bool:
        """Detect if model performance has degraded"""
        try:
            if not self.performance_memory or self.baseline_performance is None:
                return False

            # Calculate recent performance
            recent_errors = list(self.performance_memory)[-50:]  # Last 50 predictions
            if len(recent_errors) < 10:
                return False

            recent_performance = np.mean(recent_errors)

            # Check if performance has degraded beyond threshold
            degradation = (recent_performance - self.baseline_performance) / self.baseline_performance

            if degradation > self.hyperparameters['performance_threshold']:
                self.logger.warning(f"Performance degradation detected: {degradation:.2%}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error detecting performance degradation: {e}")
            return False

    def _detect_concept_drift(self, current_error: float):
        """Detect concept drift using error patterns"""
        try:
            self.concept_drift_memory.append(current_error)

            if len(self.concept_drift_memory) < 20:
                return

            # Statistical test for concept drift
            recent_errors = list(self.concept_drift_memory)[-20:]
            historical_errors = list(self.concept_drift_memory)[:-20]

            if len(historical_errors) < 20:
                return

            # Compare distributions
            recent_mean = np.mean(recent_errors)
            historical_mean = np.mean(historical_errors)

            # Simple drift detection based on error increase
            if recent_mean > historical_mean * (1 + self.hyperparameters['concept_drift_threshold']):
                self.concept_drift_detected = True
                self.logger.warning("Concept drift detected")

        except Exception as e:
            self.logger.error(f"Error detecting concept drift: {e}")

    def _prepare_adaptive_data(self) -> pd.DataFrame:
        """Prepare data for adaptive learning from feedback"""
        try:
            if not self.feedback_buffer:
                return None

            # Convert feedback to training data
            adaptive_records = []

            for feedback in self.feedback_buffer:
                # Extract features from the original prediction
                features = feedback.get('market_conditions', {})

                # Add actual values as targets
                record = {
                    **features,
                    'close_price': feedback['actual_values'][0] if len(feedback['actual_values']) > 0 else 0,
                    'timestamp': feedback['timestamp']
                }

                adaptive_records.append(record)

            if not adaptive_records:
                return None

            # Create DataFrame
            adaptive_df = pd.DataFrame(adaptive_records)

            # Apply forgetting factor to older data
            weights = np.power(self.hyperparameters['forgetting_factor'],
                             np.arange(len(adaptive_df)-1, -1, -1))

            # Sample based on weights (newer data more likely to be selected)
            sample_size = min(len(adaptive_df), self.hyperparameters['adaptation_window'])
            probabilities = weights / weights.sum()

            indices = np.random.choice(
                len(adaptive_df),
                size=sample_size,
                replace=False,
                p=probabilities
            )

            return adaptive_df.iloc[indices].reset_index(drop=True)

        except Exception as e:
            self.logger.error(f"Error preparing adaptive data: {e}")
            return None

    def _calculate_prediction_confidence(self, input_data: pd.DataFrame, prediction: np.ndarray) -> float:
        """Calculate confidence score for prediction"""
        try:
            # Simple confidence calculation based on recent performance
            if not self.performance_memory:
                return 0.5

            recent_errors = list(self.performance_memory)[-20:]
            if len(recent_errors) < 5:
                return 0.5

            # Confidence inversely related to recent error
            avg_error = np.mean(recent_errors)
            confidence = 1.0 / (1.0 + avg_error)

            # Apply market condition adjustments
            volatility_adjustment = self._assess_market_volatility(input_data)
            confidence *= volatility_adjustment

            return min(max(confidence, 0.1), 0.95)

        except Exception as e:
            self.logger.error(f"Error calculating prediction confidence: {e}")
            return 0.5

    def _assess_market_volatility(self, input_data: pd.DataFrame) -> float:
        """Assess market volatility to adjust confidence"""
        try:
            if 'close_price' not in input_data.columns or len(input_data) < 10:
                return 1.0

            # Calculate recent volatility
            close_prices = input_data['close_price'].tail(10)
            returns = close_prices.pct_change().dropna()

            if len(returns) < 5:
                return 1.0

            volatility = returns.std()

            # Lower confidence in high volatility markets
            if volatility > 0.05:  # 5% daily volatility
                return 0.7
            elif volatility > 0.03:  # 3% daily volatility
                return 0.85
            else:
                return 1.0

        except Exception as e:
            self.logger.error(f"Error assessing market volatility: {e}")
            return 1.0

    def _calculate_adaptive_metrics(self) -> Dict[str, float]:
        """Calculate metrics specific to adaptive learning"""
        try:
            metrics = {}

            # Performance trend
            if len(self.performance_memory) >= 20:
                recent_performance = np.mean(list(self.performance_memory)[-10:])
                older_performance = np.mean(list(self.performance_memory)[-20:-10])

                if older_performance > 0:
                    performance_trend = (recent_performance - older_performance) / older_performance
                    metrics['performance_trend'] = performance_trend

            # Adaptation effectiveness
            if self.adaptation_count > 0 and self.baseline_performance:
                current_performance = np.mean(list(self.performance_memory)[-50:]) if self.performance_memory else self.baseline_performance
                adaptation_improvement = (self.baseline_performance - current_performance) / self.baseline_performance
                metrics['adaptation_improvement'] = adaptation_improvement

            # Prediction confidence distribution
            if self.prediction_memory:
                confidences = [pred['confidence'] for pred in self.prediction_memory]
                metrics['avg_prediction_confidence'] = np.mean(confidences)
                metrics['min_prediction_confidence'] = np.min(confidences)

            return metrics

        except Exception as e:
            self.logger.error(f"Error calculating adaptive metrics: {e}")
            return {}

    def get_learning_stats(self) -> Dict[str, Any]:
        """Get comprehensive learning statistics"""
        return {
            'adaptation_count': self.adaptation_count,
            'last_adaptation': self.last_adaptation_time.isoformat() if self.last_adaptation_time else None,
            'concept_drift_detected': self.concept_drift_detected,
            'baseline_performance': self.baseline_performance,
            'memory_utilization': {
                'predictions': len(self.prediction_memory),
                'feedback': len(self.feedback_buffer),
                'performance': len(self.performance_memory)
            },
            'recent_performance': np.mean(list(self.performance_memory)[-10:]) if len(self.performance_memory) >= 10 else None
        }

    async def reset_adaptation(self):
        """Reset adaptive learning state"""
        self.prediction_memory.clear()
        self.performance_memory.clear()
        self.concept_drift_memory.clear()
        self.learning_buffer = []
        self.feedback_buffer = []
        self.adaptation_count = 0
        self.concept_drift_detected = False
        self.last_adaptation_time = None

        self.logger.info("Adaptive learning state reset")