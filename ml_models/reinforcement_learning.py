"""
Reinforcement Learning for Adaptive Trading Strategy Optimization
Implements Deep Q-Network (DQN) and Proximal Policy Optimization (PPO) for trading
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from collections import deque, namedtuple
import random
import logging
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
import gymnasium as gym
from gymnasium import spaces
import warnings
warnings.filterwarnings('ignore')

from .base_model import BaseMLModel

# Experience tuple for DQN
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])

@dataclass
class TradingState:
    """Represents the current state of the trading environment"""
    price_features: np.ndarray
    technical_indicators: np.ndarray
    portfolio_state: np.ndarray
    market_conditions: np.ndarray
    time_features: np.ndarray

class TradingEnvironment(gym.Env):
    """Custom trading environment for reinforcement learning"""

    def __init__(self,
                 data: pd.DataFrame,
                 initial_balance: float = 10000.0,
                 transaction_cost: float = 0.001,
                 max_position: float = 1.0):
        super().__init__()

        self.data = data.copy()
        self.initial_balance = initial_balance
        self.transaction_cost = transaction_cost
        self.max_position = max_position

        # Current state
        self.current_step = 0
        self.balance = initial_balance
        self.position = 0.0  # -1 to 1 (short to long)
        self.portfolio_value = initial_balance
        self.trades_made = 0

        # Action space: 0=hold, 1=buy, 2=sell
        self.action_space = spaces.Discrete(3)

        # State space (will be defined based on features)
        self.observation_space = None
        self._setup_state_space()

        # Performance tracking
        self.portfolio_history = []
        self.position_history = []
        self.trade_history = []

    def _setup_state_space(self):
        """Setup the observation space based on available features"""
        # Calculate features for state space sizing
        sample_state = self._get_state(0)
        state_size = len(sample_state)

        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(state_size,),
            dtype=np.float32
        )

    def _get_state(self, step: int) -> np.ndarray:
        """Get the current state representation"""
        if step >= len(self.data):
            step = len(self.data) - 1

        # Price features (OHLCV)
        price_features = []
        if step > 0:
            price_features.extend([
                self.data.iloc[step]['open_price'],
                self.data.iloc[step]['high_price'],
                self.data.iloc[step]['low_price'],
                self.data.iloc[step]['close_price'],
                self.data.iloc[step]['volume'] / 1e6,  # Normalize volume
            ])
        else:
            price_features.extend([100.0, 100.0, 100.0, 100.0, 1.0])

        # Technical indicators
        technical_features = []
        if 'sma_20' in self.data.columns:
            technical_features.append(self.data.iloc[step]['sma_20'] if step < len(self.data) else 100.0)
        if 'rsi' in self.data.columns:
            technical_features.append(self.data.iloc[step]['rsi'] / 100.0 if step < len(self.data) else 0.5)
        if 'macd' in self.data.columns:
            technical_features.append(self.data.iloc[step]['macd'] if step < len(self.data) else 0.0)

        # Portfolio state
        portfolio_features = [
            self.balance / self.initial_balance,  # Normalized balance
            self.position,  # Current position
            self.portfolio_value / self.initial_balance,  # Normalized portfolio value
            self.trades_made / 100.0,  # Normalized trade count
        ]

        # Market conditions (volatility, trend)
        market_features = []
        if step >= 20:
            recent_prices = self.data.iloc[step-20:step]['close_price'].values
            volatility = np.std(recent_prices) / np.mean(recent_prices)
            trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            market_features.extend([volatility, trend])
        else:
            market_features.extend([0.02, 0.0])  # Default values

        # Time features
        time_features = [
            step / len(self.data),  # Progress through episode
        ]

        # Combine all features
        state = np.array(
            price_features + technical_features + portfolio_features + market_features + time_features,
            dtype=np.float32
        )

        return state

    def reset(self, seed=None):
        """Reset the environment"""
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)

        self.current_step = 20  # Start after we have enough data for indicators
        self.balance = self.initial_balance
        self.position = 0.0
        self.portfolio_value = self.initial_balance
        self.trades_made = 0

        self.portfolio_history = [self.portfolio_value]
        self.position_history = [self.position]
        self.trade_history = []

        return self._get_state(self.current_step), {}

    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """Execute one step in the environment"""
        prev_portfolio_value = self.portfolio_value
        prev_position = self.position

        # Execute action
        self._execute_action(action)

        # Calculate reward
        reward = self._calculate_reward(prev_portfolio_value, prev_position, action)

        # Update state
        self.current_step += 1
        done = self.current_step >= len(self.data) - 1

        # Get next state
        next_state = self._get_state(self.current_step)

        # Update history
        self.portfolio_history.append(self.portfolio_value)
        self.position_history.append(self.position)

        info = {
            'portfolio_value': self.portfolio_value,
            'position': self.position,
            'balance': self.balance,
            'trades_made': self.trades_made
        }

        return next_state, reward, done, False, info

    def _execute_action(self, action: int):
        """Execute the trading action"""
        current_price = self.data.iloc[self.current_step]['close_price']

        if action == 1:  # Buy
            if self.position < self.max_position:
                # Calculate how much to buy
                buy_amount = min(0.1, self.max_position - self.position)  # Buy 10% or up to max
                cost = buy_amount * current_price * (1 + self.transaction_cost)

                if self.balance >= cost:
                    self.balance -= cost
                    self.position += buy_amount
                    self.trades_made += 1

                    self.trade_history.append({
                        'step': self.current_step,
                        'action': 'buy',
                        'amount': buy_amount,
                        'price': current_price,
                        'cost': cost
                    })

        elif action == 2:  # Sell
            if self.position > -self.max_position:
                # Calculate how much to sell
                sell_amount = min(0.1, self.position + self.max_position)  # Sell 10% or up to max short
                proceeds = sell_amount * current_price * (1 - self.transaction_cost)

                self.balance += proceeds
                self.position -= sell_amount
                self.trades_made += 1

                self.trade_history.append({
                    'step': self.current_step,
                    'action': 'sell',
                    'amount': sell_amount,
                    'price': current_price,
                    'proceeds': proceeds
                })

        # Update portfolio value
        current_price = self.data.iloc[self.current_step]['close_price']
        position_value = self.position * current_price
        self.portfolio_value = self.balance + position_value

    def _calculate_reward(self, prev_portfolio_value: float, prev_position: float, action: int) -> float:
        """Calculate the reward for the action taken"""
        # Portfolio return
        portfolio_return = (self.portfolio_value - prev_portfolio_value) / prev_portfolio_value

        # Base reward is portfolio return
        reward = portfolio_return * 100  # Scale for better learning

        # Penalty for excessive trading
        if action != 0:  # If not holding
            reward -= 0.1  # Small penalty for trading

        # Penalty for extreme positions
        if abs(self.position) > 0.8:
            reward -= 0.2

        # Bonus for profitable trades
        if len(self.trade_history) > 0:
            last_trade = self.trade_history[-1]
            if last_trade['step'] == self.current_step:
                # Recent trade, check if it was profitable
                if action == 2 and self.position < prev_position:  # Sold
                    # Calculate profit from this trade
                    if portfolio_return > 0:
                        reward += 1.0  # Bonus for profitable trade

        # Risk adjustment
        if len(self.portfolio_history) >= 10:
            recent_returns = np.diff(self.portfolio_history[-10:]) / self.portfolio_history[-11:-1]
            volatility = np.std(recent_returns)
            if volatility > 0:
                sharpe = np.mean(recent_returns) / volatility
                reward += sharpe * 0.1  # Small Sharpe ratio bonus

        return reward

    def get_performance_metrics(self) -> Dict[str, float]:
        """Calculate performance metrics"""
        if len(self.portfolio_history) < 2:
            return {}

        # Portfolio returns
        portfolio_values = np.array(self.portfolio_history)
        returns = np.diff(portfolio_values) / portfolio_values[:-1]

        # Total return
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]

        # Sharpe ratio
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0

        # Maximum drawdown
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown)

        # Win rate
        profitable_trades = sum(1 for trade in self.trade_history
                              if 'proceeds' in trade and trade['proceeds'] > trade.get('cost', 0))
        win_rate = profitable_trades / len(self.trade_history) if self.trade_history else 0

        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'trades_made': self.trades_made,
            'final_portfolio_value': portfolio_values[-1]
        }

class DQNNetwork(nn.Module):
    """Deep Q-Network for trading decisions"""

    def __init__(self, state_size: int, action_size: int, hidden_size: int = 256):
        super().__init__()

        self.network = nn.Sequential(
            nn.Linear(state_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, action_size)
        )

    def forward(self, x):
        return self.network(x)

class DQNAgent:
    """Deep Q-Network Agent for trading"""

    def __init__(self,
                 state_size: int,
                 action_size: int,
                 learning_rate: float = 0.001,
                 gamma: float = 0.95,
                 epsilon: float = 1.0,
                 epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01,
                 memory_size: int = 10000,
                 batch_size: int = 32):

        self.state_size = state_size
        self.action_size = action_size
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.batch_size = batch_size

        # Neural networks
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.q_network = DQNNetwork(state_size, action_size).to(self.device)
        self.target_network = DQNNetwork(state_size, action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)

        # Experience replay
        self.memory = deque(maxlen=memory_size)

        # Update target network
        self.update_target_network()

    def update_target_network(self):
        """Copy weights from main network to target network"""
        self.target_network.load_state_dict(self.q_network.state_dict())

    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay buffer"""
        self.memory.append(Experience(state, action, reward, next_state, done))

    def act(self, state, training=True):
        """Choose action using epsilon-greedy policy"""
        if training and random.random() <= self.epsilon:
            return random.choice(range(self.action_size))

        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        return q_values.argmax().item()

    def replay(self):
        """Train the network on a batch of experiences"""
        if len(self.memory) < self.batch_size:
            return

        # Sample batch
        batch = random.sample(self.memory, self.batch_size)
        states = torch.FloatTensor([e.state for e in batch]).to(self.device)
        actions = torch.LongTensor([e.action for e in batch]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in batch]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in batch]).to(self.device)
        dones = torch.BoolTensor([e.done for e in batch]).to(self.device)

        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))

        # Next Q values from target network
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        # Compute loss
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)

        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()

        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        return loss.item()

class PPOAgent:
    """Proximal Policy Optimization Agent"""

    def __init__(self,
                 state_size: int,
                 action_size: int,
                 learning_rate: float = 0.0003,
                 gamma: float = 0.99,
                 lambda_gae: float = 0.95,
                 clip_ratio: float = 0.2,
                 value_coef: float = 0.5,
                 entropy_coef: float = 0.01):

        self.state_size = state_size
        self.action_size = action_size
        self.gamma = gamma
        self.lambda_gae = lambda_gae
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Actor-Critic network
        self.actor_critic = self._build_actor_critic().to(self.device)
        self.optimizer = optim.Adam(self.actor_critic.parameters(), lr=learning_rate)

        # Storage for rollouts
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []
        self.dones = []

    def _build_actor_critic(self):
        """Build Actor-Critic network"""
        class ActorCritic(nn.Module):
            def __init__(self, state_size, action_size, hidden_size=256):
                super().__init__()

                # Shared layers
                self.shared = nn.Sequential(
                    nn.Linear(state_size, hidden_size),
                    nn.ReLU(),
                    nn.Linear(hidden_size, hidden_size),
                    nn.ReLU()
                )

                # Actor head (policy)
                self.actor = nn.Linear(hidden_size, action_size)

                # Critic head (value function)
                self.critic = nn.Linear(hidden_size, 1)

            def forward(self, x):
                shared_features = self.shared(x)
                action_logits = self.actor(shared_features)
                value = self.critic(shared_features)

                return action_logits, value

        return ActorCritic(self.state_size, self.action_size)

    def act(self, state):
        """Choose action and value"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)

        with torch.no_grad():
            action_logits, value = self.actor_critic(state_tensor)
            action_probs = F.softmax(action_logits, dim=-1)
            action_dist = torch.distributions.Categorical(action_probs)
            action = action_dist.sample()
            log_prob = action_dist.log_prob(action)

        return action.item(), value.item(), log_prob.item()

    def store_transition(self, state, action, reward, value, log_prob, done):
        """Store transition"""
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.values.append(value)
        self.log_probs.append(log_prob)
        self.dones.append(done)

    def update(self):
        """Update the policy using PPO"""
        if len(self.states) == 0:
            return

        # Convert to tensors
        states = torch.FloatTensor(self.states).to(self.device)
        actions = torch.LongTensor(self.actions).to(self.device)
        old_log_probs = torch.FloatTensor(self.log_probs).to(self.device)
        rewards = np.array(self.rewards)
        values = np.array(self.values)
        dones = np.array(self.dones)

        # Calculate advantages using GAE
        advantages = self._calculate_gae(rewards, values, dones)
        returns = advantages + values[:-1]  # Remove last value

        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        # Convert to tensors
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)

        # PPO update
        for _ in range(4):  # Multiple epochs
            # Get current policy
            action_logits, current_values = self.actor_critic(states[:-1])
            action_probs = F.softmax(action_logits, dim=-1)
            action_dist = torch.distributions.Categorical(action_probs)

            new_log_probs = action_dist.log_prob(actions)
            entropy = action_dist.entropy().mean()

            # Calculate ratio
            ratio = torch.exp(new_log_probs - old_log_probs)

            # Calculate surrogate loss
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * advantages
            actor_loss = -torch.min(surr1, surr2).mean()

            # Value loss
            value_loss = F.mse_loss(current_values.squeeze(), returns)

            # Total loss
            total_loss = actor_loss + self.value_coef * value_loss - self.entropy_coef * entropy

            # Update
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.actor_critic.parameters(), 0.5)
            self.optimizer.step()

        # Clear storage
        self.clear_storage()

    def _calculate_gae(self, rewards, values, dones):
        """Calculate Generalized Advantage Estimation"""
        advantages = np.zeros_like(rewards)
        advantage = 0

        for t in reversed(range(len(rewards) - 1)):
            if t == len(rewards) - 2:
                next_value = 0 if dones[t + 1] else values[t + 1]
            else:
                next_value = values[t + 1]

            delta = rewards[t] + self.gamma * next_value * (1 - dones[t]) - values[t]
            advantage = delta + self.gamma * self.lambda_gae * (1 - dones[t]) * advantage
            advantages[t] = advantage

        return advantages

    def clear_storage(self):
        """Clear stored transitions"""
        self.states = []
        self.actions = []
        self.rewards = []
        self.values = []
        self.log_probs = []
        self.dones = []

class ReinforcementLearningTrader(BaseMLModel):
    """Reinforcement Learning Trading Agent"""

    def __init__(self,
                 model_id: str,
                 algorithm: str = "DQN",  # "DQN" or "PPO"
                 **kwargs):
        super().__init__(model_id, f"RL_{algorithm}", "1.0")

        self.algorithm = algorithm
        self.environment = None
        self.agent = None

        # RL hyperparameters
        self.hyperparameters = {
            'algorithm': algorithm,
            'episodes': 1000,
            'learning_rate': 0.001,
            'gamma': 0.95,
            'epsilon': 1.0,
            'epsilon_decay': 0.995,
            'epsilon_min': 0.01,
            'batch_size': 32,
            'memory_size': 10000,
            'target_update_freq': 100,
            'initial_balance': 10000.0,
            'transaction_cost': 0.001,
            'max_position': 1.0
        }

        self.hyperparameters.update(kwargs)
        self.metadata['hyperparameters'] = self.hyperparameters

        # Training history
        self.training_history = {
            'episode_rewards': [],
            'portfolio_values': [],
            'episode_lengths': [],
            'epsilon_history': [],
            'loss_history': []
        }

    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the reinforcement learning agent"""
        try:
            self.logger.info(f"Starting RL training with {self.algorithm} algorithm")

            # Update hyperparameters
            self.hyperparameters.update(kwargs)

            # Prepare training data with technical indicators
            training_data_eng = self._add_technical_indicators(training_data)

            # Create environment
            self.environment = TradingEnvironment(
                data=training_data_eng,
                initial_balance=self.hyperparameters['initial_balance'],
                transaction_cost=self.hyperparameters['transaction_cost'],
                max_position=self.hyperparameters['max_position']
            )

            # Initialize agent
            state_size = self.environment.observation_space.shape[0]
            action_size = self.environment.action_space.n

            if self.algorithm == "DQN":
                self.agent = DQNAgent(
                    state_size=state_size,
                    action_size=action_size,
                    learning_rate=self.hyperparameters['learning_rate'],
                    gamma=self.hyperparameters['gamma'],
                    epsilon=self.hyperparameters['epsilon'],
                    epsilon_decay=self.hyperparameters['epsilon_decay'],
                    epsilon_min=self.hyperparameters['epsilon_min'],
                    memory_size=self.hyperparameters['memory_size'],
                    batch_size=self.hyperparameters['batch_size']
                )
            elif self.algorithm == "PPO":
                self.agent = PPOAgent(
                    state_size=state_size,
                    action_size=action_size,
                    learning_rate=self.hyperparameters['learning_rate'],
                    gamma=self.hyperparameters['gamma']
                )
            else:
                raise ValueError(f"Unknown algorithm: {self.algorithm}")

            # Training loop
            best_reward = float('-inf')
            episodes = self.hyperparameters['episodes']

            for episode in range(episodes):
                state, _ = self.environment.reset()
                episode_reward = 0
                episode_length = 0
                episode_losses = []

                while True:
                    # Choose action
                    if self.algorithm == "DQN":
                        action = self.agent.act(state, training=True)
                    else:  # PPO
                        action, value, log_prob = self.agent.act(state)

                    # Take step
                    next_state, reward, done, _, info = self.environment.step(action)

                    episode_reward += reward
                    episode_length += 1

                    # Store experience
                    if self.algorithm == "DQN":
                        self.agent.remember(state, action, reward, next_state, done)

                        # Train agent
                        if len(self.agent.memory) > self.agent.batch_size:
                            loss = self.agent.replay()
                            if loss is not None:
                                episode_losses.append(loss)

                        # Update target network
                        if episode % self.hyperparameters['target_update_freq'] == 0:
                            self.agent.update_target_network()

                    else:  # PPO
                        self.agent.store_transition(state, action, reward, value, log_prob, done)

                    state = next_state

                    if done:
                        break

                # PPO update at end of episode
                if self.algorithm == "PPO":
                    self.agent.update()

                # Record training history
                self.training_history['episode_rewards'].append(episode_reward)
                self.training_history['portfolio_values'].append(info['portfolio_value'])
                self.training_history['episode_lengths'].append(episode_length)

                if self.algorithm == "DQN":
                    self.training_history['epsilon_history'].append(self.agent.epsilon)

                if episode_losses:
                    self.training_history['loss_history'].append(np.mean(episode_losses))

                # Logging
                if episode % 100 == 0:
                    avg_reward = np.mean(self.training_history['episode_rewards'][-100:])
                    avg_portfolio = np.mean(self.training_history['portfolio_values'][-100:])

                    self.logger.info(f"Episode {episode}/{episodes}, "
                                   f"Avg Reward: {avg_reward:.2f}, "
                                   f"Avg Portfolio: {avg_portfolio:.2f}")

                    if self.algorithm == "DQN":
                        self.logger.info(f"Epsilon: {self.agent.epsilon:.3f}")

                # Save best model
                if episode_reward > best_reward:
                    best_reward = episode_reward
                    self._save_best_model()

            self.is_trained = True

            # Calculate final metrics
            final_metrics = self._calculate_training_metrics()

            self.logger.info(f"RL training completed. Best reward: {best_reward:.2f}")

            return {
                'status': 'success',
                'metrics': final_metrics,
                'episodes_trained': episodes,
                'best_reward': best_reward
            }

        except Exception as e:
            self.logger.error(f"Error during RL training: {e}")
            return {'status': 'error', 'message': str(e)}

    async def predict(self,
                     input_data: pd.DataFrame,
                     **kwargs) -> np.ndarray:
        """Make trading decisions using the trained RL agent"""
        try:
            if not self.is_trained or self.agent is None:
                raise ValueError("Agent must be trained before making predictions")

            # Prepare data
            data_eng = self._add_technical_indicators(input_data)

            # Create environment for prediction
            pred_env = TradingEnvironment(
                data=data_eng,
                initial_balance=self.hyperparameters['initial_balance'],
                transaction_cost=self.hyperparameters['transaction_cost'],
                max_position=self.hyperparameters['max_position']
            )

            # Get predictions
            state, _ = pred_env.reset()
            actions = []

            while True:
                if self.algorithm == "DQN":
                    action = self.agent.act(state, training=False)
                else:  # PPO
                    action, _, _ = self.agent.act(state)

                actions.append(action)

                next_state, _, done, _, _ = pred_env.step(action)
                state = next_state

                if done:
                    break

            return np.array(actions)

        except Exception as e:
            self.logger.error(f"Error during prediction: {e}")
            return np.array([])

    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to the data"""
        data_eng = data.copy()

        try:
            # Moving averages
            data_eng['sma_20'] = data_eng['close_price'].rolling(20).mean()
            data_eng['ema_20'] = data_eng['close_price'].ewm(span=20).mean()

            # RSI
            delta = data_eng['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data_eng['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = data_eng['close_price'].ewm(span=12).mean()
            exp2 = data_eng['close_price'].ewm(span=26).mean()
            data_eng['macd'] = exp1 - exp2

            # Fill NaN values
            data_eng = data_eng.fillna(method='bfill').fillna(method='ffill')

        except Exception as e:
            self.logger.warning(f"Error adding technical indicators: {e}")

        return data_eng

    def _calculate_training_metrics(self) -> Dict[str, float]:
        """Calculate training performance metrics"""
        if not self.training_history['episode_rewards']:
            return {}

        episode_rewards = np.array(self.training_history['episode_rewards'])
        portfolio_values = np.array(self.training_history['portfolio_values'])

        metrics = {
            'avg_episode_reward': np.mean(episode_rewards),
            'best_episode_reward': np.max(episode_rewards),
            'final_portfolio_value': portfolio_values[-1],
            'max_portfolio_value': np.max(portfolio_values),
            'portfolio_volatility': np.std(portfolio_values),
            'reward_trend': np.polyfit(range(len(episode_rewards)), episode_rewards, 1)[0]
        }

        return metrics

    def _save_best_model(self):
        """Save the best model state"""
        if self.algorithm == "DQN":
            self.best_model_state = {
                'q_network': self.agent.q_network.state_dict(),
                'target_network': self.agent.target_network.state_dict(),
                'optimizer': self.agent.optimizer.state_dict()
            }
        else:  # PPO
            self.best_model_state = {
                'actor_critic': self.agent.actor_critic.state_dict(),
                'optimizer': self.agent.optimizer.state_dict()
            }

    def get_trading_strategy(self, confidence_threshold: float = 0.6) -> Dict[str, Any]:
        """Get the learned trading strategy"""
        if not self.is_trained:
            return {}

        # Analyze the learned policy
        strategy_summary = {
            'algorithm': self.algorithm,
            'confidence_threshold': confidence_threshold,
            'training_episodes': len(self.training_history['episode_rewards']),
            'performance_metrics': self._calculate_training_metrics()
        }

        return strategy_summary

    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate the RL agent on test data"""
        try:
            # Prepare test data
            test_data_eng = self._add_technical_indicators(test_data)

            # Create test environment
            test_env = TradingEnvironment(
                data=test_data_eng,
                initial_balance=self.hyperparameters['initial_balance'],
                transaction_cost=self.hyperparameters['transaction_cost'],
                max_position=self.hyperparameters['max_position']
            )

            # Run evaluation episode
            state, _ = test_env.reset()
            total_reward = 0

            while True:
                if self.algorithm == "DQN":
                    action = self.agent.act(state, training=False)
                else:  # PPO
                    action, _, _ = self.agent.act(state)

                next_state, reward, done, _, info = test_env.step(action)
                total_reward += reward
                state = next_state

                if done:
                    break

            # Get performance metrics from environment
            env_metrics = test_env.get_performance_metrics()

            # Combine with evaluation metrics
            metrics = {
                'test_total_reward': total_reward,
                **env_metrics
            }

            self.logger.info(f"RL evaluation completed. Total reward: {total_reward:.2f}, "
                           f"Final portfolio: {env_metrics.get('final_portfolio_value', 0):.2f}")

            return metrics

        except Exception as e:
            self.logger.error(f"Error during evaluation: {e}")
            return {'error': str(e)}

    def save_model(self, filepath: str):
        """Save the RL model"""
        model_data = {
            'algorithm': self.algorithm,
            'hyperparameters': self.hyperparameters,
            'training_history': self.training_history,
            'is_trained': self.is_trained
        }

        if hasattr(self, 'best_model_state'):
            model_data['best_model_state'] = self.best_model_state

        torch.save(model_data, filepath)

    def load_model(self, filepath: str):
        """Load the RL model"""
        model_data = torch.load(filepath, map_location='cpu')

        self.algorithm = model_data['algorithm']
        self.hyperparameters = model_data['hyperparameters']
        self.training_history = model_data['training_history']
        self.is_trained = model_data['is_trained']

        # Recreate agent if model state is available
        if 'best_model_state' in model_data:
            # This would require recreating the agent with proper state size
            # Implementation depends on how you want to handle model loading
            pass