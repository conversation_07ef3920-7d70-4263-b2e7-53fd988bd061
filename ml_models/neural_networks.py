"""
Advanced Neural Network Models for Trading Predictions
Implements state-of-the-art neural architectures including attention mechanisms,
transformers, and graph neural networks for financial time series
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import math
import warnings
warnings.filterwarnings('ignore')

@dataclass
class ModelConfig:
    """Neural network model configuration"""
    input_dim: int
    hidden_dim: int = 256
    num_layers: int = 3
    dropout: float = 0.1
    learning_rate: float = 1e-3
    batch_size: int = 64
    num_epochs: int = 100
    sequence_length: int = 60
    prediction_horizon: int = 1
    attention_heads: int = 8
    use_residual: bool = True
    use_batch_norm: bool = True

class TimeSeriesDataset(Dataset):
    """Custom dataset for time series data"""

    def __init__(self, data: np.ndarray, sequence_length: int, prediction_horizon: int):
        self.data = data
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon

    def __len__(self):
        return len(self.data) - self.sequence_length - self.prediction_horizon + 1

    def __getitem__(self, idx):
        x = self.data[idx:idx + self.sequence_length]
        y = self.data[idx + self.sequence_length:idx + self.sequence_length + self.prediction_horizon]
        return torch.FloatTensor(x), torch.FloatTensor(y)

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models"""

    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)

        self.register_buffer('pe', pe)

    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism"""

    def __init__(self, d_model: int, num_heads: int):
        super().__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)

    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        attn_scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)

        if mask is not None:
            attn_scores = attn_scores.masked_fill(mask == 0, -1e9)

        attn_probs = torch.softmax(attn_scores, dim=-1)
        output = torch.matmul(attn_probs, V)
        return output

    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)

        Q = self.W_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.W_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.W_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)

        attn_output = self.scaled_dot_product_attention(Q, K, V, mask)
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model
        )

        return self.W_o(attn_output)

class TransformerBlock(nn.Module):
    """Transformer encoder block"""

    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float):
        super().__init__()

        self.attention = MultiHeadAttention(d_model, num_heads)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model)
        )

        self.dropout = nn.Dropout(dropout)

    def forward(self, x, mask=None):
        # Multi-head attention
        attn_output = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))

        # Feed forward
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))

        return x

class AdvancedLSTM(nn.Module):
    """Advanced LSTM with attention and residual connections"""

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config

        # Input projection
        self.input_projection = nn.Linear(config.input_dim, config.hidden_dim)

        # LSTM layers
        self.lstm_layers = nn.ModuleList([
            nn.LSTM(
                input_size=config.hidden_dim if i == 0 else config.hidden_dim,
                hidden_size=config.hidden_dim,
                batch_first=True,
                dropout=config.dropout if i < config.num_layers - 1 else 0
            )
            for i in range(config.num_layers)
        ])

        # Attention mechanism
        self.attention = MultiHeadAttention(config.hidden_dim, config.attention_heads)

        # Batch normalization
        if config.use_batch_norm:
            self.batch_norms = nn.ModuleList([
                nn.BatchNorm1d(config.hidden_dim) for _ in range(config.num_layers)
            ])

        # Output layers
        self.output_projection = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, config.prediction_horizon)
        )

    def forward(self, x):
        batch_size, seq_len, _ = x.shape

        # Input projection
        x = self.input_projection(x)

        # LSTM layers with residual connections
        for i, lstm in enumerate(self.lstm_layers):
            residual = x
            x, _ = lstm(x)

            if self.config.use_batch_norm:
                x = x.transpose(1, 2)
                x = self.batch_norms[i](x)
                x = x.transpose(1, 2)

            if self.config.use_residual and i > 0:
                x = x + residual

        # Apply attention
        x = self.attention(x, x, x)

        # Take the last output for prediction
        x = x[:, -1, :]

        # Output projection
        output = self.output_projection(x)

        return output

class FinancialTransformer(nn.Module):
    """Financial time series transformer with market-specific adaptations"""

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config

        # Input embedding
        self.input_embedding = nn.Linear(config.input_dim, config.hidden_dim)

        # Positional encoding
        self.pos_encoding = PositionalEncoding(config.hidden_dim, config.sequence_length)

        # Transformer blocks
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(
                d_model=config.hidden_dim,
                num_heads=config.attention_heads,
                d_ff=config.hidden_dim * 4,
                dropout=config.dropout
            )
            for _ in range(config.num_layers)
        ])

        # Market regime detection
        self.regime_classifier = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 4)  # 4 market regimes
        )

        # Volatility prediction
        self.volatility_predictor = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)
        )

        # Price prediction
        self.price_predictor = nn.Sequential(
            nn.Linear(config.hidden_dim + 4 + 1, config.hidden_dim // 2),  # +4 for regime, +1 for volatility
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, config.prediction_horizon)
        )

        self.dropout = nn.Dropout(config.dropout)

    def forward(self, x):
        batch_size, seq_len, _ = x.shape

        # Input embedding
        x = self.input_embedding(x)

        # Add positional encoding
        x = x.transpose(0, 1)
        x = self.pos_encoding(x)
        x = x.transpose(0, 1)

        x = self.dropout(x)

        # Apply transformer blocks
        for transformer in self.transformer_blocks:
            x = transformer(x)

        # Use the last token for prediction
        last_hidden = x[:, -1, :]

        # Predict market regime
        regime_logits = self.regime_classifier(last_hidden)
        regime_probs = F.softmax(regime_logits, dim=-1)

        # Predict volatility
        volatility = self.volatility_predictor(last_hidden)

        # Combine features for price prediction
        combined_features = torch.cat([last_hidden, regime_probs, volatility], dim=-1)
        price_prediction = self.price_predictor(combined_features)

        return {
            'price': price_prediction,
            'volatility': volatility,
            'regime': regime_probs
        }

class ConvolutionalLSTM(nn.Module):
    """Convolutional LSTM for capturing spatial-temporal patterns"""

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config

        # 1D Convolutional layers for feature extraction
        self.conv_layers = nn.Sequential(
            nn.Conv1d(config.input_dim, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm1d(64),
            nn.Dropout(config.dropout),

            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm1d(128),
            nn.Dropout(config.dropout),

            nn.Conv1d(128, config.hidden_dim, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.BatchNorm1d(config.hidden_dim)
        )

        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=config.hidden_dim,
            hidden_size=config.hidden_dim,
            num_layers=config.num_layers,
            batch_first=True,
            dropout=config.dropout if config.num_layers > 1 else 0,
            bidirectional=True
        )

        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=config.hidden_dim * 2,  # *2 for bidirectional
            num_heads=config.attention_heads,
            dropout=config.dropout,
            batch_first=True
        )

        # Output layer
        self.output_layer = nn.Sequential(
            nn.Linear(config.hidden_dim * 2, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.prediction_horizon)
        )

    def forward(self, x):
        batch_size, seq_len, features = x.shape

        # Apply convolution (need to transpose for Conv1d)
        x = x.transpose(1, 2)  # (batch, features, seq_len)
        x = self.conv_layers(x)
        x = x.transpose(1, 2)  # (batch, seq_len, hidden_dim)

        # LSTM processing
        lstm_out, _ = self.lstm(x)

        # Self-attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # Take the last output
        last_output = attn_out[:, -1, :]

        # Final prediction
        prediction = self.output_layer(last_output)

        return prediction

class GraphNeuralNetwork(nn.Module):
    """Graph neural network for modeling market relationships"""

    def __init__(self, config: ModelConfig, num_assets: int):
        super().__init__()
        self.config = config
        self.num_assets = num_assets

        # Node feature embedding
        self.node_embedding = nn.Linear(config.input_dim, config.hidden_dim)

        # Graph convolution layers
        self.graph_convs = nn.ModuleList([
            GraphConvolution(config.hidden_dim, config.hidden_dim)
            for _ in range(config.num_layers)
        ])

        # Temporal processing
        self.temporal_conv = nn.Conv1d(
            config.hidden_dim, config.hidden_dim,
            kernel_size=3, padding=1
        )

        # Output layer
        self.output_layer = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, config.prediction_horizon)
        )

    def forward(self, node_features, adjacency_matrix):
        batch_size, seq_len, num_nodes, features = node_features.shape

        # Process each time step
        outputs = []
        for t in range(seq_len):
            x = node_features[:, t, :, :]  # (batch, num_nodes, features)

            # Node embedding
            x = self.node_embedding(x)

            # Graph convolutions
            for conv in self.graph_convs:
                x = conv(x, adjacency_matrix)
                x = F.relu(x)

            outputs.append(x)

        # Stack temporal outputs
        temporal_features = torch.stack(outputs, dim=1)  # (batch, seq_len, num_nodes, hidden)

        # Apply temporal convolution
        # Reshape for Conv1d: (batch * num_nodes, hidden, seq_len)
        batch_size, seq_len, num_nodes, hidden_dim = temporal_features.shape
        temporal_features = temporal_features.permute(0, 2, 3, 1).reshape(-1, hidden_dim, seq_len)

        temporal_features = self.temporal_conv(temporal_features)

        # Reshape back: (batch, num_nodes, hidden, seq_len)
        temporal_features = temporal_features.reshape(batch_size, num_nodes, hidden_dim, seq_len)
        temporal_features = temporal_features.permute(0, 3, 1, 2)  # (batch, seq_len, num_nodes, hidden)

        # Take last time step and average over nodes
        final_features = temporal_features[:, -1, :, :].mean(dim=1)  # (batch, hidden)

        # Final prediction
        prediction = self.output_layer(final_features)

        return prediction

class GraphConvolution(nn.Module):
    """Graph convolution layer"""

    def __init__(self, input_dim: int, output_dim: int):
        super().__init__()
        self.linear = nn.Linear(input_dim, output_dim)

    def forward(self, x, adjacency_matrix):
        # x: (batch, num_nodes, features)
        # adjacency_matrix: (num_nodes, num_nodes)

        # Apply linear transformation
        x = self.linear(x)

        # Apply graph convolution: AXW
        output = torch.matmul(adjacency_matrix, x)

        return output

class EnsembleNeuralNetwork(nn.Module):
    """Ensemble of different neural network architectures"""

    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config

        # Different neural network models
        self.lstm_model = AdvancedLSTM(config)
        self.transformer_model = FinancialTransformer(config)
        self.conv_lstm_model = ConvolutionalLSTM(config)

        # Ensemble weights (learnable)
        self.ensemble_weights = nn.Parameter(torch.ones(3) / 3)

        # Meta-learner for combining predictions
        self.meta_learner = nn.Sequential(
            nn.Linear(3 * config.prediction_horizon, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.prediction_horizon)
        )

    def forward(self, x):
        # Get predictions from individual models
        lstm_pred = self.lstm_model(x)
        transformer_pred = self.transformer_model(x)['price']
        conv_lstm_pred = self.conv_lstm_model(x)

        # Weighted ensemble
        weights = F.softmax(self.ensemble_weights, dim=0)
        weighted_pred = (weights[0] * lstm_pred +
                        weights[1] * transformer_pred +
                        weights[2] * conv_lstm_pred)

        # Meta-learning approach
        combined_features = torch.cat([lstm_pred, transformer_pred, conv_lstm_pred], dim=-1)
        meta_pred = self.meta_learner(combined_features)

        return {
            'weighted_ensemble': weighted_pred,
            'meta_learner': meta_pred,
            'individual_predictions': {
                'lstm': lstm_pred,
                'transformer': transformer_pred,
                'conv_lstm': conv_lstm_pred
            },
            'weights': weights
        }

class NeuralNetworkTrainer:
    """Advanced neural network trainer with sophisticated training strategies"""

    def __init__(self, model: nn.Module, config: ModelConfig):
        self.model = model
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)

        # Optimizer with learning rate scheduling
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-5
        )

        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )

        # Loss functions
        self.mse_loss = nn.MSELoss()
        self.mae_loss = nn.L1Loss()
        self.huber_loss = nn.SmoothL1Loss()

        self.logger = logging.getLogger("NeuralNetworkTrainer")

    def train(self, train_loader: DataLoader, val_loader: DataLoader) -> Dict[str, List[float]]:
        """Train the neural network with advanced training strategies"""

        train_losses = []
        val_losses = []
        best_val_loss = float('inf')
        patience_counter = 0

        for epoch in range(self.config.num_epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0

            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(self.device), target.to(self.device)

                self.optimizer.zero_grad()

                # Forward pass
                if isinstance(self.model, (FinancialTransformer, EnsembleNeuralNetwork)):
                    output = self.model(data)
                    if isinstance(output, dict):
                        prediction = output.get('price', output.get('weighted_ensemble', output))
                    else:
                        prediction = output
                else:
                    prediction = self.model(data)

                # Calculate loss with multiple components
                mse_loss = self.mse_loss(prediction, target)
                mae_loss = self.mae_loss(prediction, target)

                # Combined loss
                loss = 0.7 * mse_loss + 0.3 * mae_loss

                # Add regularization for ensemble models
                if isinstance(self.model, EnsembleNeuralNetwork):
                    # Encourage diversity in ensemble
                    individual_preds = output['individual_predictions']
                    diversity_loss = 0
                    for i, pred1 in enumerate(individual_preds.values()):
                        for j, pred2 in enumerate(individual_preds.values()):
                            if i < j:
                                diversity_loss += torch.mean((pred1 - pred2) ** 2)

                    loss += 0.1 * diversity_loss

                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()
                train_loss += loss.item()

            train_loss /= len(train_loader)
            train_losses.append(train_loss)

            # Validation phase
            val_loss = self.validate(val_loader)
            val_losses.append(val_loss)

            # Learning rate scheduling
            self.scheduler.step(val_loss)

            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(self.model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1

            if patience_counter >= 20:  # Early stopping patience
                self.logger.info(f"Early stopping at epoch {epoch}")
                break

            if epoch % 10 == 0:
                self.logger.info(f"Epoch {epoch}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

        # Load best model
        self.model.load_state_dict(torch.load('best_model.pth'))

        return {
            'train_losses': train_losses,
            'val_losses': val_losses
        }

    def validate(self, val_loader: DataLoader) -> float:
        """Validate the model"""
        self.model.eval()
        val_loss = 0.0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)

                if isinstance(self.model, (FinancialTransformer, EnsembleNeuralNetwork)):
                    output = self.model(data)
                    if isinstance(output, dict):
                        prediction = output.get('price', output.get('weighted_ensemble', output))
                    else:
                        prediction = output
                else:
                    prediction = self.model(data)

                loss = self.mse_loss(prediction, target)
                val_loss += loss.item()

        return val_loss / len(val_loader)

    def predict(self, data_loader: DataLoader) -> np.ndarray:
        """Make predictions"""
        self.model.eval()
        predictions = []

        with torch.no_grad():
            for data, _ in data_loader:
                data = data.to(self.device)

                if isinstance(self.model, (FinancialTransformer, EnsembleNeuralNetwork)):
                    output = self.model(data)
                    if isinstance(output, dict):
                        prediction = output.get('price', output.get('weighted_ensemble', output))
                    else:
                        prediction = output
                else:
                    prediction = self.model(data)

                predictions.append(prediction.cpu().numpy())

        return np.concatenate(predictions, axis=0)

class NeuralNetworkModelManager:
    """Manager for neural network models with automated training and selection"""

    def __init__(self, config: ModelConfig):
        self.config = config
        self.models = {}
        self.trainers = {}
        self.scalers = {}
        self.logger = logging.getLogger("NeuralNetworkModelManager")

    def create_model(self, model_type: str, **kwargs) -> nn.Module:
        """Create a neural network model"""
        if model_type == "advanced_lstm":
            return AdvancedLSTM(self.config)
        elif model_type == "financial_transformer":
            return FinancialTransformer(self.config)
        elif model_type == "conv_lstm":
            return ConvolutionalLSTM(self.config)
        elif model_type == "ensemble":
            return EnsembleNeuralNetwork(self.config)
        elif model_type == "graph_nn":
            num_assets = kwargs.get('num_assets', 10)
            return GraphNeuralNetwork(self.config, num_assets)
        else:
            raise ValueError(f"Unknown model type: {model_type}")

    def prepare_data(self, data: pd.DataFrame) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Prepare data for neural network training"""
        # Preprocessing
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data.values)

        # Store scaler for later use
        self.scalers['main'] = scaler

        # Split data
        train_size = int(0.7 * len(scaled_data))
        val_size = int(0.15 * len(scaled_data))

        train_data = scaled_data[:train_size]
        val_data = scaled_data[train_size:train_size + val_size]
        test_data = scaled_data[train_size + val_size:]

        # Create datasets
        train_dataset = TimeSeriesDataset(train_data, self.config.sequence_length, self.config.prediction_horizon)
        val_dataset = TimeSeriesDataset(val_data, self.config.sequence_length, self.config.prediction_horizon)
        test_dataset = TimeSeriesDataset(test_data, self.config.sequence_length, self.config.prediction_horizon)

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=self.config.batch_size, shuffle=True, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=self.config.batch_size, shuffle=False, drop_last=True)
        test_loader = DataLoader(test_dataset, batch_size=self.config.batch_size, shuffle=False, drop_last=True)

        return train_loader, val_loader, test_loader

    def train_model(self, model_name: str, model_type: str, train_loader: DataLoader,
                   val_loader: DataLoader, **kwargs) -> Dict[str, Any]:
        """Train a neural network model"""

        model = self.create_model(model_type, **kwargs)
        trainer = NeuralNetworkTrainer(model, self.config)

        self.logger.info(f"Training {model_name} ({model_type})")

        # Train the model
        training_history = trainer.train(train_loader, val_loader)

        # Store model and trainer
        self.models[model_name] = model
        self.trainers[model_name] = trainer

        return {
            'model_name': model_name,
            'model_type': model_type,
            'training_history': training_history,
            'final_train_loss': training_history['train_losses'][-1],
            'final_val_loss': training_history['val_losses'][-1]
        }

    def evaluate_model(self, model_name: str, test_loader: DataLoader) -> Dict[str, float]:
        """Evaluate a trained model"""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not found")

        trainer = self.trainers[model_name]

        # Make predictions
        predictions = trainer.predict(test_loader)

        # Get actual values
        actuals = []
        for _, target in test_loader:
            actuals.append(target.numpy())
        actuals = np.concatenate(actuals, axis=0)

        # Calculate metrics
        mse = np.mean((predictions - actuals) ** 2)
        mae = np.mean(np.abs(predictions - actuals))
        rmse = np.sqrt(mse)

        # Calculate directional accuracy
        pred_direction = np.sign(np.diff(predictions.flatten()))
        actual_direction = np.sign(np.diff(actuals.flatten()))
        directional_accuracy = np.mean(pred_direction == actual_direction)

        return {
            'mse': mse,
            'mae': mae,
            'rmse': rmse,
            'directional_accuracy': directional_accuracy
        }

    def get_ensemble_prediction(self, data: torch.Tensor) -> Dict[str, np.ndarray]:
        """Get predictions from all models and create ensemble"""
        predictions = {}

        for model_name, trainer in self.trainers.items():
            trainer.model.eval()
            with torch.no_grad():
                data = data.to(trainer.device)

                if isinstance(trainer.model, (FinancialTransformer, EnsembleNeuralNetwork)):
                    output = trainer.model(data)
                    if isinstance(output, dict):
                        prediction = output.get('price', output.get('weighted_ensemble', output))
                    else:
                        prediction = output
                else:
                    prediction = trainer.model(data)

                predictions[model_name] = prediction.cpu().numpy()

        # Simple ensemble (average)
        if predictions:
            ensemble_pred = np.mean(list(predictions.values()), axis=0)
            predictions['ensemble_average'] = ensemble_pred

        return predictions

# Configuration helper
def create_neural_network_config(
    input_dim: int,
    sequence_length: int = 60,
    prediction_horizon: int = 1,
    hidden_dim: int = 256,
    num_layers: int = 3,
    learning_rate: float = 1e-3,
    batch_size: int = 64,
    num_epochs: int = 100
) -> ModelConfig:
    """Create neural network configuration"""
    return ModelConfig(
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        dropout=0.1,
        learning_rate=learning_rate,
        batch_size=batch_size,
        num_epochs=num_epochs,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        attention_heads=8,
        use_residual=True,
        use_batch_norm=True
    )