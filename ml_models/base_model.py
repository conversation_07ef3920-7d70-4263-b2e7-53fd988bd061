"""
Base Machine Learning Model for AstroA Trading System
"""

import numpy as np
import pandas as pd
import pickle
import joblib
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import logging
from pathlib import Path

class BaseMLModel(ABC):
    """Abstract base class for all ML models in the trading system"""

    def __init__(self, model_id: str, model_type: str, version: str = "1.0"):
        self.model_id = model_id
        self.model_type = model_type
        self.version = version
        self.is_trained = False
        self.model = None
        self.feature_columns = []
        self.target_columns = []
        self.training_history = []
        self.performance_metrics = {}

        # Setup logging
        self.logger = logging.getLogger(f"ML.{model_type}.{model_id}")

        # Model metadata
        self.metadata = {
            'created_at': datetime.now(),
            'last_updated': None,
            'training_samples': 0,
            'validation_accuracy': 0.0,
            'feature_importance': {},
            'hyperparameters': {}
        }

    @abstractmethod
    async def train(self,
                   training_data: pd.DataFrame,
                   validation_data: pd.DataFrame = None,
                   **kwargs) -> Dict[str, Any]:
        """Train the model with given data"""
        pass

    @abstractmethod
    async def predict(self,
                     input_data: pd.DataFrame,
                     **kwargs) -> np.ndarray:
        """Make predictions on input data"""
        pass

    @abstractmethod
    async def evaluate(self,
                      test_data: pd.DataFrame,
                      **kwargs) -> Dict[str, float]:
        """Evaluate model performance on test data"""
        pass

    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for model input"""
        try:
            # Select only the features used during training
            if self.feature_columns:
                return data[self.feature_columns].copy()
            return data.copy()
        except KeyError as e:
            self.logger.error(f"Missing feature columns: {e}")
            raise

    def save_model(self, filepath: str) -> bool:
        """Save the trained model to disk"""
        try:
            model_data = {
                'model': self.model,
                'model_id': self.model_id,
                'model_type': self.model_type,
                'version': self.version,
                'feature_columns': self.feature_columns,
                'target_columns': self.target_columns,
                'metadata': self.metadata,
                'performance_metrics': self.performance_metrics
            }

            # Create directory if it doesn't exist
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)

            # Save using joblib (better for sklearn models)
            joblib.dump(model_data, filepath)

            self.logger.info(f"Model saved to {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Error saving model: {e}")
            return False

    def load_model(self, filepath: str) -> bool:
        """Load a trained model from disk"""
        try:
            model_data = joblib.load(filepath)

            self.model = model_data['model']
            self.model_id = model_data['model_id']
            self.model_type = model_data['model_type']
            self.version = model_data['version']
            self.feature_columns = model_data['feature_columns']
            self.target_columns = model_data['target_columns']
            self.metadata = model_data['metadata']
            self.performance_metrics = model_data['performance_metrics']

            self.is_trained = True

            self.logger.info(f"Model loaded from {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            return False

    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance if available"""
        return self.metadata.get('feature_importance', {})

    def update_performance_metrics(self, metrics: Dict[str, float]):
        """Update model performance metrics"""
        self.performance_metrics.update(metrics)
        self.metadata['last_updated'] = datetime.now()

    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information"""
        return {
            'model_id': self.model_id,
            'model_type': self.model_type,
            'version': self.version,
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_columns),
            'target_count': len(self.target_columns),
            'metadata': self.metadata,
            'performance_metrics': self.performance_metrics
        }

    def validate_input_data(self, data: pd.DataFrame) -> bool:
        """Validate that input data has required features"""
        if not self.feature_columns:
            return True

        missing_features = set(self.feature_columns) - set(data.columns)
        if missing_features:
            self.logger.error(f"Missing required features: {missing_features}")
            return False

        return True

    async def retrain(self,
                     new_data: pd.DataFrame,
                     incremental: bool = True,
                     **kwargs) -> Dict[str, Any]:
        """Retrain the model with new data"""
        if incremental and hasattr(self.model, 'partial_fit'):
            # Incremental learning
            self.logger.info("Performing incremental training")
            return await self._incremental_train(new_data, **kwargs)
        else:
            # Full retraining
            self.logger.info("Performing full retraining")
            return await self.train(new_data, **kwargs)

    async def _incremental_train(self, new_data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Perform incremental training (to be overridden by specific models)"""
        self.logger.warning("Incremental training not implemented for this model type")
        return await self.train(new_data, **kwargs)

    def calculate_prediction_confidence(self, predictions: np.ndarray) -> np.ndarray:
        """Calculate confidence scores for predictions (to be overridden)"""
        # Default implementation returns uniform confidence
        return np.ones(len(predictions)) * 0.5

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data before training/prediction (to be overridden)"""
        return data.copy()

    def postprocess_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """Postprocess predictions (to be overridden)"""
        return predictions