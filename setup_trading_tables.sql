-- Trading Strategy Agent Database Tables
-- Run this script to set up the required tables for trading operations

-- Trades table - records all executed trades
CREATE TABLE IF NOT EXISTS trades (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    action VARCHAR(20) NOT NULL, -- 'buy', 'sell', 'strong_buy', 'strong_sell'
    quantity DECIMAL(15, 4) NOT NULL,
    price DECIMAL(15, 4) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    strategy_id VARCHAR(50) NOT NULL,
    confidence DECIMAL(4, 3) NOT NULL, -- 0.000 to 1.000
    metadata JSONB, -- Store additional signal metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Position closures table - records when positions are closed
CREATE TABLE IF NOT EXISTS position_closures (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    entry_price DECIMAL(15, 4) NOT NULL,
    exit_price DECIMAL(15, 4) NOT NULL,
    quantity DECIMAL(15, 4) NOT NULL,
    entry_time TIMESTAMP WITH TIME ZONE NOT NULL,
    exit_time TIMESTAMP WITH TIME ZONE NOT NULL,
    realized_pnl DECIMAL(15, 4) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Portfolio snapshots table - periodic portfolio state records
CREATE TABLE IF NOT EXISTS portfolio_snapshots (
    id SERIAL PRIMARY KEY,
    total_value DECIMAL(15, 4) NOT NULL,
    cash DECIMAL(15, 4) NOT NULL,
    positions_value DECIMAL(15, 4) NOT NULL,
    positions_count INTEGER NOT NULL,
    total_pnl DECIMAL(15, 4) NOT NULL,
    max_drawdown DECIMAL(6, 4) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy performance table - track strategy metrics
CREATE TABLE IF NOT EXISTS strategy_performance (
    id SERIAL PRIMARY KEY,
    strategy_id VARCHAR(50) NOT NULL,
    strategy_type VARCHAR(30) NOT NULL,
    total_signals INTEGER NOT NULL DEFAULT 0,
    successful_signals INTEGER NOT NULL DEFAULT 0,
    total_pnl DECIMAL(15, 4) NOT NULL DEFAULT 0.0,
    success_rate DECIMAL(6, 3) NOT NULL DEFAULT 0.0,
    sharpe_ratio DECIMAL(6, 3),
    max_drawdown DECIMAL(6, 4),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Risk metrics table - store risk calculations
CREATE TABLE IF NOT EXISTS risk_metrics (
    id SERIAL PRIMARY KEY,
    portfolio_risk DECIMAL(6, 4) NOT NULL,
    var_95 DECIMAL(15, 4) NOT NULL,
    var_99 DECIMAL(15, 4) NOT NULL,
    expected_shortfall DECIMAL(15, 4) NOT NULL,
    concentration_risk DECIMAL(6, 4) NOT NULL,
    positions_count INTEGER NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trading signals table - log all generated signals
CREATE TABLE IF NOT EXISTS trading_signals (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    signal_type VARCHAR(20) NOT NULL,
    confidence DECIMAL(4, 3) NOT NULL,
    price DECIMAL(15, 4) NOT NULL,
    strategy_id VARCHAR(50) NOT NULL,
    metadata JSONB,
    executed BOOLEAN DEFAULT FALSE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_trades_strategy_id ON trades(strategy_id);
CREATE INDEX IF NOT EXISTS idx_position_closures_symbol ON position_closures(symbol);
CREATE INDEX IF NOT EXISTS idx_portfolio_snapshots_timestamp ON portfolio_snapshots(timestamp);
CREATE INDEX IF NOT EXISTS idx_strategy_performance_strategy_id ON strategy_performance(strategy_id);
CREATE INDEX IF NOT EXISTS idx_risk_metrics_timestamp ON risk_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol_timestamp ON trading_signals(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_trading_signals_strategy_id ON trading_signals(strategy_id);

-- Insert initial strategy performance records
INSERT INTO strategy_performance (strategy_id, strategy_type)
VALUES
    ('mean_reversion_001', 'mean_reversion'),
    ('momentum_001', 'momentum')
ON CONFLICT DO NOTHING;

-- Grant permissions to trading_user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trading_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trading_user;