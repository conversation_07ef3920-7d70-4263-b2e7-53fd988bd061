<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroA Backtesting Guide - Complete User Manual</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 10px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .nav h3 {
            margin-bottom: 15px;
            color: #2a5298;
        }

        .nav ul {
            list-style: none;
            columns: 2;
        }

        .nav li {
            margin-bottom: 8px;
        }

        .nav a {
            color: #1e3c72;
            text-decoration: none;
            font-weight: 500;
        }

        .nav a:hover {
            color: #2a5298;
            text-decoration: underline;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fdfdfd;
        }

        .section h2 {
            color: #1e3c72;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #2a5298;
            margin-bottom: 15px;
            margin-top: 25px;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .file-tree {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .button {
            display: inline-block;
            background: #2a5298;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: bold;
        }

        .button:hover {
            background: #1e3c72;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2a5298;
        }

        .step {
            background: #f8f9fa;
            border-left: 4px solid #2a5298;
            padding: 20px;
            margin: 20px 0;
        }

        .step h4 {
            color: #1e3c72;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 AstroA Mathematical Backtesting Guide</h1>
            <p>Complete User Manual for Testing Your Mathematical Trading Ideas</p>
            <p><em>Calculus, Probability, Matrix Math, AI Analysis - Your Math, Your Rules</em></p>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#getting-started">🚀 Getting Started</a></li>
                <li><a href="#api-setup">🔑 API Setup (REQUIRED)</a></li>
                <li><a href="#math-architecture">🧮 Mathematical Architecture</a></li>
                <li><a href="#file-structure">📁 File Structure</a></li>
                <li><a href="#running-backtest">⚡ Running Mathematical Backtests</a></li>
                <li><a href="#reading-results">📊 Reading Mathematical Results</a></li>
                <li><a href="#troubleshooting">🔧 Troubleshooting</a></li>
                <li><a href="#advanced-usage">🎯 Advanced Mathematical Testing</a></li>
                <li><a href="#file-dependencies">🔗 File Dependencies</a></li>
            </ul>
        </div>

        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
            <h3 style="color: white; margin-bottom: 15px;">🎯 What This Guide Is Really About</h3>
            <p><strong>This is NOT about simple strategies.</strong> This is about testing YOUR mathematical ideas:</p>
            <ul style="margin-top: 10px;">
                <li>🧮 <strong>Calculus-Based Analysis:</strong> Derivatives, momentum, acceleration</li>
                <li>📊 <strong>Probability Theory:</strong> Market state probabilities, conditional analysis</li>
                <li>🔢 <strong>Matrix Mathematics:</strong> Portfolio optimization, correlation analysis</li>
                <li>🔍 <strong>Pattern Recognition:</strong> Mathematical pattern detection</li>
                <li>🤖 <strong>AI Integration:</strong> Mathematical result interpretation</li>
            </ul>
        </div>

        <section id="getting-started" class="section">
            <h2>🚀 Getting Started</h2>

            <div class="warning">
                <strong>⚠️ Prerequisites:</strong> You need Python installed, API keys (see next section), and access to a terminal/command prompt.
            </div>

            <h3>Step 1: Check Your Environment</h3>
            <div class="step">
                <h4>Open Terminal/Command Prompt</h4>
                <p>Windows: Press Win + R, type "cmd", press Enter</p>
                <p>Mac: Press Cmd + Space, type "terminal", press Enter</p>
                <p>Linux: Press Ctrl + Alt + T</p>
            </div>

            <div class="step">
                <h4>Navigate to AstroA Directory</h4>
                <div class="code-block">
                    cd /path/to/your/AstroA
                    # Replace /path/to/your/AstroA with actual path
                </div>
            </div>

            <h3>Step 2: Activate Virtual Environment</h3>
            <div class="code-block">
                # On Windows:
                venv\Scripts\activate

                # On Mac/Linux:
                source venv/bin/activate
            </div>

            <div class="success">
                <strong>✅ Success Indicator:</strong> You should see "(venv)" at the beginning of your command prompt
            </div>

            <h3>Step 3: Install Dependencies</h3>
            <div class="code-block">
                pip install -r requirements.txt
            </div>

            <h3>Step 4: Set Up Database</h3>
            <div class="code-block">
                # Set up trading tables
                PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -f setup_trading_tables.sql
            </div>
        </section>

        <section id="api-setup" class="section">
            <h2>🔑 API Keys Setup (REQUIRED for Mathematical Backtesting)</h2>

            <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>⚠️ CRITICAL:</strong> You MUST have these API keys to backtest your mathematical ideas. The system needs real market data to test your formulas!
            </div>

            <h3>📋 Required API Keys & Where to Get Them</h3>

            <div style="background: #f8f9fa; border: 2px solid #2a5298; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #1e3c72; margin-bottom: 10px;">🤖 DeepSeek AI (REQUIRED - Mathematical Analysis)</h4>
                <p><strong>Purpose:</strong> AI interpretation of your mathematical results</p>
                <p><strong>Cost:</strong> Pay-per-use (~$0.10-1.00 per day of testing)</p>
                <a href="https://platform.deepseek.com/" style="display: inline-block; background: #2a5298; color: white !important; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px 0; font-weight: bold;" target="_blank">Get DeepSeek API Key</a>
                <div class="code-block">
# In your .env file:
DEEPSEEK_API_KEY=sk-your-actual-deepseek-key-here
DEEPSEEK_BASE_URL=https://api.deepseek.com</div>
            </div>

            <div style="background: #f8f9fa; border: 2px solid #2a5298; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #1e3c72; margin-bottom: 10px;">📰 NewsAPI (REQUIRED - Sentiment Analysis)</h4>
                <p><strong>Purpose:</strong> News sentiment for mathematical probability calculations</p>
                <p><strong>Cost:</strong> Free tier available (500 requests/day)</p>
                <a href="https://newsapi.org/register" style="display: inline-block; background: #2a5298; color: white !important; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px 0; font-weight: bold;" target="_blank">Get News API Key</a>
                <div class="code-block">
# In your .env file:
NEWS_API_KEY=your-actual-news-api-key-here</div>
            </div>

            <div style="background: #f8f9fa; border: 2px solid #2a5298; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #1e3c72; margin-bottom: 10px;">📈 Alpha Vantage (REQUIRED - Cross-Asset Data)</h4>
                <p><strong>Purpose:</strong> Stock/forex data for correlation matrix calculations</p>
                <p><strong>Cost:</strong> Free tier available (25 requests/day)</p>
                <a href="https://www.alphavantage.co/support/#api-key" style="display: inline-block; background: #2a5298; color: white !important; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px 0; font-weight: bold;" target="_blank">Get Alpha Vantage API Key</a>
                <div class="code-block">
# In your .env file:
ALPHA_VANTAGE_API_KEY=your-actual-alpha-vantage-key-here</div>
            </div>

            <div style="background: #f8f9fa; border: 2px solid #2a5298; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #1e3c72; margin-bottom: 10px;">💱 Exchange APIs (OPTIONAL - More Data)</h4>
                <p><strong>Purpose:</strong> Additional crypto data for mathematical analysis</p>
                <p><strong>Binance:</strong> Free API access</p>
                <a href="https://www.binance.com/en/binance-api" style="display: inline-block; background: #2a5298; color: white !important; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px 0; font-weight: bold;" target="_blank">Get Binance API Key</a>
                <p><strong>Coinbase:</strong> Free API access</p>
                <a href="https://docs.cloud.coinbase.com/sign-in-with-coinbase/docs/api-key-authentication" style="display: inline-block; background: #2a5298; color: white !important; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin: 5px 0; font-weight: bold;" target="_blank">Get Coinbase API Key</a>
                <div class="code-block">
# In your .env file:
BINANCE_API_KEY=your-binance-key-here
BINANCE_SECRET_KEY=your-binance-secret-here
COINBASE_API_KEY=your-coinbase-key-here
COINBASE_SECRET_KEY=your-coinbase-secret-here</div>
            </div>

            <h3>⚙️ Setting Up Your .env File</h3>
            <div class="step">
                <h4>Step 1: Copy the template</h4>
                <div class="code-block">cp .env.example .env</div>
                <p><strong>File location:</strong> <code>/home/<USER>/axmadcodes/AstroA/.env</code></p>
            </div>

            <div class="step">
                <h4>Step 2: Edit with your API keys</h4>
                <div class="code-block">
# Open the .env file in any text editor:
nano .env

# Essential for mathematical backtesting:
DEEPSEEK_API_KEY=sk-your-actual-deepseek-key
DEEPSEEK_BASE_URL=https://api.deepseek.com
NEWS_API_KEY=your-actual-news-api-key
ALPHA_VANTAGE_API_KEY=your-actual-alpha-vantage-key

# Database settings (usually already correct):
DB_HOST=localhost
DB_NAME=mathematical_trading
DB_USER=trading_user
DB_PASSWORD=hejhej

# Optional exchange APIs:
BINANCE_API_KEY=your_binance_key_if_you_have_one
COINBASE_API_KEY=your_coinbase_key_if_you_have_one</div>
            </div>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>💰 Total Cost Estimate:</strong><br>
                • DeepSeek: ~$0.10-1.00 per day of testing<br>
                • NewsAPI: Free (500 requests/day)<br>
                • Alpha Vantage: Free (25 requests/day)<br>
                • <strong>Total: Under $1/day for comprehensive mathematical testing</strong>
            </div>

            <h3>📁 API Configuration Files</h3>
            <table>
                <tr>
                    <th>Configuration File</th>
                    <th>Purpose</th>
                    <th>Location</th>
                </tr>
                <tr>
                    <td><strong>.env</strong></td>
                    <td>Store all your API keys securely</td>
                    <td><code>./AstroA/.env</code></td>
                </tr>
                <tr>
                    <td><strong>config/settings.py</strong></td>
                    <td>Read API keys from .env file</td>
                    <td><code>./AstroA/config/settings.py</code></td>
                </tr>
                <tr>
                    <td><strong>config/deepseek_client.py</strong></td>
                    <td>DeepSeek AI client configuration</td>
                    <td><code>./AstroA/config/deepseek_client.py</code></td>
                </tr>
            </table>
        </section>

        <section id="math-architecture" class="section">
            <h2>🧮 Mathematical Architecture - The Real System</h2>

            <div class="info">
                <strong>🎯 Understanding the REAL AstroA:</strong><br>
                This is a <strong>Mathematical Trading Engine</strong>, not simple strategies. The "mean reversion" and "momentum" you see are just execution templates. The real intelligence is in the mathematical analysis.
            </div>

            <h3>🔄 Mathematical Analysis Pipeline</h3>
            <div style="background: #e8f4fd; border: 2px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 15px 0; font-family: 'Courier New', monospace;">
📡 Raw Data → 🧮 Mathematical Engine → 🤖 AI Interpretation → 📈 Strategy Selection → ⚖️ Risk Management

Where Mathematical Engine includes:
├── Calculus Formulas (derivatives, integrals, momentum)
├── Probability Theory (distributions, conditional probabilities)
├── Matrix Mathematics (correlations, optimizations)
├── Statistical Analysis (50+ indicators)
├── Pattern Recognition (mathematical patterns)
└── Risk Analysis (VaR, portfolio metrics)
            </div>

            <h3>🎯 Core Mathematical Components</h3>
            <table>
                <tr>
                    <th>Component</th>
                    <th>Mathematical Focus</th>
                    <th>File Location</th>
                </tr>
                <tr>
                    <td><strong>Calculus Formulas</strong></td>
                    <td>Price derivatives, momentum, acceleration</td>
                    <td>shared/mathematical/formulas.py</td>
                </tr>
                <tr>
                    <td><strong>Probability Formulas</strong></td>
                    <td>Market state probabilities, success rates</td>
                    <td>shared/mathematical/formulas.py</td>
                </tr>
                <tr>
                    <td><strong>Matrix Formulas</strong></td>
                    <td>Portfolio optimization, correlations</td>
                    <td>shared/mathematical/formulas.py</td>
                </tr>
                <tr>
                    <td><strong>Mathematical Engine Agent</strong></td>
                    <td>Coordinates all mathematical analysis</td>
                    <td>agents/mathematical_engine/mathematical_engine_agent.py</td>
                </tr>
                <tr>
                    <td><strong>Statistical Analyzer</strong></td>
                    <td>Technical indicators, moving averages</td>
                    <td>agents/mathematical_engine/analyzers/statistical/</td>
                </tr>
                <tr>
                    <td><strong>Correlation Analyzer</strong></td>
                    <td>Cross-asset correlations, lead-lag</td>
                    <td>agents/mathematical_engine/analyzers/correlation/</td>
                </tr>
                <tr>
                    <td><strong>Pattern Analyzer</strong></td>
                    <td>Mathematical pattern detection</td>
                    <td>agents/mathematical_engine/analyzers/pattern/</td>
                </tr>
                <tr>
                    <td><strong>Risk Analyzer</strong></td>
                    <td>VaR, portfolio risk, drawdowns</td>
                    <td>agents/mathematical_engine/analyzers/risk/</td>
                </tr>
            </table>

            <h3>🔬 How Mathematical Analysis Feeds Trading</h3>
            <div style="background: #e8f4fd; border: 2px solid #bee5eb; padding: 20px; border-radius: 8px; margin: 15px 0; font-family: 'Courier New', monospace;">
# In trading_strategy_agent.py (line 165-167):
analysis_results = await self._get_analysis_results()
signals = await strategy.generate_signals(market_data, analysis_results)

# The mathematical engine provides:
- Statistical indicators and technical analysis
- Probability distributions for market states
- Correlation matrices for portfolio decisions
- Pattern recognition results for signal confirmation
- Risk assessments for position sizing
- Calculus-based momentum and acceleration data
            </div>
        </section>

        <section id="file-structure" class="section">
            <h2>📁 Complete File Structure & Purpose</h2>

            <div class="file-tree">
AstroA/
├── 📄 main.py                          # Main entry point - START HERE
├── 📄 README.md                        # Project overview
├── 📄 requirements.txt                 # Python dependencies
├── 📄 .env                            # Environment variables (API keys, etc.)
├── 📄 .env.example                    # Template for environment setup
│
├── 🏗️ agents/                         # AI Agent System
│   ├── 📄 base_agent.py               # Core agent functionality
│   ├── 📄 data_agent.py               # Data collection orchestrator
│   ├──
│   ├── 📂 data_collector/             # Market Data Collection
│   │   ├── 📄 data_collection_agent.py # Main data collector
│   │   ├── 📂 collectors/
│   │   │   ├── 📄 market_collector.py  # Crypto market data
│   │   │   └── 📄 news_collector.py    # News sentiment data
│   │   └── 📂 processors/             # Data processing modules
│   │
│   ├── 📂 trading_strategy/           # Trading Strategy Engine
│   │   ├── 📄 trading_strategy_agent.py # Main strategy coordinator
│   │   ├── 📂 strategies/
│   │   │   ├── 📄 mean_reversion_strategy.py # Mean reversion logic
│   │   │   └── 📄 momentum_strategy.py # Momentum trading logic
│   │   └── 📂 risk_management/
│   │       └── 📄 risk_manager.py     # Risk assessment & control
│   │
│   └── 📂 communication/              # Agent communication system
│
├── 🧮 shared/                         # Shared Utilities & Types
│   ├── 📂 types/
│   │   ├── 📄 agent_types.py          # Agent type definitions
│   │   └── 📄 strategy_types.py       # Trading strategy types
│   ├── 📂 utils/
│   │   ├── 📄 base_agent.py           # Base agent class
│   │   └── 📄 base_strategy.py        # Base strategy class
│   ├── 📂 mathematical/
│   │   ├── 📄 formulas.py             # Mathematical formulas
│   │   ├── 📄 utils.py                # Math utilities
│   │   └── 📄 constants.py            # Mathematical constants
│   └── 📂 exceptions/                 # Custom exception classes
│
├── ⚙️ config/                         # Configuration
│   ├── 📄 settings.py                 # Application settings
│   └── 📄 deepseek_client.py          # AI client configuration
│
├── 🗄️ database/                       # Database Setup
│   ├── 📄 database_schema.sql         # Core database schema
│   └── 📄 setup_trading_tables.sql    # Trading-specific tables
│
├── 📊 data/                          # Data Storage
│   ├── 📂 raw/                       # Raw market data
│   ├── 📂 processed/                 # Processed data
│   └── 📂 cache/                     # Cached results
│
├── 📈 reports/                       # Analysis Reports
│   └── 📂 output/                    # Generated reports go here
│
├── 📝 logs/                          # System Logs
│   └── 📂 agents/                    # Agent-specific logs
│
├── 🧪 Test Files                     # Testing & Verification
│   ├── 📄 test_trading_strategy_agent.py # Strategy testing
│   ├── 📄 test_deepseek.py           # AI connectivity test
│   ├── 📄 test_mathematical_engine.py # Math engine test
│   └── 📄 verify_trading_system.py   # System verification
│
└── 📄 COMPREHENSIVE_BACKTESTING_GUIDE.html # This guide!
            </div>

            <h3>🔑 Key Files Explained</h3>
            <table>
                <tr>
                    <th>File</th>
                    <th>Purpose</th>
                    <th>When You Need It</th>
                </tr>
                <tr>
                    <td><strong>main.py</strong></td>
                    <td>Main application entry point</td>
                    <td>Always - this is where you start</td>
                </tr>
                <tr>
                    <td><strong>test_trading_strategy_agent.py</strong></td>
                    <td>Test the trading strategies</td>
                    <td>Before running live backtests</td>
                </tr>
                <tr>
                    <td><strong>verify_trading_system.py</strong></td>
                    <td>Check if everything is set up correctly</td>
                    <td>After installation, before first run</td>
                </tr>
                <tr>
                    <td><strong>trading_strategy_agent.py</strong></td>
                    <td>Core trading logic coordinator</td>
                    <td>Main backtesting engine</td>
                </tr>
                <tr>
                    <td><strong>setup_trading_tables.sql</strong></td>
                    <td>Database setup for trading data</td>
                    <td>One-time setup</td>
                </tr>
            </table>
        </section>

        <section id="running-backtest" class="section">
            <h2>⚡ Running Your Mathematical Backtests</h2>

            <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>🔑 PREREQUISITE:</strong> Make sure you have set up your API keys in the .env file (see API Setup section above)
            </div>

            <div class="info">
                <strong>💡 What is Mathematical Backtesting?</strong><br>
                This tests YOUR mathematical ideas (calculus, probability, matrix operations) against real market data to see how well your mathematical models perform. It's like a laboratory for your mathematical trading theories!
            </div>

            <h3>Method 1: Test Mathematical Engine (Start Here!)</h3>
            <div class="step">
                <h4>Step 1: Test Your Mathematical Components</h4>
                <div class="code-block">python test_mathematical_engine.py</div>
                <p><strong>What this tests:</strong></p>
                <ul>
                    <li>✅ Calculus formulas (derivatives, momentum, acceleration)</li>
                    <li>✅ Probability calculations (market states, success rates)</li>
                    <li>✅ Matrix operations (correlations, portfolio optimization)</li>
                    <li>✅ Statistical analysis (50+ indicators)</li>
                    <li>✅ Pattern recognition algorithms</li>
                    <li>✅ Risk analysis calculations</li>
                    <li>✅ Database integration</li>
                    <li>✅ Performance benchmarks</li>
                </ul>
                <p><strong>Expected output:</strong> "🎉 ALL TESTS PASSED! Mathematical Engine is ready!"</p>
            </div>

            <div class="step">
                <h4>Step 2: Verify System Integration</h4>
                <div class="code-block">python verify_trading_system.py</div>
                <p><strong>What this does:</strong> Checks if mathematical engine integrates with trading system</p>
                <p><strong>Expected output:</strong> Green checkmarks (✅) for all mathematical components</p>
            </div>

            <h3>Method 2: Full Mathematical Backtest with Real Data</h3>
            <div class="step">
                <h4>Step 1: Collect Data for Mathematical Analysis</h4>
                <div class="code-block">python main.py</div>
                <p><strong>What this collects for your mathematical analysis:</strong></p>
                <ul>
                    <li>📊 Multi-timeframe crypto data (1m, 5m, 1h, 4h, 1d)</li>
                    <li>📰 News sentiment data (for probability calculations)</li>
                    <li>📈 Cross-asset correlations (stocks, forex, commodities)</li>
                    <li>🧮 Processed mathematical indicators</li>
                    <li>🤖 AI interpretation of mathematical results</li>
                </ul>
                <p><strong>Duration:</strong> 2-5 minutes depending on API response times</p>
            </div>

            <div class="step">
                <h4>Step 2: Run Mathematical Trading Analysis</h4>
                <div class="code-block">python test_trading_strategy_agent.py</div>
                <p><strong>What this tests:</strong></p>
                <ul>
                    <li>🧮 How mathematical analysis influences trading decisions</li>
                    <li>📊 Statistical indicators feeding into strategies</li>
                    <li>🔢 Matrix-based position sizing calculations</li>
                    <li>📈 Probability-based entry/exit timing</li>
                    <li>⚖️ Mathematical risk management</li>
                </ul>
            </div>

            <div class="step">
                <h4>Step 3: Check Mathematical Results</h4>
                <p>Look for these mathematical success indicators:</p>
                <ul>
                    <li>✅ "Mathematical Engine Agent initialized successfully"</li>
                    <li>🧮 "Generated X signals with mathematical basis"</li>
                    <li>📊 "Mathematical analysis: Z-score: X.XX, Probability: X.XX"</li>
                    <li>💾 "Analysis results stored with mathematical metadata"</li>
                </ul>
            </div>

            <h3>Method 3: Test Individual Mathematical Components</h3>

            <div class="step">
                <h4>Test Your Calculus Formulas</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import CalculusFormulas
import pandas as pd
import numpy as np

# Test your calculus ideas
calc = CalculusFormulas()
test_prices = pd.Series([100, 101, 103, 102, 105, 107, 106, 108, 110, 109])

# Test price momentum using derivatives
momentum = calc.derivative_price_momentum(test_prices)
print('🧮 Mathematical Momentum (First Derivative):')
print(momentum.dropna())

# Test price acceleration using second derivatives
acceleration = calc.second_derivative_acceleration(test_prices)
print('📈 Mathematical Acceleration (Second Derivative):')
print(acceleration.dropna())
"</div>
            </div>

            <div class="step">
                <h4>Test Your Probability Formulas</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import ProbabilityFormulas
import pandas as pd
import numpy as np

# Test your probability ideas
prob = ProbabilityFormulas()
test_prices = pd.Series(np.random.normal(100, 5, 100).cumsum() + 1000)

# Test probability of reaching target price
target_analysis = prob.normal_distribution_price_probability(test_prices, 1150)
print('📊 Probability Analysis Results:')
for key, value in target_analysis.items():
    print(f'{key}: {value}')
"</div>
            </div>

            <div class="step">
                <h4>Test Your Matrix Operations</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import MatrixFormulas
import pandas as pd
import numpy as np

# Test your matrix ideas
matrix = MatrixFormulas()

# Create test return data
np.random.seed(42)
returns_data = pd.DataFrame({
    'BTC': np.random.normal(0.001, 0.03, 100),
    'ETH': np.random.normal(0.0008, 0.025, 100),
    'ADA': np.random.normal(0.0012, 0.02, 100)
})

# Test correlation matrix analysis
corr_analysis = matrix.correlation_matrix_analysis(returns_data)
print('🔢 Matrix Analysis Results:')
print(f'Determinant: {corr_analysis[\"determinant\"]:.6f}')
print(f'Max Correlation: {corr_analysis[\"max_correlation\"]:.3f}')
print('Correlation Matrix:')
print(corr_analysis['correlation_matrix'])
"</div>
            </div>

            <div class="warning">
                <strong>⚠️ If you get errors:</strong><br>
                1. Make sure your virtual environment is activated<br>
                2. Check that your database is running<br>
                3. Verify your .env file has correct settings<br>
                4. See the Troubleshooting section below
            </div>
        </section>

        <section id="reading-results" class="section">
            <h2>📊 Reading Your Mathematical Backtest Results</h2>

            <h3>🧮 Understanding Mathematical Analysis Output</h3>
            <div class="info">
                Your mathematical analysis results show how calculus, probability, and matrix operations influenced trading decisions:
            </div>

            <table>
                <tr>
                    <th>Result Type</th>
                    <th>Location</th>
                    <th>What It Contains</th>
                </tr>
                <tr>
                    <td><strong>Mathematical Analysis Results</strong></td>
                    <td><code>./data/analysis_results_YYYYMMDD_HHMMSS.json</code></td>
                    <td>Calculus derivatives, probability distributions, matrix correlations</td>
                </tr>
                <tr>
                    <td><strong>Mathematical Engine Logs</strong></td>
                    <td><code>./logs/agents/mathematical_engine_001.log</code></td>
                    <td>Detailed mathematical calculations and analysis steps</td>
                </tr>
                <tr>
                    <td><strong>Trading Strategy Logs</strong></td>
                    <td><code>./trading_strategy_test.log</code></td>
                    <td>How mathematical analysis influenced trading decisions</td>
                </tr>
                <tr>
                    <td><strong>Mathematical Database Results</strong></td>
                    <td>PostgreSQL Tables: <code>trades</code>, <code>analysis_results</code></td>
                    <td>Mathematical metadata for each trade and analysis run</td>
                </tr>
                <tr>
                    <td><strong>System Performance Logs</strong></td>
                    <td><code>./logs/astroa_YYYYMMDD.log</code></td>
                    <td>Overall system performance and mathematical engine status</td>
                </tr>
            </table>

            <h3>🧮 Mathematical Engine Test Output</h3>

            <h4>1. Mathematical Components Test Results</h4>
            <div class="code-block">
=== Mathematical Engine - Comprehensive Test Suite ===

📐 Mathematical Formulas: PASSED
   ✅ Calculus formulas: derivatives and momentum calculated
   ✅ Probability analysis: 0.234 chance of reaching target
   ✅ Matrix formulas: correlation analysis completed

🧮 Statistical Analyzer: PASSED
   ✅ Statistical analysis completed for 847 data points
   ✅ Current price: $45,234.56
   ✅ Volatility: 3.45%
   ✅ Moving averages calculated: 6 indicators
   ✅ Technical indicators calculated: 12 indicators

🔗 Correlation Analyzer: PASSED
   ✅ Correlation analysis completed for 3 assets
   ✅ Average correlation: 0.456

🔍 Pattern Analyzer: PASSED
   ✅ Pattern analysis completed for 300 periods
   ✅ Candlestick patterns detected: 23 total patterns

🛡️ Risk Analyzer: PASSED
   ✅ Portfolio volatility: 12.34%
   ✅ Sharpe ratio: 1.456
   ✅ VaR (95%): -0.0234

🎯 Integrated Analysis: PASSED
   ✅ Calculus analysis: momentum and acceleration calculated
   ✅ Advanced indicators: momentum oscillator and trend strength

🎉 ALL TESTS PASSED! Mathematical Engine is ready!
            </div>

            <h4>2. Mathematical Trading Integration Results</h4>
            <div class="code-block">
=== Trading Strategy Agent Test ===

8. Testing strategy signal generation...
   🧮 Mathematical analysis input:
      - Calculus momentum: 0.0045 (positive acceleration)
      - Probability of upward move: 0.678
      - Matrix correlation strength: 0.234

   📊 Strategy mean_reversion_001 generated 2 signals:
   Signal: buy BTC/USDT at $45000.00 (confidence: 0.75)
      └── Mathematical basis: Z-score: -2.34, Probability: 0.78
   Signal: sell ETH/USDT at $3200.00 (confidence: 0.68)
      └── Mathematical basis: Z-score: +2.12, Probability: 0.71

   📈 Strategy momentum_001 generated 1 signal:
   Signal: strong_buy ADA/USDT at $0.45 (confidence: 0.82)
      └── Mathematical basis: Momentum: 0.0067, Trend strength: 0.84
            </div>

            <h3>🔍 Analyzing Your Mathematical Impact</h3>

            <table>
                <tr>
                    <th>Mathematical Component</th>
                    <th>How It Affects Trading</th>
                    <th>Example Output</th>
                </tr>
                <tr>
                    <td><strong>Calculus Derivatives</strong></td>
                    <td>Momentum detection, acceleration analysis</td>
                    <td>Momentum: 0.0045 → BUY signal</td>
                </tr>
                <tr>
                    <td><strong>Probability Calculations</strong></td>
                    <td>Entry/exit timing, confidence levels</td>
                    <td>P(upward) = 0.678 → High confidence</td>
                </tr>
                <tr>
                    <td><strong>Matrix Operations</strong></td>
                    <td>Position sizing, portfolio allocation</td>
                    <td>Correlation = 0.234 → Diversification OK</td>
                </tr>
                <tr>
                    <td><strong>Statistical Analysis</strong></td>
                    <td>Technical indicators, market state</td>
                    <td>Volatility = 3.45% → Low risk</td>
                </tr>
                <tr>
                    <td><strong>Pattern Recognition</strong></td>
                    <td>Signal confirmation, timing</td>
                    <td>23 patterns detected → Confirmation</td>
                </tr>
            </table>

            <h4>3. Understanding Trading Signals</h4>
            <table>
                <tr>
                    <th>Signal Type</th>
                    <th>Meaning</th>
                    <th>Confidence Range</th>
                </tr>
                <tr>
                    <td>BUY</td>
                    <td>Strategy recommends buying</td>
                    <td>0.6 - 0.8</td>
                </tr>
                <tr>
                    <td>STRONG_BUY</td>
                    <td>High confidence buy signal</td>
                    <td>0.8 - 1.0</td>
                </tr>
                <tr>
                    <td>SELL</td>
                    <td>Strategy recommends selling</td>
                    <td>0.6 - 0.8</td>
                </tr>
                <tr>
                    <td>STRONG_SELL</td>
                    <td>High confidence sell signal</td>
                    <td>0.8 - 1.0</td>
                </tr>
                <tr>
                    <td>HOLD</td>
                    <td>No clear direction</td>
                    <td>Below 0.6</td>
                </tr>
            </table>

            <h3>📈 Performance Metrics Explained</h3>

            <h4>Portfolio Metrics</h4>
            <ul>
                <li><strong>Total Value:</strong> Current value of all holdings</li>
                <li><strong>Cash:</strong> Available cash for trading</li>
                <li><strong>Positions Value:</strong> Value of current positions</li>
                <li><strong>Total PnL:</strong> Profit and Loss (unrealized)</li>
                <li><strong>Daily PnL:</strong> Today's profit/loss</li>
                <li><strong>Max Drawdown:</strong> Largest peak-to-trough decline</li>
            </ul>

            <h4>Strategy Performance</h4>
            <ul>
                <li><strong>Success Rate:</strong> Percentage of profitable trades</li>
                <li><strong>Sharpe Ratio:</strong> Risk-adjusted returns (higher is better)</li>
                <li><strong>Total Signals:</strong> Number of trading signals generated</li>
                <li><strong>Successful Signals:</strong> Number of profitable signals</li>
            </ul>

            <h3>📊 Database Queries for Mathematical Results</h3>
            <p>To see how your mathematical analysis influenced trading decisions:</p>

            <div class="code-block">
# View trades with mathematical metadata
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    symbol,
    action,
    confidence,
    metadata->>'z_score' as z_score,
    metadata->>'volatility' as volatility,
    metadata->>'momentum' as momentum,
    timestamp
FROM trades
WHERE metadata IS NOT NULL
ORDER BY timestamp DESC
LIMIT 10;"

# View mathematical analysis results
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    timestamp,
    selected_symbols,
    mathematical_scores->>'calculus' as calculus_analysis,
    mathematical_scores->>'probability' as probability_analysis
FROM analysis_results
ORDER BY timestamp DESC
LIMIT 5;"

# View mathematical performance correlation
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    COUNT(*) as total_trades,
    AVG(confidence) as avg_confidence,
    AVG(CAST(metadata->>'z_score' AS FLOAT)) as avg_z_score
FROM trades
WHERE metadata->>'z_score' IS NOT NULL;"
            </div>
        </section>

        <section id="system-architecture" class="section">
            <h2>🧮 Mathematical System Architecture</h2>

            <div class="info">
                <strong>📚 Understanding the REAL System:</strong><br>
                AstroA is a <strong>Mathematical Trading Engine</strong> where mathematical analysis drives all trading decisions. The "strategies" are just execution templates - the real intelligence is mathematical.
            </div>

            <h3>🔄 Mathematical Analysis Pipeline</h3>
            <div class="file-tree">
1. 📡 Data Collection Agent (APIs Required)
   ├── Fetches crypto prices from exchanges (CCXT)
   ├── Collects news sentiment (NewsAPI)
   ├── Gathers cross-asset correlations (Alpha Vantage)
   └── Stores in PostgreSQL database
        ↓
2. 🧮 MATHEMATICAL ENGINE (Core Intelligence)
   ├── Calculus Formulas
   │   ├── Price derivatives (momentum)
   │   ├── Second derivatives (acceleration)
   │   └── Integral-based volume analysis
   ├── Probability Theory
   │   ├── Market state probabilities
   │   ├── Target price probability calculations
   │   └── Conditional probability analysis
   ├── Matrix Mathematics
   │   ├── Correlation matrix analysis
   │   ├── Portfolio optimization
   │   └── Eigenvalue decomposition
   ├── Statistical Analysis (50+ indicators)
   ├── Pattern Recognition (mathematical patterns)
   └── Risk Analysis (VaR, portfolio metrics)
        ↓
3. 🤖 AI Analysis (DeepSeek API)
   ├── Interprets mathematical results
   ├── Contextualizes probability outputs
   ├── Explains correlation relationships
   └── Provides market insights
        ↓
4. 📈 Strategy Execution Templates
   ├── Mathematical Mean Reversion
   │   ├── Uses calculus-based Z-scores
   │   ├── Applies probability thresholds
   │   └── Matrix-based position sizing
   └── Mathematical Momentum
       ├── Uses derivative analysis
       ├── Probability-based confirmation
       └── Risk-adjusted sizing
        ↓
5. ⚖️ Mathematical Risk Management
   ├── VaR calculations from probability engine
   ├── Matrix-based portfolio risk
   ├── Calculus-based stop loss placement
   └── Probability-weighted position sizes
        ↓
6. 💼 Portfolio Management
   ├── Executes trades based on mathematical signals
   ├── Tracks mathematical metadata
   ├── Calculates mathematical performance metrics
   └── Updates with mathematical basis
        ↓
7. 📊 Mathematical Results & Logging
   ├── Stores mathematical analysis metadata
   ├── Logs mathematical decision reasoning
   ├── Creates mathematical performance reports
   └── Tracks mathematical model accuracy
            </div>

            <h3>🎯 Strategy Logic Explained</h3>

            <h4>Mean Reversion Strategy</h4>
            <div class="step">
                <h4>How it Works:</h4>
                <ol>
                    <li>Calculates rolling average price over 20 periods</li>
                    <li>Calculates price standard deviation</li>
                    <li>Computes Z-score: (Current Price - Average) / Standard Deviation</li>
                    <li>Generates signals:
                        <ul>
                            <li>Z-score < -2.0 → BUY (price below average)</li>
                            <li>Z-score > +2.0 → SELL (price above average)</li>
                            <li>|Z-score| < 0.5 → EXIT (price near average)</li>
                        </ul>
                    </li>
                </ol>
                <p><strong>Best for:</strong> Range-bound markets, sideways trends</p>
            </div>

            <h4>Momentum Strategy</h4>
            <div class="step">
                <h4>How it Works:</h4>
                <ol>
                    <li>Analyzes price momentum over multiple timeframes</li>
                    <li>Calculates rate of change and moving averages</li>
                    <li>Identifies trend strength and direction</li>
                    <li>Generates signals:
                        <ul>
                            <li>Strong upward momentum → BUY</li>
                            <li>Strong downward momentum → SELL</li>
                            <li>Momentum weakening → EXIT</li>
                        </ul>
                    </li>
                </ol>
                <p><strong>Best for:</strong> Trending markets, breakouts</p>
            </div>

            <h3>🔗 File Dependencies & Connections</h3>
            <table>
                <tr>
                    <th>Core File</th>
                    <th>Depends On</th>
                    <th>Used By</th>
                </tr>
                <tr>
                    <td>main.py</td>
                    <td>config/settings.py, agents/base_agent.py</td>
                    <td>User (entry point)</td>
                </tr>
                <tr>
                    <td>trading_strategy_agent.py</td>
                    <td>shared/utils/base_agent.py, shared/types/strategy_types.py</td>
                    <td>test_trading_strategy_agent.py</td>
                </tr>
                <tr>
                    <td>mean_reversion_strategy.py</td>
                    <td>shared/utils/base_strategy.py</td>
                    <td>trading_strategy_agent.py</td>
                </tr>
                <tr>
                    <td>momentum_strategy.py</td>
                    <td>shared/utils/base_strategy.py</td>
                    <td>trading_strategy_agent.py</td>
                </tr>
                <tr>
                    <td>risk_manager.py</td>
                    <td>shared/types/strategy_types.py</td>
                    <td>trading_strategy_agent.py</td>
                </tr>
            </table>
        </section>

        <section id="troubleshooting" class="section">
            <h2>🔧 Troubleshooting Common Issues</h2>

            <h3>❌ Problem: "ModuleNotFoundError"</h3>
            <div class="step">
                <h4>Solution:</h4>
                <ol>
                    <li>Make sure virtual environment is activated: <code>source venv/bin/activate</code></li>
                    <li>Install requirements: <code>pip install -r requirements.txt</code></li>
                    <li>Check Python path: <code>python -c "import sys; print(sys.path)"</code></li>
                </ol>
            </div>

            <h3>❌ Problem: Database Connection Failed</h3>
            <div class="step">
                <h4>Solution:</h4>
                <ol>
                    <li>Check if PostgreSQL is running: <code>systemctl status postgresql</code></li>
                    <li>Verify database exists:
                        <div class="code-block">
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d postgres -c "SELECT datname FROM pg_database WHERE datname='mathematical_trading';"
                        </div>
                    </li>
                    <li>Check .env file has correct database settings</li>
                    <li>Create database if missing: <code>createdb mathematical_trading</code></li>
                </ol>
            </div>

            <h3>❌ Problem: "No market data available"</h3>
            <div class="step">
                <h4>Solution:</h4>
                <ol>
                    <li>Check internet connection</li>
                    <li>Verify API keys in .env file</li>
                    <li>Run data collection first: <code>python main.py</code></li>
                    <li>Check if data exists in database:
                        <div class="code-block">
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "SELECT COUNT(*) FROM market_data;"
                        </div>
                    </li>
                </ol>
            </div>

            <h3>❌ Problem: Tests Fail</h3>
            <div class="step">
                <h4>Solution:</h4>
                <ol>
                    <li>Run system verification: <code>python verify_trading_system.py</code></li>
                    <li>Check logs for specific errors: <code>tail -f logs/astroa_$(date +%Y%m%d).log</code></li>
                    <li>Ensure database tables exist: <code>python -c "import psycopg2; print('DB OK')"</code></li>
                    <li>Clear cache and restart: <code>rm -rf data/cache/*</code></li>
                </ol>
            </div>

            <h3>❌ Problem: Poor Strategy Performance</h3>
            <div class="step">
                <h4>Diagnosis:</h4>
                <ol>
                    <li>Check market conditions (strategies perform differently in different markets)</li>
                    <li>Review confidence thresholds in strategy parameters</li>
                    <li>Analyze historical data quality and timeframes</li>
                    <li>Consider adjusting strategy parameters for current market regime</li>
                </ol>
            </div>
        </section>

        <section id="advanced-usage" class="section">
            <h2>🎯 Advanced Usage & Customization</h2>

            <h3>⚙️ Configuring Strategy Parameters</h3>
            <div class="step">
                <h4>Mean Reversion Strategy Parameters:</h4>
                <div class="code-block">
# In mean_reversion_strategy.py, modify default_params:
default_params = {
    'lookback_period': 20,      # Rolling window size
    'z_score_entry': 2.0,       # Entry threshold
    'z_score_exit': 0.5,        # Exit threshold
    'min_confidence': 0.6,      # Minimum signal confidence
    'max_holding_period': 10    # Maximum days to hold position
}
                </div>
            </div>

            <h3>🎚️ Risk Management Settings</h3>
            <div class="step">
                <h4>Adjusting Risk Parameters:</h4>
                <div class="code-block">
# In trading_strategy_agent.py, modify portfolio settings:
self.initial_capital = 100000      # Starting capital
self.strategy_execution_interval = 300  # 5 minutes
self.risk_assessment_interval = 60      # 1 minute
                </div>
            </div>

            <h3>📊 Custom Reporting</h3>
            <div class="step">
                <h4>Generate Custom Performance Report:</h4>
                <div class="code-block">
# Create custom analysis script
python -c "
import psycopg2
import pandas as pd
import matplotlib.pyplot as plt

# Connect to database
conn = psycopg2.connect(
    host='localhost',
    database='mathematical_trading',
    user='trading_user',
    password='hejhej'
)

# Get portfolio performance over time
df = pd.read_sql('''
    SELECT timestamp, total_value, total_pnl
    FROM portfolio_snapshots
    ORDER BY timestamp
''', conn)

# Plot performance
plt.figure(figsize=(12, 6))
plt.plot(df['timestamp'], df['total_value'])
plt.title('Portfolio Value Over Time')
plt.xlabel('Date')
plt.ylabel('Portfolio Value ($)')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('reports/output/portfolio_performance.png')
print('Report saved to reports/output/portfolio_performance.png')
"
                </div>
            </div>

            <h3>🔄 Running Continuous Backtesting</h3>
            <div class="step">
                <h4>Set up automated testing:</h4>
                <div class="code-block">
# Create a simple loop for continuous testing
while true; do
    echo "Starting backtest cycle at $(date)"
    python test_trading_strategy_agent.py
    echo "Backtest completed. Waiting 1 hour..."
    sleep 3600
done
                </div>
            </div>

            <h3>💾 Exporting Results</h3>
            <div class="step">
                <h4>Export trading data to CSV:</h4>
                <div class="code-block">
# Export all trades to CSV
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
COPY (
    SELECT symbol, action, quantity, price, confidence, timestamp, strategy_id
    FROM trades
    ORDER BY timestamp DESC
) TO STDOUT WITH CSV HEADER;" > trades_export.csv

echo "Trades exported to trades_export.csv"
                </div>
            </div>
        </section>

        <section id="file-dependencies" class="section">
            <h2>🔗 Complete File Dependencies Map</h2>

            <div class="info">
                <strong>📋 Understanding Dependencies:</strong><br>
                This section shows you which files depend on which other files, helping you understand the system architecture.
            </div>

            <h3>Core System Dependencies</h3>
            <div class="file-tree">
main.py
├── config/settings.py
├── agents/base_agent.py
│   ├── shared/utils/base_agent.py
│   └── shared/types/agent_types.py
└── agents/data_agent.py
    └── agents/data_collector/data_collection_agent.py

trading_strategy_agent.py
├── shared/utils/base_agent.py
├── shared/types/agent_types.py
├── shared/types/strategy_types.py
├── agents/trading_strategy/risk_management/risk_manager.py
├── agents/trading_strategy/strategies/mean_reversion_strategy.py
│   ├── shared/utils/base_strategy.py
│   └── shared/types/strategy_types.py
└── agents/trading_strategy/strategies/momentum_strategy.py
    ├── shared/utils/base_strategy.py
    └── shared/types/strategy_types.py

risk_manager.py
├── shared/types/strategy_types.py
└── shared/mathematical/formulas.py

base_strategy.py
├── shared/types/strategy_types.py
└── shared/mathematical/utils.py
            </div>

            <h3>Database Dependencies</h3>
            <table>
                <tr>
                    <th>Component</th>
                    <th>Database Tables Used</th>
                    <th>SQL Files Required</th>
                </tr>
                <tr>
                    <td>Data Collection Agent</td>
                    <td>market_data, news_data</td>
                    <td>database_schema.sql</td>
                </tr>
                <tr>
                    <td>Trading Strategy Agent</td>
                    <td>trades, position_closures, portfolio_snapshots</td>
                    <td>setup_trading_tables.sql</td>
                </tr>
                <tr>
                    <td>Risk Manager</td>
                    <td>risk_metrics, strategy_performance</td>
                    <td>setup_trading_tables.sql</td>
                </tr>
                <tr>
                    <td>Test Scripts</td>
                    <td>All tables (read-only)</td>
                    <td>Both SQL files</td>
                </tr>
            </table>

            <h3>Import Chain Analysis</h3>
            <div class="code-block">
# To understand what imports what, run:
python -c "
import ast
import os

def analyze_imports(file_path):
    with open(file_path, 'r') as f:
        content = f.read()

    tree = ast.parse(content)
    imports = []

    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.append(node.module)

    return imports

# Analyze key files
key_files = [
    'main.py',
    'agents/trading_strategy/trading_strategy_agent.py',
    'test_trading_strategy_agent.py'
]

for file_path in key_files:
    if os.path.exists(file_path):
        imports = analyze_imports(file_path)
        print(f'\n{file_path} imports:')
        for imp in imports[:10]:  # Show first 10
            print(f'  - {imp}')
"
            </div>
        </section>

        <div class="section">
            <h2>🧮 Ready to Test Your Mathematical Ideas!</h2>

            <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>🧮 You Now Have Everything You Need:</strong><br>
                • ✅ Understanding of the REAL mathematical architecture<br>
                • ✅ API keys setup for data collection (DeepSeek, NewsAPI, Alpha Vantage)<br>
                • ✅ How to test individual mathematical components (calculus, probability, matrix)<br>
                • ✅ How to run comprehensive mathematical backtests<br>
                • ✅ How to read mathematical analysis results<br>
                • ✅ Database queries to see mathematical impact on trading
            </div>

            <div class="step">
                <h4>🚀 Your Mathematical Backtesting Workflow:</h4>
                <ol>
                    <li><strong>Set up API keys</strong> - Get DeepSeek, NewsAPI, Alpha Vantage for real data</li>
                    <li><strong>Test mathematical components</strong> - Run <code>python test_mathematical_engine.py</code></li>
                    <li><strong>Collect data for analysis</strong> - Run <code>python main.py</code> to get market data</li>
                    <li><strong>Run mathematical backtests</strong> - Run <code>python test_trading_strategy_agent.py</code></li>
                    <li><strong>Analyze mathematical results</strong> - Use database queries to see mathematical impact</li>
                    <li><strong>Customize your math</strong> - Edit <code>shared/mathematical/formulas.py</code> with YOUR ideas</li>
                    <li><strong>Iterate and improve</strong> - Refine your mathematical trading models</li>
                </ol>
            </div>

            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
                <h3 style="color: white; margin-bottom: 15px;">🧠 Remember: This is YOUR Mathematical Laboratory</h3>
                <p>The simple "mean reversion" and "momentum" strategies are just templates. The real power is in:</p>
                <ul>
                    <li>🧮 <strong>Your calculus formulas</strong> for momentum and trend analysis</li>
                    <li>📊 <strong>Your probability models</strong> for market predictions</li>
                    <li>🔢 <strong>Your matrix operations</strong> for portfolio optimization</li>
                    <li>🔍 <strong>Your pattern recognition</strong> for signal confirmation</li>
                    <li>🤖 <strong>AI interpretation</strong> of your mathematical results</li>
                </ul>
                <p><strong>This system exists to test YOUR mathematical ideas - use it!</strong></p>
            </div>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>💡 Pro Tip:</strong> Start with <code>python test_mathematical_engine.py</code> to verify your math works, then run <code>python main.py</code> to collect data, then <code>python test_trading_strategy_agent.py</code> to see how your math affects trading!
            </div>

            <div class="info">
                <strong>📚 Key Files for Your Mathematical Ideas:</strong><br>
                • <code>shared/mathematical/formulas.py</code> - Add your calculus, probability, matrix formulas<br>
                • <code>.env</code> - Your API keys (DeepSeek, NewsAPI, Alpha Vantage)<br>
                • <code>test_mathematical_engine.py</code> - Test your mathematical components<br>
                • Database tables <code>trades</code> and <code>analysis_results</code> - See mathematical impact
            </div>
        </div>

        <footer style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; margin-top: 40px;">
            <p><strong>🧮 AstroA Mathematical Trading Engine</strong></p>
            <p><em>Your mathematical ideas + Real market data + AI analysis = Better trading decisions</em></p>
            <p>This is your mathematical laboratory - experiment, test, and discover!</p>
        </footer>
    </div>
</body>
</html>