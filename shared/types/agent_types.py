"""
Base types and enums for the Multi-Agent Trading System
"""
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import uuid

class AgentType(Enum):
    DATA_COLLECTOR = "data_collector"
    MATHEMATICAL_ENGINE = "mathematical_engine"
    TRADING_STRATEGY = "trading_strategy"
    COMMUNICATION = "communication"

class AgentStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    ERROR = "error"
    STOPPED = "stopped"

class MessageType(Enum):
    DATA_REQUEST = "data_request"
    DATA_RESPONSE = "data_response"
    ANALYSIS_REQUEST = "analysis_request"
    ANALYSIS_RESPONSE = "analysis_response"
    TRADE_SIGNAL = "trade_signal"
    ALERT = "alert"
    HEARTBEAT = "heartbeat"

@dataclass
class AgentMessage:
    id: str
    sender_id: str
    receiver_id: str
    message_type: MessageType
    payload: Dict[str, Any]
    timestamp: datetime
    priority: int = 1

@dataclass
class MarketData:
    symbol: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    exchange: str
    timeframe: str

@dataclass
class NewsData:
    title: str
    content: str
    url: str
    source: str
    published_at: datetime
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None
    symbols: List[str] = None

@dataclass
class AnalysisResult:
    symbol: str
    timestamp: datetime
    analysis_type: str
    result: Dict[str, Any]
    confidence: float
    metadata: Dict[str, Any]