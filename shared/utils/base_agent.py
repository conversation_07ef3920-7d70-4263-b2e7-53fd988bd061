"""
Base Agent class for the Multi-Agent Trading System
"""
import asyncio
import logging
import redis
import json
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
import uuid

from shared.types.agent_types import (
    AgentType, AgentStatus, MessageType, AgentMessage
)

class BaseAgent(ABC):
    """Base class for all trading system agents"""

    def __init__(
        self,
        agent_id: str,
        agent_type: AgentType,
        redis_host: str = "localhost",
        redis_port: int = 6379,
        redis_db: int = 0
    ):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.status = AgentStatus.IDLE
        self.created_at = datetime.now()
        self.last_heartbeat = datetime.now()

        # Setup logging
        self.logger = self._setup_logging()

        # Setup Redis connection for inter-agent communication
        self.redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True
        )

        # Message handlers
        self.message_handlers: Dict[MessageType, Callable] = {}
        self._register_default_handlers()

        # Running flag
        self.running = False

        self.logger.info(f"Agent {self.agent_id} ({self.agent_type.value}) initialized")

    def _setup_logging(self) -> logging.Logger:
        """Setup agent-specific logging"""
        logger = logging.getLogger(f"agent.{self.agent_id}")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.FileHandler(f"logs/agents/{self.agent_id}.log")
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _register_default_handlers(self):
        """Register default message handlers"""
        self.message_handlers[MessageType.HEARTBEAT] = self._handle_heartbeat

    async def _handle_heartbeat(self, message: AgentMessage):
        """Handle heartbeat messages"""
        self.last_heartbeat = datetime.now()
        self.logger.debug(f"Heartbeat received from {message.sender_id}")

    def register_handler(self, message_type: MessageType, handler: Callable):
        """Register a message handler for a specific message type"""
        self.message_handlers[message_type] = handler
        self.logger.info(f"Registered handler for {message_type.value}")

    async def send_message(self, receiver_id: str, message_type: MessageType, payload: Dict[str, Any], priority: int = 1):
        """Send a message to another agent"""
        message = AgentMessage(
            id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            payload=payload,
            timestamp=datetime.now(),
            priority=priority
        )

        channel = f"agent:{receiver_id}:messages"
        message_data = {
            "id": message.id,
            "sender_id": message.sender_id,
            "receiver_id": message.receiver_id,
            "message_type": message.message_type.value,
            "payload": message.payload,
            "timestamp": message.timestamp.isoformat(),
            "priority": message.priority
        }

        self.redis_client.lpush(channel, json.dumps(message_data))
        self.logger.debug(f"Sent {message_type.value} message to {receiver_id}")

    async def receive_messages(self) -> List[AgentMessage]:
        """Receive messages from the message queue"""
        channel = f"agent:{self.agent_id}:messages"
        messages = []

        while True:
            message_data = self.redis_client.brpop(channel, timeout=1)
            if not message_data:
                break

            try:
                data = json.loads(message_data[1])
                message = AgentMessage(
                    id=data["id"],
                    sender_id=data["sender_id"],
                    receiver_id=data["receiver_id"],
                    message_type=MessageType(data["message_type"]),
                    payload=data["payload"],
                    timestamp=datetime.fromisoformat(data["timestamp"]),
                    priority=data["priority"]
                )
                messages.append(message)
            except Exception as e:
                self.logger.error(f"Error parsing message: {e}")

        return messages

    async def process_messages(self):
        """Process incoming messages"""
        messages = await self.receive_messages()

        for message in messages:
            if message.message_type in self.message_handlers:
                try:
                    await self.message_handlers[message.message_type](message)
                except Exception as e:
                    self.logger.error(f"Error handling {message.message_type.value}: {e}")
            else:
                self.logger.warning(f"No handler for message type: {message.message_type.value}")

    async def start(self):
        """Start the agent"""
        self.running = True
        self.status = AgentStatus.RUNNING
        self.logger.info(f"Agent {self.agent_id} starting...")

        try:
            await self.initialize()

            while self.running:
                await self.process_messages()
                await self.execute_main_logic()
                await asyncio.sleep(1)  # Prevent tight loop

        except Exception as e:
            self.logger.error(f"Agent error: {e}")
            self.status = AgentStatus.ERROR
        finally:
            await self.cleanup()
            self.status = AgentStatus.STOPPED
            self.logger.info(f"Agent {self.agent_id} stopped")

    async def stop(self):
        """Stop the agent"""
        self.logger.info(f"Agent {self.agent_id} stopping...")
        self.running = False

    @abstractmethod
    async def initialize(self):
        """Initialize agent-specific resources"""
        pass

    @abstractmethod
    async def execute_main_logic(self):
        """Execute the main agent logic"""
        pass

    @abstractmethod
    async def cleanup(self):
        """Cleanup agent-specific resources"""
        pass

    def get_status(self) -> Dict[str, Any]:
        """Get agent status information"""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "last_heartbeat": self.last_heartbeat.isoformat(),
            "running": self.running
        }