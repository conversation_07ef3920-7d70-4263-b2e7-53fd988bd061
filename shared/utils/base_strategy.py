"""
Base Strategy class for all trading strategies
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd

from shared.types.strategy_types import (
    StrategyType, TradingSignal, Position, Order
)

class BaseStrategy(ABC):
    """Base class for all trading strategies"""

    def __init__(
        self,
        strategy_id: str,
        strategy_type: StrategyType,
        symbols: List[str],
        parameters: Dict[str, Any] = None
    ):
        self.strategy_id = strategy_id
        self.strategy_type = strategy_type
        self.symbols = symbols
        self.parameters = parameters or {}

        # Strategy state
        self.is_active = False
        self.created_at = datetime.now()
        self.last_update = datetime.now()

        # Performance tracking
        self.total_signals = 0
        self.successful_signals = 0
        self.total_pnl = 0.0

        # Setup logging
        self.logger = logging.getLogger(f"strategy.{self.strategy_id}")

        self.logger.info(f"Strategy {self.strategy_id} ({self.strategy_type.value}) initialized")

    @abstractmethod
    async def generate_signals(self, market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate trading signals based on market data and analysis"""
        pass

    @abstractmethod
    async def calculate_position_size(self, signal: TradingSignal,
                                    portfolio_value: float,
                                    risk_budget: float) -> float:
        """Calculate optimal position size for a signal"""
        pass

    @abstractmethod
    async def should_exit_position(self, position: Position,
                                 current_data: Dict[str, Any]) -> bool:
        """Determine if a position should be closed"""
        pass

    @abstractmethod
    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        pass

    async def update_parameters(self, new_parameters: Dict[str, Any]):
        """Update strategy parameters"""
        self.parameters.update(new_parameters)
        self.last_update = datetime.now()
        self.logger.info(f"Strategy parameters updated: {new_parameters}")

    async def activate(self):
        """Activate the strategy"""
        self.is_active = True
        self.logger.info(f"Strategy {self.strategy_id} activated")

    async def deactivate(self):
        """Deactivate the strategy"""
        self.is_active = False
        self.logger.info(f"Strategy {self.strategy_id} deactivated")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        success_rate = (self.successful_signals / max(self.total_signals, 1)) * 100

        return {
            "strategy_id": self.strategy_id,
            "strategy_type": self.strategy_type.value,
            "is_active": self.is_active,
            "total_signals": self.total_signals,
            "successful_signals": self.successful_signals,
            "success_rate": success_rate,
            "total_pnl": self.total_pnl,
            "created_at": self.created_at.isoformat(),
            "last_update": self.last_update.isoformat()
        }

    def record_signal_result(self, signal: TradingSignal, pnl: float):
        """Record the result of a trading signal"""
        self.total_signals += 1
        if pnl > 0:
            self.successful_signals += 1
        self.total_pnl += pnl

        self.logger.info(f"Signal result recorded: PnL={pnl:.2f}, Success Rate={self.successful_signals/self.total_signals*100:.1f}%")