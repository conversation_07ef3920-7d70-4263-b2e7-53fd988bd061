"""
Advanced Mathematical Formulas for Trading Analysis
Based on calculus, probability, and matrix mathematics
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Union, Optional
from scipy import stats, optimize, integrate
from scipy.special import comb
import warnings

class CalculusFormulas:
    """Calculus-based formulas for rate of change and accumulation analysis"""

    @staticmethod
    def derivative_price_momentum(prices: pd.Series, window: int = 5) -> pd.Series:
        """Calculate price momentum using derivative approximation"""
        # Using limit definition: f'(x) = lim(h->0) [f(x+h) - f(x)] / h
        return prices.diff(window) / window

    @staticmethod
    def second_derivative_acceleration(prices: pd.Series) -> pd.Series:
        """Calculate price acceleration using second derivative"""
        first_deriv = prices.diff()
        return first_deriv.diff()

    @staticmethod
    def integral_volume_accumulation(volumes: pd.Series, window: int = 20) -> pd.Series:
        """Calculate volume accumulation using integration principles"""
        # Approximating integral using cumulative sum
        return volumes.rolling(window=window).sum()

    @staticmethod
    def tangent_line_resistance(prices: pd.Series, point_idx: int, window: int = 10) -> Dict:
        """Calculate tangent line at a point (support/resistance)"""
        if point_idx < window or point_idx >= len(prices) - window:
            return None

        # Calculate derivative at the point
        h = 1  # step size
        derivative = (prices.iloc[point_idx + h] - prices.iloc[point_idx - h]) / (2 * h)

        # Tangent line: y - y0 = m(x - x0)
        y0 = prices.iloc[point_idx]
        x0 = point_idx

        return {
            'slope': derivative,
            'intercept': y0 - derivative * x0,
            'point': (x0, y0),
            'equation': f"y = {derivative:.4f}x + {y0 - derivative * x0:.4f}"
        }

    @staticmethod
    def area_under_curve_strength(prices: pd.Series, start_idx: int, end_idx: int) -> float:
        """Calculate area under price curve (trend strength)"""
        if start_idx >= end_idx or end_idx > len(prices):
            return 0.0

        price_segment = prices.iloc[start_idx:end_idx]
        # Using trapezoidal rule for numerical integration
        return np.trapz(price_segment.values)

class ProbabilityFormulas:
    """Probability-based formulas for market prediction and risk analysis"""

    @staticmethod
    def normal_distribution_price_probability(prices: pd.Series, target_price: float) -> Dict:
        """Calculate probability of reaching target price using normal distribution"""
        returns = prices.pct_change().dropna()
        mean_return = returns.mean()
        std_return = returns.std()

        # Convert to price probability
        current_price = prices.iloc[-1]
        required_return = (target_price - current_price) / current_price

        # Z-score calculation
        z_score = (required_return - mean_return) / std_return
        probability = 1 - stats.norm.cdf(abs(z_score))

        return {
            'target_price': target_price,
            'current_price': current_price,
            'required_return': required_return,
            'z_score': z_score,
            'probability': probability,
            'confidence': 1 - probability
        }

    @staticmethod
    def binomial_trading_success(win_rate: float, num_trades: int, min_wins: int) -> float:
        """Calculate probability of achieving minimum wins in trading"""
        total_prob = 0.0
        for k in range(min_wins, num_trades + 1):
            prob_k = comb(num_trades, k) * (win_rate ** k) * ((1 - win_rate) ** (num_trades - k))
            total_prob += prob_k
        return total_prob

    @staticmethod
    def geometric_distribution_waiting_time(success_rate: float, target_trades: int) -> Dict:
        """Calculate expected waiting time for successful trade"""
        # Geometric distribution: P(X = k) = (1-p)^(k-1) * p
        probability = ((1 - success_rate) ** (target_trades - 1)) * success_rate
        expected_value = 1 / success_rate

        return {
            'probability_at_trade': probability,
            'expected_waiting_time': expected_value,
            'success_rate': success_rate
        }

    @staticmethod
    def conditional_probability_market_state(prices: pd.Series, volume: pd.Series) -> Dict:
        """Calculate conditional probabilities for market states"""
        # Define market states
        price_change = prices.pct_change()
        high_volume = volume > volume.median()
        positive_return = price_change > 0

        # P(positive return | high volume)
        high_vol_positive = np.sum(high_volume & positive_return)
        total_high_vol = np.sum(high_volume)
        prob_positive_given_high_vol = high_vol_positive / total_high_vol if total_high_vol > 0 else 0

        # P(high volume | positive return)
        total_positive = np.sum(positive_return)
        prob_high_vol_given_positive = high_vol_positive / total_positive if total_positive > 0 else 0

        return {
            'prob_positive_given_high_volume': prob_positive_given_high_vol,
            'prob_high_volume_given_positive': prob_high_vol_given_positive,
            'total_observations': len(prices),
            'high_volume_observations': total_high_vol,
            'positive_return_observations': total_positive
        }

class MatrixFormulas:
    """Matrix-based formulas for portfolio optimization and correlation analysis"""

    @staticmethod
    def correlation_matrix_analysis(returns_df: pd.DataFrame) -> Dict:
        """Calculate and analyze correlation matrix"""
        corr_matrix = returns_df.corr()

        # Calculate determinant (measure of independence)
        det = np.linalg.det(corr_matrix.values)

        # Calculate eigenvalues and eigenvectors
        eigenvalues, eigenvectors = np.linalg.eig(corr_matrix.values)

        # Find maximum and minimum correlations
        corr_values = corr_matrix.values
        np.fill_diagonal(corr_values, np.nan)  # Exclude diagonal
        max_corr = np.nanmax(corr_values)
        min_corr = np.nanmin(corr_values)

        return {
            'correlation_matrix': corr_matrix,
            'determinant': det,
            'eigenvalues': eigenvalues,
            'eigenvectors': eigenvectors,
            'max_correlation': max_corr,
            'min_correlation': min_corr,
            'condition_number': np.max(eigenvalues) / np.min(eigenvalues)
        }

    @staticmethod
    def portfolio_optimization_matrix(returns_df: pd.DataFrame, target_return: float) -> Dict:
        """Portfolio optimization using matrix operations"""
        returns = returns_df.values
        mean_returns = np.mean(returns, axis=0)
        cov_matrix = np.cov(returns.T)

        n_assets = len(mean_returns)

        try:
            # Inverse of covariance matrix
            inv_cov = np.linalg.inv(cov_matrix)

            # Ones vector
            ones = np.ones((n_assets, 1))

            # Calculate optimal weights using matrix algebra
            A = np.dot(ones.T, np.dot(inv_cov, ones))[0, 0]
            B = np.dot(ones.T, np.dot(inv_cov, mean_returns.reshape(-1, 1)))[0, 0]
            C = np.dot(mean_returns.T, np.dot(inv_cov, mean_returns))

            # Efficient frontier parameters
            discriminant = C * A - B ** 2

            if discriminant <= 0:
                return {"error": "No efficient frontier exists"}

            # Optimal weights for target return
            lambda_val = (C - target_return * B) / discriminant
            gamma_val = (target_return * A - B) / discriminant

            weights = lambda_val * np.dot(inv_cov, ones).flatten() + gamma_val * np.dot(inv_cov, mean_returns)

            # Portfolio variance
            portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))

            return {
                'optimal_weights': weights,
                'target_return': target_return,
                'portfolio_variance': portfolio_variance,
                'portfolio_std': np.sqrt(portfolio_variance),
                'sharpe_ratio': target_return / np.sqrt(portfolio_variance),
                'weights_sum': np.sum(weights)
            }

        except np.linalg.LinAlgError:
            return {"error": "Covariance matrix is singular"}

    @staticmethod
    def factor_model_analysis(returns_df: pd.DataFrame, market_returns: pd.Series) -> Dict:
        """Factor model analysis using matrix regression"""
        y = returns_df.values  # Asset returns
        x = market_returns.values.reshape(-1, 1)  # Market factor

        # Add intercept term
        X = np.column_stack([np.ones(len(x)), x])

        try:
            # Matrix solution: beta = (X'X)^(-1)X'y
            XtX_inv = np.linalg.inv(np.dot(X.T, X))
            betas = np.dot(np.dot(XtX_inv, X.T), y)

            # Calculate R-squared for each asset
            y_pred = np.dot(X, betas)
            residuals = y - y_pred

            ss_res = np.sum(residuals ** 2, axis=0)
            ss_tot = np.sum((y - np.mean(y, axis=0)) ** 2, axis=0)
            r_squared = 1 - (ss_res / ss_tot)

            return {
                'alphas': betas[0, :],  # Intercepts
                'betas': betas[1, :],   # Market betas
                'r_squared': r_squared,
                'residuals': residuals,
                'asset_names': returns_df.columns.tolist()
            }

        except np.linalg.LinAlgError:
            return {"error": "Matrix inversion failed"}

class AdvancedMathematicalIndicators:
    """Advanced mathematical indicators combining multiple mathematical concepts"""

    @staticmethod
    def calculus_momentum_oscillator(prices: pd.Series, window: int = 14) -> pd.Series:
        """Momentum oscillator using calculus derivatives"""
        # First derivative (velocity)
        velocity = prices.diff()

        # Second derivative (acceleration)
        acceleration = velocity.diff()

        # Combine velocity and acceleration
        momentum_score = velocity.rolling(window).mean() + 0.5 * acceleration.rolling(window).mean()

        # Normalize to oscillator range [-1, 1]
        momentum_std = momentum_score.rolling(window * 2).std()
        return momentum_score / (momentum_std * 2)

    @staticmethod
    def probability_trend_strength(prices: pd.Series, window: int = 20) -> pd.Series:
        """Trend strength using probability concepts"""
        returns = prices.pct_change()

        # Calculate rolling probability of positive returns
        positive_prob = returns.rolling(window).apply(lambda x: np.sum(x > 0) / len(x))

        # Convert to trend strength score
        trend_strength = 2 * (positive_prob - 0.5)  # Range [-1, 1]

        return trend_strength

    @staticmethod
    def matrix_diversification_ratio(returns_df: pd.DataFrame, window: int = 60) -> pd.Series:
        """Diversification ratio using matrix operations"""
        def calc_div_ratio(returns_window):
            if len(returns_window) < 10:
                return np.nan

            weights = np.ones(returns_window.shape[1]) / returns_window.shape[1]  # Equal weights

            # Portfolio variance using matrix operations
            cov_matrix = np.cov(returns_window.T)
            portfolio_var = np.dot(weights.T, np.dot(cov_matrix, weights))

            # Average individual variance
            individual_vars = np.diag(cov_matrix)
            avg_individual_var = np.mean(individual_vars)

            # Diversification ratio
            return avg_individual_var / portfolio_var if portfolio_var > 0 else 1

        # Rolling diversification ratio
        return returns_df.rolling(window).apply(calc_div_ratio, raw=False).iloc[:, 0]

# Integration class for combined mathematical analysis
class IntegratedMathematicalAnalysis:
    """Integrated mathematical analysis combining calculus, probability, and matrices"""

    def __init__(self):
        self.calculus = CalculusFormulas()
        self.probability = ProbabilityFormulas()
        self.matrix = MatrixFormulas()
        self.indicators = AdvancedMathematicalIndicators()

    def comprehensive_market_analysis(self, price_data: pd.DataFrame, volume_data: pd.Series = None) -> Dict:
        """Comprehensive analysis using all mathematical concepts"""
        results = {}

        if 'close_price' not in price_data.columns:
            return {"error": "close_price column required"}

        close_prices = price_data['close_price']

        # Calculus analysis
        results['calculus'] = {
            'momentum': self.calculus.derivative_price_momentum(close_prices),
            'acceleration': self.calculus.second_derivative_acceleration(close_prices),
            'trend_strength': self.calculus.area_under_curve_strength(close_prices, 0, len(close_prices))
        }

        # Probability analysis
        if len(close_prices) > 30:
            target_price = close_prices.iloc[-1] * 1.05  # 5% upside
            results['probability'] = self.probability.normal_distribution_price_probability(close_prices, target_price)

        # Matrix analysis (if multiple assets)
        if len(price_data.columns) > 1:
            returns_df = price_data.pct_change().dropna()
            results['matrix'] = self.matrix.correlation_matrix_analysis(returns_df)

        # Advanced indicators
        results['indicators'] = {
            'momentum_oscillator': self.indicators.calculus_momentum_oscillator(close_prices),
            'trend_strength': self.indicators.probability_trend_strength(close_prices)
        }

        return results