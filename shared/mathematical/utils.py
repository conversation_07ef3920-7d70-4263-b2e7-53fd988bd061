"""
Mathematical utility functions for trading analysis
"""
import numpy as np
import pandas as pd
from typing import Union, List, Tuple, Optional
from scipy import stats
import warnings

def safe_divide(numerator: Union[float, np.ndarray, pd.Series],
                denominator: Union[float, np.ndarray, pd.Series],
                default: float = 0.0) -> Union[float, np.ndarray, pd.Series]:
    """Safely divide two numbers, handling division by zero"""
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        result = np.divide(numerator, denominator)

        if isinstance(result, pd.Series):
            # Handle pandas Series
            result = result.fillna(default)
            result = result.replace([np.inf, -np.inf], default)
        elif isinstance(result, np.ndarray):
            # Handle numpy arrays
            result[np.isnan(result)] = default
            result[np.isinf(result)] = default
        elif isinstance(result, (float, int, np.number)):
            # Handle scalars
            if np.isnan(result) or np.isinf(result):
                result = default

    return result

def normalize_series(series: pd.Series, method: str = 'zscore') -> pd.Series:
    """Normalize a pandas series using various methods"""
    if method == 'zscore':
        return (series - series.mean()) / series.std()
    elif method == 'minmax':
        return (series - series.min()) / (series.max() - series.min())
    elif method == 'robust':
        median = series.median()
        mad = np.median(np.abs(series - median))
        return (series - median) / mad
    else:
        raise ValueError(f"Unknown normalization method: {method}")

def rolling_window(data: np.ndarray, window: int) -> np.ndarray:
    """Create rolling windows from array data"""
    shape = data.shape[:-1] + (data.shape[-1] - window + 1, window)
    strides = data.strides + (data.strides[-1],)
    return np.lib.stride_tricks.as_strided(data, shape=shape, strides=strides)

def calculate_returns(prices: pd.Series, method: str = 'simple') -> pd.Series:
    """Calculate returns from price series"""
    if method == 'simple':
        return prices.pct_change()
    elif method == 'log':
        return np.log(prices / prices.shift(1))
    else:
        raise ValueError(f"Unknown return calculation method: {method}")

def detect_outliers(data: pd.Series, method: str = 'iqr', threshold: float = 1.5) -> pd.Series:
    """Detect outliers in data using various methods"""
    if method == 'iqr':
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - threshold * IQR
        upper_bound = Q3 + threshold * IQR
        return (data < lower_bound) | (data > upper_bound)
    elif method == 'zscore':
        z_scores = np.abs(stats.zscore(data))
        return z_scores > threshold
    else:
        raise ValueError(f"Unknown outlier detection method: {method}")

def smooth_series(series: pd.Series, method: str = 'ewm', alpha: float = 0.1) -> pd.Series:
    """Smooth a time series using various methods"""
    if method == 'ewm':
        return series.ewm(alpha=alpha).mean()
    elif method == 'sma':
        window = int(1 / alpha)
        return series.rolling(window=window).mean()
    elif method == 'savgol':
        from scipy.signal import savgol_filter
        window_length = min(len(series), 51)
        if window_length % 2 == 0:
            window_length -= 1
        return pd.Series(savgol_filter(series, window_length, 3), index=series.index)
    else:
        raise ValueError(f"Unknown smoothing method: {method}")

def calculate_confidence_interval(data: pd.Series, confidence: float = 0.95) -> Tuple[float, float]:
    """Calculate confidence interval for data"""
    mean = data.mean()
    sem = stats.sem(data)
    h = sem * stats.t.ppf((1 + confidence) / 2., len(data) - 1)
    return mean - h, mean + h

def calculate_derivative(prices: pd.Series, method: str = 'gradient') -> pd.Series:
    """Calculate derivatives (rate of change) using calculus principles"""
    if method == 'gradient':
        # Numerical gradient approximation
        return pd.Series(np.gradient(prices.values), index=prices.index)
    elif method == 'diff':
        # Simple difference approximation
        return prices.diff()
    elif method == 'central_diff':
        # Central difference formula
        return (prices.shift(-1) - prices.shift(1)) / 2
    else:
        raise ValueError(f"Unknown derivative method: {method}")

def calculate_integral(series: pd.Series, method: str = 'trapz') -> float:
    """Calculate integral (accumulation) using calculus principles"""
    if method == 'trapz':
        return np.trapz(series.values)
    elif method == 'simpson':
        from scipy.integrate import simpson
        return simpson(series.values)
    elif method == 'cumsum':
        return series.sum()
    else:
        raise ValueError(f"Unknown integration method: {method}")

def matrix_determinant(matrix: np.ndarray) -> float:
    """Calculate matrix determinant"""
    return np.linalg.det(matrix)

def matrix_inverse(matrix: np.ndarray) -> np.ndarray:
    """Calculate matrix inverse if it exists"""
    try:
        return np.linalg.inv(matrix)
    except np.linalg.LinAlgError:
        return None

def calculate_probability_density(data: pd.Series, x_values: np.ndarray) -> np.ndarray:
    """Calculate probability density function for normal distribution"""
    mean = data.mean()
    std = data.std()
    return stats.norm.pdf(x_values, mean, std)

def binomial_probability(n: int, k: int, p: float) -> float:
    """Calculate binomial probability"""
    from math import comb
    return comb(n, k) * (p ** k) * ((1 - p) ** (n - k))

def validate_data_quality(data: pd.DataFrame, min_data_points: int = 100) -> dict:
    """Validate data quality for mathematical analysis"""
    results = {
        'total_points': len(data),
        'missing_values': data.isnull().sum().sum(),
        'duplicate_rows': data.duplicated().sum(),
        'data_quality_score': 0.0,
        'warnings': []
    }

    # Check minimum data points
    if results['total_points'] < min_data_points:
        results['warnings'].append(f"Insufficient data points: {results['total_points']} < {min_data_points}")

    # Check missing values
    missing_percentage = (results['missing_values'] / (len(data) * len(data.columns))) * 100
    if missing_percentage > 5:
        results['warnings'].append(f"High missing values: {missing_percentage:.1f}%")

    # Calculate data quality score
    quality_score = 100
    quality_score -= min(missing_percentage * 2, 50)  # Penalty for missing values
    quality_score -= min((results['duplicate_rows'] / len(data)) * 100, 20)  # Penalty for duplicates

    results['data_quality_score'] = max(quality_score, 0)

    return results