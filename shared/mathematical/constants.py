"""
Mathematical constants and configuration for trading analysis
"""
import numpy as np

# Statistical Constants
CONFIDENCE_LEVELS = {
    '90%': 1.645,
    '95%': 1.96,
    '99%': 2.576
}

# Technical Analysis Parameters
DEFAULT_PERIODS = {
    'short_ma': 9,
    'medium_ma': 21,
    'long_ma': 50,
    'bollinger_period': 20,
    'rsi_period': 14,
    'stochastic_period': 14
}

# Risk Management Constants
RISK_FREE_RATE = 0.02  # 2% annual risk-free rate
TRADING_DAYS_PER_YEAR = 252
HOURS_PER_DAY = 24

# Correlation Thresholds
CORRELATION_THRESHOLDS = {
    'strong_positive': 0.7,
    'moderate_positive': 0.3,
    'weak': 0.1,
    'moderate_negative': -0.3,
    'strong_negative': -0.7
}

# VaR Confidence Levels
VAR_CONFIDENCE_LEVELS = [0.90, 0.95, 0.99]

# Pattern Recognition Parameters
PATTERN_TOLERANCE = 0.02  # 2% tolerance for pattern matching
MIN_PATTERN_LENGTH = 5
MAX_PATTERN_LENGTH = 50

# Mathematical Constants from Calculus
GOLDEN_RATIO = 1.618033988749
EULER_NUMBER = np.e
PI = np.pi

# Probability Distribution Parameters
NORMAL_DISTRIBUTION_PARAMS = {
    'mean': 0,
    'std': 1
}

# Matrix Operations
MATRIX_TOLERANCE = 1e-10
MAX_ITERATIONS = 1000