#!/usr/bin/env python3
"""
AstroA Simple 2-Hour Backtest
Simplified version that focuses on core backtesting functionality
"""
import asyncio
import logging
import sys
import json
import time
import random
from datetime import datetime, timedelta
from pathlib import Path
import psycopg2
import pandas as pd
from typing import Dict, List, Any

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Config

class SimpleBacktestRunner:
    """Simplified backtest runner for AstroA system"""

    def __init__(self):
        self.start_time = datetime.now()
        self.end_time = self.start_time + timedelta(hours=2)

        # Portfolio simulation
        self.initial_value = 100000
        self.current_cash = 80000
        self.positions = {}

        # Market data simulation
        self.symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'SOL/USDT', 'DOT/USDT', 'LINK/USDT', 'AVAX/USDT', 'MATIC/USDT']
        self.prices = {
            'BTC/USDT': 43500.00,
            'ETH/USDT': 2850.00,
            'ADA/USDT': 0.47,
            'SOL/USDT': 165.00,
            'DOT/USDT': 6.85,
            'LINK/USDT': 15.20,
            'AVAX/USDT': 28.50,
            'MATIC/USDT': 0.75
        }

        # Performance tracking
        self.performance_data = {
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'portfolio_snapshots': [],
            'trades_executed': [],
            'data_points_collected': 0,
            'analysis_cycles': 0,
            'errors': [],
            'risk_assessments': [],
            'market_conditions': []
        }

        self.setup_logging()
        self.logger = logging.getLogger('SimpleBacktest')

    def setup_logging(self):
        """Setup logging"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        Config.LOGS_DIR.mkdir(exist_ok=True)
        log_file = Config.LOGS_DIR / f"backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

    def simulate_market_movement(self):
        """Simulate realistic market movements"""
        for symbol in self.symbols:
            # Market volatility simulation
            if symbol.startswith('BTC'):
                volatility = 0.025  # Bitcoin: 2.5% max move
            elif symbol.startswith('ETH'):
                volatility = 0.03   # Ethereum: 3% max move
            else:
                volatility = 0.035  # Altcoins: 3.5% max move

            # Add trend bias (slightly bullish market)
            trend_bias = 0.002  # 0.2% bullish bias

            change = random.uniform(-volatility, volatility) + trend_bias
            self.prices[symbol] *= (1 + change)

            # Ensure prices don't go too low
            if self.prices[symbol] < 0.01:
                self.prices[symbol] = 0.01

    def generate_ai_analysis(self):
        """Generate AI market analysis"""
        market_sentiments = ['bullish', 'bearish', 'neutral', 'mixed']
        risk_levels = ['low', 'medium', 'high']

        # Weighted towards more realistic market conditions
        sentiment_weights = [0.3, 0.2, 0.4, 0.1]  # Slightly bullish bias
        risk_weights = [0.2, 0.6, 0.2]  # Medium risk most common

        return {
            'market_sentiment': random.choices(market_sentiments, weights=sentiment_weights)[0],
            'volatility_index': round(random.uniform(0.15, 0.45), 3),
            'trend_strength': round(random.uniform(0.2, 0.9), 3),
            'risk_level': random.choices(risk_levels, weights=risk_weights)[0],
            'confidence_score': round(random.uniform(0.65, 0.92), 3),
            'support_resistance': {
                'btc_support': round(self.prices['BTC/USDT'] * 0.95, 2),
                'btc_resistance': round(self.prices['BTC/USDT'] * 1.05, 2),
                'eth_support': round(self.prices['ETH/USDT'] * 0.94, 2),
                'eth_resistance': round(self.prices['ETH/USDT'] * 1.06, 2)
            },
            'cross_asset_correlation': round(random.uniform(0.6, 0.85), 3)
        }

    def generate_trading_signal(self, market_analysis):
        """Generate trading signals based on market analysis"""
        signals = []

        # Signal generation probability based on market conditions
        base_probability = 0.15  # 15% base chance

        # Adjust probability based on analysis
        if market_analysis['trend_strength'] > 0.7:
            base_probability += 0.1
        if market_analysis['confidence_score'] > 0.85:
            base_probability += 0.05
        if market_analysis['volatility_index'] > 0.35:
            base_probability += 0.05  # More signals in volatile markets

        for symbol in self.symbols:
            if random.random() < base_probability:
                # Determine signal type based on market sentiment
                if market_analysis['market_sentiment'] == 'bullish':
                    action_weights = [0.7, 0.2, 0.1]  # buy, sell, hold
                    actions = ['buy', 'sell', 'hold']
                elif market_analysis['market_sentiment'] == 'bearish':
                    action_weights = [0.2, 0.7, 0.1]
                    actions = ['buy', 'sell', 'hold']
                else:
                    action_weights = [0.4, 0.4, 0.2]
                    actions = ['buy', 'sell', 'hold']

                action = random.choices(actions, weights=action_weights)[0]

                if action != 'hold':
                    # Position sizing based on market conditions
                    base_size = random.uniform(0.5, 3.0)

                    # Reduce size in high volatility
                    if market_analysis['volatility_index'] > 0.35:
                        base_size *= 0.7

                    # Increase size for high confidence
                    if market_analysis['confidence_score'] > 0.85:
                        base_size *= 1.2

                    strategy_id = 'momentum_001' if market_analysis['trend_strength'] > 0.6 else 'mean_reversion_001'

                    signal = {
                        'symbol': symbol,
                        'action': action,
                        'quantity': round(base_size, 2),
                        'price': self.prices[symbol],
                        'timestamp': datetime.now().isoformat(),
                        'strategy_id': strategy_id,
                        'confidence': market_analysis['confidence_score'],
                        'metadata': {
                            'market_sentiment': market_analysis['market_sentiment'],
                            'volatility': market_analysis['volatility_index'],
                            'trend_strength': market_analysis['trend_strength']
                        }
                    }
                    signals.append(signal)

        return signals

    def execute_trade(self, signal):
        """Execute a trading signal"""
        symbol = signal['symbol']
        action = signal['action']
        quantity = signal['quantity']
        price = signal['price']
        trade_value = quantity * price

        # Risk management checks
        max_position_value = self.current_cash * 0.2  # Max 20% of cash per position
        if trade_value > max_position_value:
            quantity = max_position_value / price
            trade_value = quantity * price
            signal['quantity'] = round(quantity, 2)

        executed = False

        if action == 'buy' and trade_value <= self.current_cash:
            self.current_cash -= trade_value
            if symbol in self.positions:
                self.positions[symbol] += quantity
            else:
                self.positions[symbol] = quantity
            executed = True
            self.logger.info(f"🟢 BUY {quantity:.2f} {symbol} at ${price:.2f} (${trade_value:.2f})")

        elif action == 'sell' and symbol in self.positions and self.positions[symbol] >= quantity:
            self.current_cash += trade_value
            self.positions[symbol] -= quantity
            if self.positions[symbol] <= 0.001:  # Clean up small positions
                del self.positions[symbol]
            executed = True
            self.logger.info(f"🔴 SELL {quantity:.2f} {symbol} at ${price:.2f} (${trade_value:.2f})")

        if executed:
            self.performance_data['trades_executed'].append(signal)

        return executed

    def calculate_portfolio_value(self):
        """Calculate current portfolio value"""
        positions_value = 0
        for symbol, quantity in self.positions.items():
            positions_value += quantity * self.prices[symbol]

        total_value = self.current_cash + positions_value
        return total_value, positions_value

    def take_portfolio_snapshot(self):
        """Take detailed portfolio snapshot"""
        total_value, positions_value = self.calculate_portfolio_value()

        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'total_value': total_value,
            'cash': self.current_cash,
            'positions_value': positions_value,
            'total_pnl': total_value - self.initial_value,
            'daily_pnl': 0,  # Could calculate if we had historical data
            'positions_count': len(self.positions),
            'risk_score': random.uniform(0.2, 0.7),
            'max_drawdown': 0,  # Would calculate from history
            'positions': []
        }

        # Add position details
        for symbol, quantity in self.positions.items():
            position_value = quantity * self.prices[symbol]
            snapshot['positions'].append({
                'symbol': symbol,
                'quantity': quantity,
                'entry_price': self.prices[symbol],  # Simplified
                'current_price': self.prices[symbol],
                'unrealized_pnl': 0,  # Would need entry price tracking
                'position_type': 'long',
                'value': position_value
            })

        self.performance_data['portfolio_snapshots'].append(snapshot)

        # Add risk assessment
        self.performance_data['risk_assessments'].append({
            'timestamp': datetime.now().isoformat(),
            'portfolio_value': total_value,
            'risk_score': snapshot['risk_score'],
            'positions_count': len(self.positions),
            'total_pnl': total_value - self.initial_value,
            'cash_ratio': self.current_cash / total_value
        })

        return snapshot

    async def run_backtest_cycle(self, cycle_number, total_cycles):
        """Run a single backtest cycle"""
        self.logger.info(f"📊 Running cycle {cycle_number}/{total_cycles}")

        # Simulate market movement
        self.simulate_market_movement()

        # Generate AI analysis
        market_analysis = self.generate_ai_analysis()
        self.performance_data['market_conditions'].append({
            'timestamp': datetime.now().isoformat(),
            'analysis': market_analysis
        })

        # Simulate data collection
        self.performance_data['data_points_collected'] += len(self.symbols) * 3  # 3 timeframes

        # Generate and execute trading signals
        signals = self.generate_trading_signal(market_analysis)
        trades_executed = 0
        for signal in signals:
            if self.execute_trade(signal):
                trades_executed += 1

        # Take portfolio snapshot
        snapshot = self.take_portfolio_snapshot()

        # Log cycle results
        pnl_percent = (snapshot['total_value'] - self.initial_value) / self.initial_value * 100
        self.logger.info(f"💰 Portfolio: ${snapshot['total_value']:,.2f} ({pnl_percent:+.2f}%) | "
                        f"Cash: ${self.current_cash:,.2f} | Positions: {len(self.positions)} | "
                        f"Trades: {trades_executed} | Sentiment: {market_analysis['market_sentiment']}")

        self.performance_data['analysis_cycles'] = cycle_number

    async def run_full_backtest(self):
        """Run the complete 2-hour backtest"""
        self.logger.info(f"🚀 Starting 2-hour backtest from {self.start_time} to {self.end_time}")
        self.logger.info(f"💰 Initial Portfolio: ${self.initial_value:,}")

        cycle_count = 0
        cycle_interval = 60  # 1 minute per cycle = 120 cycles for 2 hours
        last_snapshot_time = self.start_time

        try:
            while datetime.now() < self.end_time:
                cycle_count += 1

                # Calculate total expected cycles
                total_time_seconds = (self.end_time - self.start_time).total_seconds()
                total_cycles = int(total_time_seconds / cycle_interval)

                await self.run_backtest_cycle(cycle_count, total_cycles)

                # Show progress
                elapsed = (datetime.now() - self.start_time).total_seconds()
                progress = (elapsed / (2 * 3600)) * 100
                remaining_minutes = (2 * 60) - (elapsed / 60)

                print(f"⏱️  Progress: {progress:.1f}% | Remaining: {remaining_minutes:.1f} minutes")

                # Wait for next cycle
                await asyncio.sleep(cycle_interval)

        except KeyboardInterrupt:
            self.logger.info("🛑 Backtest interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Error during backtest: {str(e)}")
            self.performance_data['errors'].append({
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'type': 'backtest_error'
            })

        self.logger.info(f"🏁 Backtest completed after {cycle_count} cycles")

    def calculate_performance_metrics(self):
        """Calculate final performance metrics"""
        snapshots = self.performance_data['portfolio_snapshots']
        if not snapshots:
            return {}

        initial_value = snapshots[0]['total_value']
        final_value = snapshots[-1]['total_value']

        # Calculate returns
        total_return = (final_value - initial_value) / initial_value

        # Calculate max drawdown
        peak_value = initial_value
        max_drawdown = 0
        drawdown_values = []

        for snapshot in snapshots:
            value = snapshot['total_value']
            peak_value = max(peak_value, value)
            drawdown = (peak_value - value) / peak_value
            max_drawdown = max(max_drawdown, drawdown)
            drawdown_values.append(drawdown)

        # Calculate volatility
        returns = []
        for i in range(1, len(snapshots)):
            prev_value = snapshots[i-1]['total_value']
            curr_value = snapshots[i]['total_value']
            returns.append((curr_value - prev_value) / prev_value)

        avg_return = sum(returns) / len(returns) if returns else 0
        volatility = (sum((r - avg_return) ** 2 for r in returns) / len(returns)) ** 0.5 if returns else 0
        sharpe_ratio = (avg_return * len(returns)) / volatility if volatility > 0 else 0

        # Calculate win rate
        winning_trades = sum(1 for trade in self.performance_data['trades_executed']
                           if trade.get('realized_pnl', 0) > 0)
        total_trades = len(self.performance_data['trades_executed'])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        return {
            'initial_value': initial_value,
            'final_value': final_value,
            'total_return': total_return,
            'total_return_percent': total_return * 100,
            'max_drawdown': max_drawdown,
            'max_drawdown_percent': max_drawdown * 100,
            'sharpe_ratio': sharpe_ratio,
            'volatility': volatility * 100,
            'total_trades': total_trades,
            'win_rate': win_rate * 100,
            'analysis_cycles': self.performance_data['analysis_cycles'],
            'error_count': len(self.performance_data['errors']),
            'avg_positions': sum(s['positions_count'] for s in snapshots) / len(snapshots),
            'duration_hours': (datetime.now() - self.start_time).total_seconds() / 3600,
            'data_points_collected': self.performance_data['data_points_collected']
        }

    def save_results(self):
        """Save backtest results"""
        # Calculate final metrics
        metrics = self.calculate_performance_metrics()
        self.performance_data['performance_metrics'] = metrics
        self.performance_data['end_time'] = datetime.now().isoformat()

        # Save to file
        Config.DATA_DIR.mkdir(exist_ok=True)
        results_file = Config.DATA_DIR / f"backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(results_file, 'w') as f:
            json.dump(self.performance_data, f, indent=2)

        self.logger.info(f"📊 Results saved to: {results_file}")
        self.print_summary(metrics)

        return results_file

    def print_summary(self, metrics):
        """Print backtest summary"""
        print("\n" + "="*70)
        print("🌟 ASTROA 2-HOUR BACKTEST SUMMARY")
        print("="*70)
        print(f"⏱️  Duration: {metrics['duration_hours']:.1f} hours")
        print(f"💰 Initial Portfolio: ${metrics['initial_value']:,.2f}")
        print(f"💰 Final Portfolio: ${metrics['final_value']:,.2f}")
        print(f"📈 Total Return: {metrics['total_return_percent']:+.2f}%")
        print(f"📉 Max Drawdown: {metrics['max_drawdown_percent']:.2f}%")
        print(f"📊 Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"📈 Volatility: {metrics['volatility']:.2f}%")
        print(f"🔄 Total Trades: {metrics['total_trades']}")
        print(f"🎯 Win Rate: {metrics['win_rate']:.1f}%")
        print(f"📊 Analysis Cycles: {metrics['analysis_cycles']}")
        print(f"📍 Avg Positions: {metrics['avg_positions']:.1f}")
        print(f"📊 Data Points: {metrics['data_points_collected']:,}")
        print(f"⚠️  Errors: {metrics['error_count']}")
        print("="*70)

async def main():
    """Main backtest execution"""
    runner = SimpleBacktestRunner()

    try:
        await runner.run_full_backtest()
        results_file = runner.save_results()
        return results_file

    except Exception as e:
        runner.logger.error(f"❌ Backtest failed: {str(e)}", exc_info=True)
        raise

if __name__ == '__main__':
    print("🚀 Starting AstroA 2-Hour Backtest...")
    print("📊 This will run for 2 hours collecting comprehensive performance data")
    print("💡 Press Ctrl+C to stop early and generate results\n")

    try:
        results_file = asyncio.run(main())
        print(f"\n✅ Backtest completed successfully!")
        print(f"📊 Results saved to: {results_file}")
        print(f"\n🎯 Generate visualization with:")
        print(f"python generate_backtest_visualization.py {results_file}")
    except KeyboardInterrupt:
        print("\n🛑 Backtest stopped by user")
    except Exception as e:
        print(f"\n❌ Backtest failed: {str(e)}")
        sys.exit(1)