<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freqtrade Integration Guide - AstroA Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .freqtrade-bg {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        }
        .step-card {
            border-left: 4px solid #3b82f6;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #3b82f6;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(59, 130, 246, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid #3b82f6;
            color: #3b82f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .copy-button:hover {
            background: rgba(59, 130, 246, 0.3);
        }
        .warning-box {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            border: 2px solid #f59e0b;
            color: #92400e;
        }
        .success-box {
            background: linear-gradient(45deg, #d1fae5, #a7f3d0);
            border: 2px solid #059669;
            color: #065f46;
        }
        .info-box {
            background: linear-gradient(45deg, #dbeafe, #bfdbfe);
            border: 2px solid #2563eb;
            color: #1e40af;
        }
        .code-file {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            margin: 16px 0;
        }
        .file-header {
            background: #1e293b;
            padding: 8px 16px;
            border-bottom: 1px solid #334155;
            font-family: 'Ubuntu Mono', monospace;
            color: #94a3b8;
            font-size: 14px;
        }
        .file-content {
            padding: 16px;
            color: #e2e8f0;
            font-family: 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .nav-tabs {
            display: flex;
            background: #1e293b;
            border-radius: 8px 8px 0 0;
        }
        .nav-tab {
            padding: 12px 24px;
            cursor: pointer;
            background: #334155;
            color: #94a3b8;
            border-right: 1px solid #475569;
            transition: all 0.3s ease;
        }
        .nav-tab:first-child {
            border-radius: 8px 0 0 0;
        }
        .nav-tab:last-child {
            border-right: none;
            border-radius: 0 8px 0 0;
        }
        .nav-tab.active {
            background: #3b82f6;
            color: white;
        }
        .nav-tab:hover {
            background: #475569;
        }
        .tab-content {
            display: none;
            background: #0f172a;
            padding: 24px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #334155;
            border-top: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="freqtrade-bg text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex items-center space-x-4">
                <i data-lucide="trending-up" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-4xl font-bold">Freqtrade Integration Guide</h1>
                    <p class="text-xl opacity-90">Connect Your AstroA System to Live Trading</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8">
        <!-- Navigation -->
        <div class="mb-8">
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showTab('overview')">
                    <i data-lucide="info" class="w-4 h-4 inline mr-2"></i>Overview
                </div>
                <div class="nav-tab" onclick="showTab('installation')">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>Installation
                </div>
                <div class="nav-tab" onclick="showTab('configuration')">
                    <i data-lucide="settings" class="w-4 h-4 inline mr-2"></i>Configuration
                </div>
                <div class="nav-tab" onclick="showTab('strategies')">
                    <i data-lucide="brain" class="w-4 h-4 inline mr-2"></i>Strategy Integration
                </div>
                <div class="nav-tab" onclick="showTab('deployment')">
                    <i data-lucide="rocket" class="w-4 h-4 inline mr-2"></i>Deployment
                </div>
                <div class="nav-tab" onclick="showTab('monitoring')">
                    <i data-lucide="activity" class="w-4 h-4 inline mr-2"></i>Monitoring
                </div>
            </div>

            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🎯 Integration Overview</h2>

                <div class="warning-box p-6 rounded-lg mb-6">
                    <div class="flex items-center mb-3">
                        <i data-lucide="alert-triangle" class="w-6 h-6 mr-3"></i>
                        <h3 class="text-xl font-bold">⚠️ Important Prerequisites</h3>
                    </div>
                    <ul class="space-y-2">
                        <li>✅ AstroA system must show 60+ days of profitable paper trading</li>
                        <li>✅ Maximum drawdown under 10%</li>
                        <li>✅ Sharpe ratio > 1.0</li>
                        <li>✅ Real capital ready for deployment ($1,000+ minimum)</li>
                        <li>✅ Exchange API keys configured</li>
                    </ul>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="zap" class="w-6 h-6 inline mr-2 text-blue-600"></i>
                            What Freqtrade Adds
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>🔗 Direct broker connectivity (100+ exchanges)</li>
                            <li>⚡ Real-time order execution</li>
                            <li>📊 Advanced backtesting validation</li>
                            <li>🔄 24/7 automated trading</li>
                            <li>📱 Web UI for monitoring</li>
                            <li>🛡️ Built-in risk management</li>
                        </ul>
                    </div>

                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="cpu" class="w-6 h-6 inline mr-2 text-green-600"></i>
                            AstroA Integration Points
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>🧠 Convert Mean Reversion strategy</li>
                            <li>📈 Convert Momentum strategy</li>
                            <li>🔄 Data collection pipeline integration</li>
                            <li>⚖️ Risk management translation</li>
                            <li>📊 Performance metrics mapping</li>
                            <li>🔔 Alert system connection</li>
                        </ul>
                    </div>
                </div>

                <div class="info-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="calendar" class="w-6 h-6 inline mr-2"></i>
                        Integration Timeline
                    </h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">1</div>
                            <p class="font-semibold">Week 1-2</p>
                            <p class="text-sm">Installation & Setup</p>
                        </div>
                        <div class="text-center">
                            <div class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">2</div>
                            <p class="font-semibold">Week 3-4</p>
                            <p class="text-sm">Strategy Translation</p>
                        </div>
                        <div class="text-center">
                            <div class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">3</div>
                            <p class="font-semibold">Week 5+</p>
                            <p class="text-sm">Live Trading</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Installation Tab -->
            <div id="installation" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📦 Installation & Setup</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 1: Install Freqtrade</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Navigate to your AstroA directory
cd /home/<USER>/axmadcodes/AstroA

# Activate your virtual environment
source venv/bin/activate

# Install freqtrade with all dependencies
pip install freqtrade[all]

# Verify installation
freqtrade --version</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 2: Create Freqtrade Directory Structure</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create freqtrade user directory
freqtrade create-userdir --userdir freqtrade_data

# Create AstroA integration directory
mkdir -p freqtrade_data/astroa_integration
mkdir -p freqtrade_data/strategies/astroa
mkdir -p freqtrade_data/user_data/notebooks</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 3: Initialize Configuration</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Generate base configuration
freqtrade new-config --config freqtrade_data/config.json

# Create AstroA-specific config
freqtrade new-config --config freqtrade_data/astroa_config.json</div>
                </div>

                <div class="success-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="check-circle" class="w-6 h-6 inline mr-2"></i>
                        Directory Structure Created
                    </h3>
                    <div class="command-block">
freqtrade_data/
├── config.json              # Main configuration
├── astroa_config.json        # AstroA-specific config
├── strategies/
│   └── astroa/              # AstroA strategy conversions
├── user_data/
│   ├── data/                # Market data storage
│   ├── logs/                # Trading logs
│   └── notebooks/           # Analysis notebooks
└── astroa_integration/      # Integration scripts</div>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div id="configuration" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">⚙️ Configuration Setup</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Exchange Configuration</h3>

                    <div class="code-file">
                        <div class="file-header">freqtrade_data/astroa_config.json</div>
                        <div class="file-content">{
  "max_open_trades": 5,
  "stake_currency": "USDT",
  "stake_amount": 1000,
  "tradable_balance_ratio": 0.99,
  "fiat_display_currency": "USD",
  "timeframe": "1h",

  "dry_run": true,
  "dry_run_wallet": 10000,

  "cancel_open_orders_on_exit": false,

  "unfilledtimeout": {
    "entry": 10,
    "exit": 10,
    "exit_timeout_count": 0,
    "unit": "minutes"
  },

  "entry_pricing": {
    "price_side": "same",
    "use_order_book": true,
    "order_book_top": 1,
    "price_last_balance": 0.0,
    "check_depth_of_market": {
      "enabled": false,
      "bids_to_ask_delta": 1
    }
  },

  "exit_pricing": {
    "price_side": "same",
    "use_order_book": true,
    "order_book_top": 1
  },

  "exchange": {
    "name": "binance",
    "key": "your_api_key_here",
    "secret": "your_secret_here",
    "ccxt_config": {},
    "ccxt_async_config": {},
    "pair_whitelist": [
      "BTC/USDT",
      "ETH/USDT",
      "ADA/USDT",
      "SOL/USDT",
      "DOT/USDT",
      "LINK/USDT",
      "AVAX/USDT",
      "MATIC/USDT"
    ],
    "pair_blacklist": [
      "*/BNB"
    ]
  },

  "pairlists": [
    {
      "method": "StaticPairList"
    }
  ],

  "edge": {
    "enabled": false,
    "process_throttle_secs": 3600,
    "calculate_since_number_of_days": 7,
    "allowed_risk": 0.01,
    "stoploss_range_min": -0.01,
    "stoploss_range_max": -0.1,
    "stoploss_range_step": -0.01,
    "minimum_winrate": 0.60,
    "minimum_expectancy": 0.20,
    "min_trade_number": 10,
    "max_trade_duration_minute": 1440,
    "remove_pumps": false
  },

  "telegram": {
    "enabled": false,
    "token": "your_telegram_token_here",
    "chat_id": "your_telegram_chat_id_here"
  },

  "api_server": {
    "enabled": true,
    "listen_ip_address": "127.0.0.1",
    "listen_port": 8080,
    "verbosity": "error",
    "enable_openapi": false,
    "jwt_secret_key": "your_jwt_secret_here",
    "CORS_origins": [],
    "username": "freqtrade",
    "password": "your_password_here"
  },

  "bot_name": "AstroA-Freqtrade",
  "initial_state": "running",
  "force_entry_enable": false,
  "internals": {
    "process_throttle_secs": 5
  }
}</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Environment Variables Setup</h3>

                    <div class="code-file">
                        <div class="file-header">freqtrade_data/.env</div>
                        <div class="file-content"># Exchange API Keys
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET=your_binance_secret_here

# Telegram Notifications
TELEGRAM_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# API Server
FREQTRADE_JWT_SECRET=your_jwt_secret_here
FREQTRADE_PASSWORD=your_secure_password

# AstroA Integration
ASTROA_DB_HOST=localhost
ASTROA_DB_NAME=mathematical_trading
ASTROA_DB_USER=trading_user
ASTROA_DB_PASSWORD=hejhej
DEEPSEEK_API_KEY=your_deepseek_key</div>
                    </div>

                    <div class="warning-box p-4 rounded-lg mt-4">
                        <p><strong>⚠️ Security Note:</strong> Never commit API keys to version control. Use environment variables or secure key management.</p>
                    </div>
                </div>
            </div>

            <!-- Strategy Integration Tab -->
            <div id="strategies" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🧠 Strategy Integration</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Convert Mean Reversion Strategy</h3>

                    <div class="code-file">
                        <div class="file-header">freqtrade_data/strategies/astroa/AstroaMeanReversion.py</div>
                        <div class="file-content">import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, CategoricalParameter, DecimalParameter, IntParameter
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
import logging

class AstroaMeanReversion(IStrategy):
    """
    AstroA Mean Reversion Strategy - Freqtrade Implementation
    Converted from agents/trading_strategy/strategies/mean_reversion_strategy.py
    """

    # Strategy interface version
    INTERFACE_VERSION = 3

    # ROI table:
    minimal_roi = {
        "0": 0.04,    # 4% profit target
        "15": 0.02,   # 2% after 15 minutes
        "30": 0.01,   # 1% after 30 minutes
        "60": 0      # Break even after 1 hour
    }

    # Stoploss
    stoploss = -0.02  # 2% stop loss (matches AstroA risk management)

    # Timeframe
    timeframe = '1h'

    # Can this strategy go short?
    can_short = False

    # Run "populate_indicators" only for new candle
    process_only_new_candles = True

    # Experimental settings (configuration will overide these if set)
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    # Optional order type mapping
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Strategy parameters (optimizable)
    z_score_entry = DecimalParameter(1.5, 3.0, default=2.0, space="buy")
    z_score_exit = DecimalParameter(0.2, 1.0, default=0.5, space="sell")
    lookback_period = IntParameter(10, 30, default=20, space="buy")
    min_confidence = DecimalParameter(0.6, 0.9, default=0.7, space="buy")

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Populate indicators that will be used in entry and exit signals.
        Translates AstroA mathematical indicators to freqtrade format.
        """

        # Calculate rolling statistics (from AstroA mean reversion logic)
        dataframe['rolling_mean'] = dataframe['close'].rolling(self.lookback_period.value).mean()
        dataframe['rolling_std'] = dataframe['close'].rolling(self.lookback_period.value).std()

        # Z-score calculation (core of AstroA mean reversion)
        dataframe['z_score'] = (dataframe['close'] - dataframe['rolling_mean']) / dataframe['rolling_std']

        # ATR for dynamic stop loss (matches AstroA risk management)
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        # Volume indicators
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']

        # Additional technical indicators for confidence scoring
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['bb_upperband'], dataframe['bb_middleband'], dataframe['bb_lowerband'] = ta.BBANDS(
            dataframe, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0
        )

        # Volatility indicator
        dataframe['volatility'] = dataframe['rolling_std'] / dataframe['rolling_mean']

        # Confidence score calculation (from AstroA risk assessment)
        dataframe['confidence'] = np.where(
            dataframe['z_score'].abs() > 2.0,
            np.minimum(dataframe['z_score'].abs() / 3.0, 1.0),
            0.0
        )

        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Populate the entry trend indicators.
        Based on AstroA mean reversion entry logic.
        """

        dataframe.loc[
            (
                # Core mean reversion signal
                (dataframe['z_score'] <= -self.z_score_entry.value) &

                # Confidence threshold
                (dataframe['confidence'] >= self.min_confidence.value) &

                # Volume confirmation
                (dataframe['volume_ratio'] > 0.8) &

                # RSI oversold confirmation
                (dataframe['rsi'] < 35) &

                # Not in extreme volatility
                (dataframe['volatility'] < 0.05) &

                # Ensure we have enough data
                (dataframe['rolling_std'] > 0)
            ),
            'enter_long'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Populate the exit trend indicators.
        Based on AstroA mean reversion exit logic.
        """

        dataframe.loc[
            (
                # Mean reversion exit signal
                (dataframe['z_score'] >= -self.z_score_exit.value) |

                # RSI overbought
                (dataframe['rsi'] > 70) |

                # Price above upper Bollinger Band
                (dataframe['close'] > dataframe['bb_upperband'])
            ),
            'exit_long'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: 'datetime',
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss implementation.
        Implements AstroA's dynamic ATR-based stop loss.
        """

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if 'atr' in last_candle:
            # Dynamic stop based on ATR (matches AstroA risk management)
            atr_multiplier = 1.5  # Conservative stop loss
            dynamic_stop = atr_multiplier * last_candle['atr'] / trade.open_rate

            # Never set stop loss more generous than the default
            return max(dynamic_stop, self.stoploss)

        return self.stoploss

    def leverage(self, pair: str, current_time: 'datetime', current_rate: float,
                proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                side: str, **kwargs) -> float:
        """
        Leverage control (matches AstroA conservative approach)
        """
        return 1.0  # No leverage, matches AstroA risk management

    def custom_stake_amount(self, pair: str, current_time: 'datetime',
                           current_rate: float, proposed_stake: float,
                           min_stake: Optional[float], max_stake: float,
                           leverage: float, entry_tag: Optional[str],
                           side: str, **kwargs) -> float:
        """
        Custom stake amount calculation.
        Implements AstroA's Kelly Criterion position sizing.
        """

        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if 'confidence' in last_candle and 'volatility' in last_candle:
            # Kelly Criterion adjustment based on confidence and volatility
            confidence = last_candle['confidence']
            volatility = last_candle['volatility']

            # Base position size (conservative)
            base_position = proposed_stake * 0.5

            # Adjust based on confidence (higher confidence = larger position)
            confidence_multiplier = 0.5 + (confidence * 1.0)

            # Adjust based on volatility (higher volatility = smaller position)
            volatility_multiplier = max(0.3, 1.0 - volatility * 5.0)

            adjusted_stake = base_position * confidence_multiplier * volatility_multiplier

            # Respect min/max constraints
            if min_stake:
                adjusted_stake = max(adjusted_stake, min_stake)
            adjusted_stake = min(adjusted_stake, max_stake)

            return adjusted_stake

        return proposed_stake * 0.5  # Conservative default

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                           rate: float, time_in_force: str, current_time: 'datetime',
                           entry_tag: Optional[str], side: str, **kwargs) -> bool:
        """
        Trade entry confirmation.
        Implements AstroA's final risk checks before entry.
        """

        # Get current portfolio information
        open_trades = len(Trade.get_trades_proxy(is_open=True))

        # Maximum 5 concurrent positions (matches AstroA risk management)
        if open_trades >= 5:
            return False

        # Additional risk checks can be added here
        return True

    def confirm_trade_exit(self, pair: str, trade: 'Trade', order_type: str,
                          amount: float, rate: float, time_in_force: str,
                          exit_reason: str, current_time: 'datetime', **kwargs) -> bool:
        """
        Trade exit confirmation.
        """
        return True</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Convert Momentum Strategy</h3>

                    <div class="code-file">
                        <div class="file-header">freqtrade_data/strategies/astroa/AstroaMomentum.py</div>
                        <div class="file-content">import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib
from freqtrade.strategy import IStrategy, CategoricalParameter, DecimalParameter, IntParameter
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple

class AstroaMomentum(IStrategy):
    """
    AstroA Momentum Strategy - Freqtrade Implementation
    Converted from agents/trading_strategy/strategies/momentum_strategy.py
    """

    INTERFACE_VERSION = 3

    # ROI table (aggressive for momentum)
    minimal_roi = {
        "0": 0.06,    # 6% profit target
        "20": 0.04,   # 4% after 20 minutes
        "40": 0.02,   # 2% after 40 minutes
        "80": 0       # Break even after 80 minutes
    }

    # Stoploss (wider for momentum)
    stoploss = -0.03  # 3% stop loss

    # Timeframe
    timeframe = '1h'

    # Strategy parameters
    fast_ma = IntParameter(5, 15, default=10, space="buy")
    slow_ma = IntParameter(20, 40, default=30, space="buy")
    rsi_overbought = IntParameter(65, 85, default=75, space="sell")
    rsi_oversold = IntParameter(15, 35, default=25, space="buy")
    momentum_threshold = DecimalParameter(0.02, 0.08, default=0.05, space="buy")

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Populate momentum indicators from AstroA strategy.
        """

        # Moving averages (core momentum signals)
        dataframe[f'ma_fast'] = ta.SMA(dataframe, timeperiod=self.fast_ma.value)
        dataframe[f'ma_slow'] = ta.SMA(dataframe, timeperiod=self.slow_ma.value)

        # RSI for momentum confirmation
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)

        # Price momentum calculation
        dataframe['momentum'] = (dataframe['close'] - dataframe['close'].shift(14)) / dataframe['close'].shift(14)

        # Volume momentum
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_momentum'] = dataframe['volume'] / dataframe['volume_sma']

        # ATR for position sizing
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        # MACD for trend confirmation
        macd, macdsignal, macdhist = ta.MACD(dataframe, fastperiod=12, slowperiod=26, signalperiod=9)
        dataframe['macd'] = macd
        dataframe['macdsignal'] = macdsignal
        dataframe['macdhist'] = macdhist

        # Bollinger Bands for volatility
        dataframe['bb_upperband'], dataframe['bb_middleband'], dataframe['bb_lowerband'] = ta.BBANDS(
            dataframe, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0
        )

        # Momentum strength calculation
        dataframe['momentum_strength'] = np.where(
            (dataframe['momentum'].abs() > self.momentum_threshold.value) &
            (dataframe['macd'] > dataframe['macdsignal']),
            np.minimum(dataframe['momentum'].abs() / 0.1, 1.0),
            0.0
        )

        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Momentum entry signals.
        """

        dataframe.loc[
            (
                # Moving average crossover (bullish)
                (dataframe['ma_fast'] > dataframe['ma_slow']) &

                # Price momentum confirmation
                (dataframe['momentum'] > self.momentum_threshold.value) &

                # RSI not overbought
                (dataframe['rsi'] > self.rsi_oversold.value) &
                (dataframe['rsi'] < self.rsi_overbought.value) &

                # MACD bullish
                (dataframe['macd'] > dataframe['macdsignal']) &
                (dataframe['macdhist'] > 0) &

                # Volume confirmation
                (dataframe['volume_momentum'] > 1.2) &

                # Price above middle Bollinger Band
                (dataframe['close'] > dataframe['bb_middleband']) &

                # Momentum strength threshold
                (dataframe['momentum_strength'] > 0.6)
            ),
            'enter_long'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        """
        Momentum exit signals.
        """

        dataframe.loc[
            (
                # Moving average bearish crossover
                (dataframe['ma_fast'] < dataframe['ma_slow']) |

                # RSI overbought
                (dataframe['rsi'] > self.rsi_overbought.value) |

                # MACD bearish divergence
                ((dataframe['macd'] < dataframe['macdsignal']) &
                 (dataframe['macdhist'] < 0)) |

                # Momentum loss
                (dataframe['momentum'] < 0) |

                # Volume decline
                (dataframe['volume_momentum'] < 0.8)
            ),
            'exit_long'] = 1

        return dataframe</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Data Integration Script</h3>

                    <div class="code-file">
                        <div class="file-header">freqtrade_data/astroa_integration/data_bridge.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
AstroA-Freqtrade Data Bridge
Synchronizes data between AstroA system and Freqtrade
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
import psycopg2
import json
from typing import Dict, List, Optional

# Add AstroA system to path
sys.path.append('/home/<USER>/axmadcodes/AstroA')

from config.settings import Config
from agents.data_collector.data_collection_agent import DataCollectionAgent

class AstroAFreqtradeBridge:
    """Bridge between AstroA data collection and Freqtrade"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_agent = DataCollectionAgent()
        self.db_config = {
            'host': 'localhost',
            'database': 'mathematical_trading',
            'user': 'trading_user',
            'password': 'hejhej'
        }

    async def sync_market_data(self, symbols: List[str], timeframes: List[str]) -> Dict:
        """Sync market data from AstroA to Freqtrade format"""

        try:
            # Collect data using AstroA agent
            result = await self.data_agent.execute(
                symbols=symbols,
                timeframes=timeframes
            )

            if result.status == 'success':
                # Convert to Freqtrade format
                freqtrade_data = self._convert_to_freqtrade_format(result.data)

                # Save to Freqtrade data directory
                self._save_freqtrade_data(freqtrade_data)

                return {'status': 'success', 'data': freqtrade_data}
            else:
                self.logger.error(f"Data collection failed: {result.error_message}")
                return {'status': 'error', 'message': result.error_message}

        except Exception as e:
            self.logger.error(f"Data sync error: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    def _convert_to_freqtrade_format(self, astroa_data: Dict) -> Dict:
        """Convert AstroA data format to Freqtrade format"""

        freqtrade_data = {}

        # Process crypto data
        if 'crypto_data' in astroa_data:
            for exchange, exchange_data in astroa_data['crypto_data'].items():
                for symbol, symbol_data in exchange_data.items():
                    for timeframe, ohlcv_data in symbol_data.items():
                        # Convert to Freqtrade OHLCV format
                        freqtrade_key = f"{symbol}_{timeframe}"
                        freqtrade_data[freqtrade_key] = [
                            [
                                int(candle['timestamp'].timestamp() * 1000),
                                float(candle['open']),
                                float(candle['high']),
                                float(candle['low']),
                                float(candle['close']),
                                float(candle['volume'])
                            ]
                            for candle in ohlcv_data
                        ]

        return freqtrade_data

    def _save_freqtrade_data(self, data: Dict):
        """Save data in Freqtrade format"""

        data_dir = "freqtrade_data/user_data/data/binance"
        os.makedirs(data_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for symbol_timeframe, ohlcv_data in data.items():
            filename = f"{data_dir}/{symbol_timeframe}_{timestamp}.json"

            with open(filename, 'w') as f:
                json.dump(ohlcv_data, f, indent=2)

            self.logger.info(f"Saved {len(ohlcv_data)} candles to {filename}")

    async def get_astroa_signals(self, symbol: str) -> Optional[Dict]:
        """Get latest trading signals from AstroA database"""

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # Get latest signal for symbol
            cursor.execute("""
                SELECT signal_type, confidence, metadata, created_at
                FROM trading_signals
                WHERE symbol = %s
                ORDER BY created_at DESC
                LIMIT 1
            """, (symbol,))

            result = cursor.fetchone()

            if result:
                return {
                    'signal_type': result[0],
                    'confidence': float(result[1]),
                    'metadata': result[2],
                    'timestamp': result[3]
                }

            return None

        except Exception as e:
            self.logger.error(f"Error getting AstroA signals: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

# Usage example
async def main():
    bridge = AstroAFreqtradeBridge()

    symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
    timeframes = ['1h', '4h']

    result = await bridge.sync_market_data(symbols, timeframes)
    print(f"Data sync result: {result['status']}")

if __name__ == "__main__":
    asyncio.run(main())</div>
                    </div>
                </div>
            </div>

            <!-- Deployment Tab -->
            <div id="deployment" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🚀 Deployment & Execution</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 1: Dry Run Testing</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Test AstroA Mean Reversion strategy
freqtrade backtesting \
  --config freqtrade_data/astroa_config.json \
  --strategy AstroaMeanReversion \
  --timeframe 1h \
  --timerange 20240701-20240901 \
  --export trades

# Test AstroA Momentum strategy
freqtrade backtesting \
  --config freqtrade_data/astroa_config.json \
  --strategy AstroaMomentum \
  --timeframe 1h \
  --timerange 20240701-20240901 \
  --export trades</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 2: Paper Trading</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Start paper trading with AstroA strategies
freqtrade trade \
  --config freqtrade_data/astroa_config.json \
  --strategy AstroaMeanReversion \
  --dry-run

# Monitor in separate terminal
freqtrade show-trades \
  --config freqtrade_data/astroa_config.json \
  --db-url sqlite:///freqtrade_data/tradesv3.dryrun.sqlite</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 3: Live Trading Setup</h3>
                    <div class="warning-box p-4 rounded-lg mb-4">
                        <p><strong>⚠️ Warning:</strong> Only proceed if paper trading shows consistent profitability for 60+ days!</p>
                    </div>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Update config for live trading
# Set dry_run: false in astroa_config.json
# Set stake_amount to actual capital allocation

# Start live trading (CAUTION!)
freqtrade trade \
  --config freqtrade_data/astroa_config.json \
  --strategy AstroaMeanReversion \
  --logfile freqtrade_data/logs/live_trading.log</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 4: Start Web UI</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Start Freqtrade web interface
freqtrade trade \
  --config freqtrade_data/astroa_config.json \
  --strategy AstroaMeanReversion \
  --enable-api-server

# Access web interface at:
# http://localhost:8080
# Username: freqtrade
# Password: your_password_here</div>
                </div>
            </div>

            <!-- Monitoring Tab -->
            <div id="monitoring" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 Monitoring & Management</h2>

                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="activity" class="w-6 h-6 inline mr-2 text-blue-600"></i>
                            Performance Monitoring
                        </h3>
                        <div class="command-block">
                            <div class="env-indicator">TERMINAL</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# View current status
freqtrade status --config freqtrade_data/astroa_config.json

# Show profit summary
freqtrade profit --config freqtrade_data/astroa_config.json

# Plot performance
freqtrade plot-profit \
  --config freqtrade_data/astroa_config.json \
  --exportfilename freqtrade_data/user_data/backtest_results/</div>
                    </div>

                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="database" class="w-6 h-6 inline mr-2 text-green-600"></i>
                            Data Management
                        </h3>
                        <div class="command-block">
                            <div class="env-indicator">TERMINAL</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Download latest market data
freqtrade download-data \
  --exchange binance \
  --pairs BTC/USDT ETH/USDT ADA/USDT \
  --timeframes 1h 4h 1d \
  --days 30

# Sync with AstroA data
python freqtrade_data/astroa_integration/data_bridge.py</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Risk Management Commands</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Emergency stop all trading
freqtrade stop --config freqtrade_data/astroa_config.json

# Force sell all positions
freqtrade forceexit all --config freqtrade_data/astroa_config.json

# Check current positions
freqtrade show-trades --config freqtrade_data/astroa_config.json</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Integration with AstroA Monitoring</h3>

                    <div class="code-file">
                        <div class="file-header">freqtrade_data/astroa_integration/monitor.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
AstroA-Freqtrade Monitor
Syncs performance data between systems
"""

import sys
import psycopg2
import json
import requests
from datetime import datetime, timedelta

class AstroAFreqtradeMonitor:
    """Monitor for tracking performance across both systems"""

    def __init__(self):
        self.freqtrade_api = "http://localhost:8080/api/v1"
        self.astroa_db = {
            'host': 'localhost',
            'database': 'mathematical_trading',
            'user': 'trading_user',
            'password': 'hejhej'
        }

    def get_freqtrade_performance(self):
        """Get performance data from Freqtrade API"""
        try:
            response = requests.get(f"{self.freqtrade_api}/profit")
            return response.json()
        except Exception as e:
            print(f"Error getting Freqtrade performance: {e}")
            return None

    def sync_to_astroa_db(self, performance_data):
        """Sync Freqtrade performance to AstroA database"""
        try:
            conn = psycopg2.connect(**self.astroa_db)
            cursor = conn.cursor()

            # Insert performance snapshot
            cursor.execute("""
                INSERT INTO portfolio_snapshots
                (timestamp, total_value, profit_loss, trade_count, source)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                datetime.now(),
                performance_data.get('profit_closed_coin', 0),
                performance_data.get('profit_all_coin', 0),
                performance_data.get('trade_count', 0),
                'freqtrade'
            ))

            conn.commit()
            print("Performance data synced to AstroA database")

        except Exception as e:
            print(f"Error syncing to AstroA: {e}")
        finally:
            if conn:
                conn.close()

if __name__ == "__main__":
    monitor = AstroAFreqtradeMonitor()
    perf = monitor.get_freqtrade_performance()
    if perf:
        monitor.sync_to_astroa_db(perf)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 bg-gray-800 text-white py-8">
            <div class="container mx-auto px-6 text-center">
                <p class="text-lg">🌟 AstroA Freqtrade Integration Guide</p>
                <p class="mt-2 opacity-75">Navigate the markets with stellar precision through live trading</p>
                <div class="mt-4 flex justify-center space-x-6">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="shield-check" class="w-5 h-5"></i>
                        <span>Risk-First Approach</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="trending-up" class="w-5 h-5"></i>
                        <span>Strategy Proven</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="cpu" class="w-5 h-5"></i>
                        <span>AI-Powered</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Copy to clipboard functionality
        function copyToClipboard(button) {
            const codeBlock = button.parentNode;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(function() {
                button.textContent = 'Copied!';
                setTimeout(function() {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        // Add click event listeners to all copy buttons
        document.addEventListener('DOMContentLoaded', function() {
            const copyButtons = document.querySelectorAll('.copy-button');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    copyToClipboard(this);
                });
            });
        });
    </script>
</body>
</html>