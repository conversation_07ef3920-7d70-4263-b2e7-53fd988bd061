<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Installation & Setup Guide - Mathematical Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .step-card {
            border-left: 4px solid #3b82f6;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1f2937;
            color: #10b981;
            font-family: 'Courier New', monospace;
            padding: 16px;
            border-radius: 8px;
            margin: 12px 0;
            position: relative;
            overflow-x: auto;
        }
        .copy-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #10b981;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .copy-button:hover {
            background: rgba(16, 185, 129, 0.3);
        }
        .platform-tabs {
            display: flex;
            margin-bottom: 16px;
        }
        .platform-tab {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: #f9fafb;
            cursor: pointer;
            transition: all 0.2s;
        }
        .platform-tab.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }
        .platform-tab:first-child {
            border-radius: 8px 0 0 8px;
        }
        .platform-tab:last-child {
            border-radius: 0 8px 8px 0;
        }
        .platform-content {
            display: none;
        }
        .platform-content.active {
            display: block;
        }
        .tool-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 1px solid #0284c7;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
        }
        .requirement-badge {
            display: inline-block;
            background: #dbeafe;
            color: #1e40af;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px;
        }
        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
        }
        .success-box {
            background: #dcfce7;
            border: 1px solid #16a34a;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
        }
        .info-box {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 12px;
            margin: 12px 0;
        }
        .step-number {
            width: 40px;
            height: 40px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            margin-right: 16px;
            flex-shrink: 0;
        }
        .verification-checklist {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            transition: background 0.2s;
        }
        .checklist-item:hover {
            background: #f1f5f9;
        }
        .expandable-section {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin: 16px 0;
            overflow: hidden;
        }
        .expandable-header {
            background: #f9fafb;
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            justify-content: between;
            align-items: center;
            border-bottom: 1px solid #e5e7eb;
        }
        .expandable-content {
            padding: 16px;
            display: none;
        }
        .expandable-content.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="gradient-bg text-white p-4 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i data-lucide="download" class="w-8 h-8"></i>
                <span class="text-xl font-bold">Complete Installation & Setup Guide</span>
                <span class="bg-white/20 px-2 py-1 rounded text-xs">STEP-BY-STEP</span>
            </div>
            <div class="flex space-x-4">
                <a href="#prerequisites" class="hover:text-blue-200">Prerequisites</a>
                <a href="#installation" class="hover:text-blue-200">Installation</a>
                <a href="#configuration" class="hover:text-blue-200">Configuration</a>
                <a href="#verification" class="hover:text-blue-200">Verification</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-16">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h1 class="text-4xl font-bold mb-6">Complete Installation & Setup Guide</h1>
            <p class="text-xl mb-8">Follow these exact steps to set up your Mathematical Trading System from scratch</p>
            <div class="flex justify-center space-x-6">
                <div class="bg-white/20 rounded-lg p-4">
                    <i data-lucide="terminal" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="font-semibold">Command Line</p>
                    <p class="text-sm">Copy & paste commands</p>
                </div>
                <div class="bg-white/20 rounded-lg p-4">
                    <i data-lucide="package" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="font-semibold">Package Managers</p>
                    <p class="text-sm">Automated installation</p>
                </div>
                <div class="bg-white/20 rounded-lg p-4">
                    <i data-lucide="check-circle" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="font-semibold">Verification</p>
                    <p class="text-sm">Test everything works</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Prerequisites Section -->
    <section id="prerequisites" class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Prerequisites & System Requirements</h2>

            <!-- System Requirements -->
            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <div class="tool-card">
                    <div class="flex items-center mb-4">
                        <i data-lucide="cpu" class="w-6 h-6 text-blue-600 mr-2"></i>
                        <h3 class="text-lg font-bold">Hardware Requirements</h3>
                    </div>
                    <ul class="space-y-2 text-sm">
                        <li>• <strong>CPU:</strong> 4+ cores (Intel i5/AMD Ryzen 5+)</li>
                        <li>• <strong>RAM:</strong> 8GB minimum, 16GB recommended</li>
                        <li>• <strong>Storage:</strong> 50GB free space (SSD preferred)</li>
                        <li>• <strong>Network:</strong> Stable internet connection</li>
                        <li>• <strong>OS:</strong> Windows 10+, macOS 10.15+, or Linux</li>
                    </ul>
                </div>

                <div class="tool-card">
                    <div class="flex items-center mb-4">
                        <i data-lucide="code" class="w-6 h-6 text-green-600 mr-2"></i>
                        <h3 class="text-lg font-bold">Software Prerequisites</h3>
                    </div>
                    <ul class="space-y-2 text-sm">
                        <li>• <strong>Python:</strong> 3.9 or higher</li>
                        <li>• <strong>Git:</strong> Version control</li>
                        <li>• <strong>Node.js:</strong> 18+ (for frontend)</li>
                        <li>• <strong>PostgreSQL:</strong> 13+ database</li>
                        <li>• <strong>Redis:</strong> In-memory cache</li>
                    </ul>
                </div>

                <div class="tool-card">
                    <div class="flex items-center mb-4">
                        <i data-lucide="key" class="w-6 h-6 text-purple-600 mr-2"></i>
                        <h3 class="text-lg font-bold">API Keys Needed</h3>
                    </div>
                    <ul class="space-y-2 text-sm">
                        <li>• <strong>Exchange APIs:</strong> Binance, Coinbase</li>
                        <li>• <strong>News API:</strong> NewsAPI.org</li>
                        <li>• <strong>Financial Data:</strong> Alpha Vantage</li>
                        <li>• <strong>AI Services:</strong> OpenAI (optional)</li>
                        <li>• <strong>Cloud:</strong> AWS/GCP (for production)</li>
                    </ul>
                </div>
            </div>

            <!-- Operating System Specific Instructions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold mb-6">Operating System Setup</h3>

                <!-- Platform Tabs -->
                <div class="platform-tabs">
                    <div class="platform-tab active" onclick="switchPlatform('windows')">
                        <i data-lucide="monitor" class="w-4 h-4 inline mr-2"></i>
                        Windows
                    </div>
                    <div class="platform-tab" onclick="switchPlatform('macos')">
                        <i data-lucide="laptop" class="w-4 h-4 inline mr-2"></i>
                        macOS
                    </div>
                    <div class="platform-tab" onclick="switchPlatform('linux')">
                        <i data-lucide="terminal" class="w-4 h-4 inline mr-2"></i>
                        Linux
                    </div>
                </div>

                <!-- Windows Content -->
                <div id="windows-content" class="platform-content active">
                    <h4 class="font-bold mb-3">Windows 10/11 Setup</h4>

                    <div class="info-box">
                        <strong>Recommended:</strong> Use Windows Subsystem for Linux (WSL2) for better compatibility with Python data science tools.
                    </div>

                    <h5 class="font-semibold mb-2">1. Install WSL2 (Recommended)</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Open PowerShell as Administrator and run:
wsl --install

# After restart, install Ubuntu:
wsl --install -d Ubuntu

# Set WSL2 as default:
wsl --set-default-version 2
                    </div>

                    <h5 class="font-semibold mb-2">2. Install Package Managers</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install Chocolatey (Package Manager for Windows)
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install tools via Chocolatey:
choco install python git nodejs postgresql redis-64
                    </div>

                    <h5 class="font-semibold mb-2">3. Alternative: Direct Downloads</h5>
                    <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                        <li><a href="https://www.python.org/downloads/" class="text-blue-600 hover:underline">Python 3.11+</a> - Official installer</li>
                        <li><a href="https://git-scm.com/download/win" class="text-blue-600 hover:underline">Git for Windows</a> - Version control</li>
                        <li><a href="https://nodejs.org/en/download/" class="text-blue-600 hover:underline">Node.js LTS</a> - JavaScript runtime</li>
                        <li><a href="https://www.postgresql.org/download/windows/" class="text-blue-600 hover:underline">PostgreSQL</a> - Database</li>
                        <li><a href="https://github.com/microsoftarchive/redis/releases" class="text-blue-600 hover:underline">Redis for Windows</a> - Cache server</li>
                    </ul>
                </div>

                <!-- macOS Content -->
                <div id="macos-content" class="platform-content">
                    <h4 class="font-bold mb-3">macOS Setup</h4>

                    <h5 class="font-semibold mb-2">1. Install Homebrew (Package Manager)</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Add Homebrew to PATH (follow the instructions after installation)
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
source ~/.zshrc
                    </div>

                    <h5 class="font-semibold mb-2">2. Install Development Tools</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install Xcode Command Line Tools
xcode-select --install

# Install Python, Git, Node.js, PostgreSQL, Redis
brew install python@3.11 git node postgresql redis

# Install additional tools
brew install --cask visual-studio-code
brew install --cask docker
                    </div>

                    <h5 class="font-semibold mb-2">3. Start Services</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Start PostgreSQL
brew services start postgresql

# Start Redis
brew services start redis

# Verify installations
python3 --version
git --version
node --version
psql --version
redis-cli --version
                    </div>
                </div>

                <!-- Linux Content -->
                <div id="linux-content" class="platform-content">
                    <h4 class="font-bold mb-3">Linux (Ubuntu/Debian) Setup</h4>

                    <h5 class="font-semibold mb-2">1. Update System</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Update package lists
sudo apt update && sudo apt upgrade -y

# Install essential build tools
sudo apt install -y build-essential curl wget git
                    </div>

                    <h5 class="font-semibold mb-2">2. Install Python 3.11+</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Add deadsnakes PPA for latest Python
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update

# Install Python 3.11 and pip
sudo apt install -y python3.11 python3.11-pip python3.11-venv python3.11-dev

# Set Python 3.11 as default
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
                    </div>

                    <h5 class="font-semibold mb-2">3. Install Node.js</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install Node.js via NodeSource
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version
npm --version
                    </div>

                    <h5 class="font-semibold mb-2">4. Install PostgreSQL & Redis</h5>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Install Redis
sudo apt install -y redis-server

# Start services
sudo systemctl start postgresql
sudo systemctl enable postgresql
sudo systemctl start redis-server
sudo systemctl enable redis-server
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Section -->
    <section id="installation" class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Step-by-Step Installation</h2>

            <!-- Step 1: Project Setup -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">1</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Create Project Directory & Virtual Environment</h3>

                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create project directory
mkdir mathematical-trading-system
cd mathematical-trading-system

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Virtual environment activated (prompt shows (venv))</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>pip version 23.0+ installed</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Install Core Dependencies -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">2</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Install Core Python Dependencies</h3>

                        <div class="warning-box">
                            <strong>⚠️ Important:</strong> Make sure your virtual environment is activated before installing packages!
                        </div>

                        <h4 class="font-semibold mb-2">Core Data Science Libraries</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Core data manipulation
pip install pandas numpy scipy matplotlib plotly seaborn

# Mathematical libraries
pip install sympy statsmodels

# Machine learning
pip install scikit-learn lightgbm xgboost

# Time series analysis
pip install prophet ta-lib

# Portfolio optimization
pip install pyportfolioopt cvxpy
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">API & Data Collection Libraries</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Cryptocurrency exchange APIs
pip install ccxt

# Traditional financial data
pip install yfinance alpha_vantage

# News and social media
pip install newsapi-python tweepy beautifulsoup4 requests

# Web scraping
pip install selenium webdriver-manager
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">AI & Agent Libraries</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# LLM and AI agent frameworks
pip install langchain openai anthropic

# Multi-agent systems
pip install autogen-agentchat

# Natural language processing
pip install transformers torch spacy nltk

# Download spaCy model
python -m spacy download en_core_web_sm
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Web Framework & Database</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Web framework
pip install fastapi uvicorn

# Database
pip install sqlalchemy psycopg2-binary alembic

# Caching and task queue
pip install redis celery

# Template engine for reports
pip install jinja2

# Development tools
pip install pytest black flake8 pre-commit
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All packages installed without errors</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Import test: <code>import pandas, numpy, ccxt, langchain</code></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Database Setup -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">3</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Database Configuration</h3>

                        <h4 class="font-semibold mb-2">PostgreSQL Setup</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Connect to PostgreSQL as superuser
sudo -u postgres psql

# Create database and user
CREATE DATABASE mathematical_trading;
CREATE USER trading_user WITH PASSWORD 'secure_password_123';
GRANT ALL PRIVILEGES ON DATABASE mathematical_trading TO trading_user;
\q

# Test connection
psql -h localhost -U trading_user -d mathematical_trading
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Redis Setup</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Test Redis connection
redis-cli ping
# Should return: PONG

# Configure Redis (optional)
sudo nano /etc/redis/redis.conf
# Uncomment and modify:
# maxmemory 1gb
# maxmemory-policy allkeys-lru

# Restart Redis (Linux)
sudo systemctl restart redis-server
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Create Database Schema</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create database initialization script
cat > init_database.sql << 'EOF'
-- Market data table
CREATE TABLE market_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(20,8),
    high_price DECIMAL(20,8),
    low_price DECIMAL(20,8),
    close_price DECIMAL(20,8),
    volume DECIMAL(20,8),
    exchange VARCHAR(50),
    timeframe VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- News data table
CREATE TABLE news_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20),
    title TEXT,
    content TEXT,
    url TEXT,
    source VARCHAR(100),
    published_at TIMESTAMP,
    sentiment_score DECIMAL(5,4),
    relevance_score DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Features table
CREATE TABLE features (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    timeframe VARCHAR(10),
    feature_name VARCHAR(100),
    feature_value DECIMAL(20,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Analysis results table
CREATE TABLE analysis_results (
    id SERIAL PRIMARY KEY,
    run_id UUID NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    selected_symbols JSON,
    mathematical_scores JSON,
    correlation_matrix JSON,
    risk_metrics JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX idx_news_data_symbol_published ON news_data(symbol, published_at);
CREATE INDEX idx_features_symbol_timestamp ON features(symbol, timestamp);
EOF

# Execute the script
psql -h localhost -U trading_user -d mathematical_trading -f init_database.sql
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>PostgreSQL connects without errors</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Redis responds to ping command</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Database tables created successfully</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Project Structure -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">4</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Create Project Structure</h3>

                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create directory structure
mkdir -p {agents,data/{raw,processed,features,models},database/{models,migrations},mathematical_engines,ml_models,api/{routes,middleware},frontend/{src,public},reports/{templates,static,output},config,tests/{test_agents,test_mathematical_engines,test_ml_models},scripts,docker,docs}

# Create __init__.py files for Python packages
touch agents/__init__.py
touch mathematical_engines/__init__.py
touch ml_models/__init__.py
touch api/__init__.py

# Create main configuration files
touch config/settings.py
touch config/database.yaml
touch .env
touch requirements.txt
touch README.md
touch main.py
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Generate requirements.txt</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Generate requirements file with current packages
pip freeze > requirements.txt

# Or create a clean requirements.txt manually
cat > requirements.txt << 'EOF'
# Core data science
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
matplotlib>=3.7.0
plotly>=5.14.0
seaborn>=0.12.0

# Mathematical libraries
sympy>=1.12.0
statsmodels>=0.14.0

# Machine learning
scikit-learn>=1.3.0
lightgbm>=4.0.0
xgboost>=1.7.0
prophet>=1.1.0

# Financial data
ccxt>=4.0.0
yfinance>=0.2.0
alpha-vantage>=2.3.0
pyportfolioopt>=1.5.0

# AI and agents
langchain>=0.0.300
openai>=0.28.0
anthropic>=0.7.0

# Web framework
fastapi>=0.100.0
uvicorn>=0.23.0

# Database
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
redis>=4.6.0
alembic>=1.11.0

# Task queue
celery>=5.3.0

# News and NLP
newsapi-python>=0.2.0
beautifulsoup4>=4.12.0
requests>=2.31.0
transformers>=4.30.0
spacy>=3.6.0
nltk>=3.8.0

# Template engine
jinja2>=3.1.0

# Development tools
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
pre-commit>=3.3.0
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All directories created successfully</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>requirements.txt contains all packages</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: API Keys Configuration -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">5</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Obtain & Configure API Keys</h3>

                        <div class="warning-box">
                            <strong>🔐 Security:</strong> Never commit API keys to version control. Use environment variables or encrypted config files.
                        </div>

                        <h4 class="font-semibold mb-2">Required API Keys</h4>

                        <div class="expandable-section">
                            <div class="expandable-header" onclick="toggleExpansion('binance-api')">
                                <span><strong>1. Binance API</strong> (Cryptocurrency Data)</span>
                                <i data-lucide="chevron-down" class="w-4 h-4"></i>
                            </div>
                            <div id="binance-api" class="expandable-content">
                                <ol class="list-decimal list-inside space-y-2 text-sm">
                                    <li>Go to <a href="https://www.binance.com/en/my/settings/api-management" class="text-blue-600 hover:underline">Binance API Management</a></li>
                                    <li>Click "Create API" and name it "Trading System"</li>
                                    <li>Select "Read Info" permissions only (no trading initially)</li>
                                    <li>Complete verification (SMS/Email)</li>
                                    <li>Copy API Key and Secret Key</li>
                                    <li>Set IP whitelist for security</li>
                                </ol>
                            </div>
                        </div>

                        <div class="expandable-section">
                            <div class="expandable-header" onclick="toggleExpansion('newsapi')">
                                <span><strong>2. NewsAPI</strong> (News Data)</span>
                                <i data-lucide="chevron-down" class="w-4 h-4"></i>
                            </div>
                            <div id="newsapi" class="expandable-content">
                                <ol class="list-decimal list-inside space-y-2 text-sm">
                                    <li>Visit <a href="https://newsapi.org/register" class="text-blue-600 hover:underline">NewsAPI Registration</a></li>
                                    <li>Sign up with email</li>
                                    <li>Copy your API key from dashboard</li>
                                    <li>Note: Free tier allows 1000 requests/day</li>
                                </ol>
                            </div>
                        </div>

                        <div class="expandable-section">
                            <div class="expandable-header" onclick="toggleExpansion('alphavantage')">
                                <span><strong>3. Alpha Vantage</strong> (Stock/Forex Data)</span>
                                <i data-lucide="chevron-down" class="w-4 h-4"></i>
                            </div>
                            <div id="alphavantage" class="expandable-content">
                                <ol class="list-decimal list-inside space-y-2 text-sm">
                                    <li>Go to <a href="https://www.alphavantage.co/support/#api-key" class="text-blue-600 hover:underline">Alpha Vantage API</a></li>
                                    <li>Enter your email to get free API key</li>
                                    <li>Copy the API key from confirmation email</li>
                                    <li>Note: Free tier allows 25 requests/day</li>
                                </ol>
                            </div>
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Create Environment Configuration</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create .env file for API keys
cat > .env << 'EOF'
# Database Configuration
DATABASE_URL=postgresql://trading_user:secure_password_123@localhost:5432/mathematical_trading
REDIS_URL=redis://localhost:6379

# Exchange API Keys
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here

# News API Keys
NEWS_API_KEY=your_newsapi_key_here

# Financial Data APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# AI API Keys (Optional)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# Security
SECRET_KEY=generate_a_random_secret_key_here
JWT_SECRET=another_random_secret_for_jwt

# Application Settings
DEBUG=True
LOG_LEVEL=INFO
MAX_REQUESTS_PER_MINUTE=60
EOF

# Secure the .env file
chmod 600 .env

# Add .env to .gitignore
echo ".env" >> .gitignore
echo "__pycache__/" >> .gitignore
echo "*.pyc" >> .gitignore
echo ".DS_Store" >> .gitignore
echo "venv/" >> .gitignore
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Create Settings Configuration</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create config/settings.py
cat > config/settings.py << 'EOF'
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base directory
BASE_DIR = Path(__file__).parent.parent

# Database settings
DATABASE_URL = os.getenv('DATABASE_URL')
REDIS_URL = os.getenv('REDIS_URL')

# API Keys
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')
NEWS_API_KEY = os.getenv('NEWS_API_KEY')
ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY')

# Application settings
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
SECRET_KEY = os.getenv('SECRET_KEY')

# Trading settings
MAX_POSITION_SIZE = 0.1  # 10% max per asset
MAX_PORTFOLIO_VOLATILITY = 0.2  # 20% annual volatility
MAX_DRAWDOWN_LIMIT = 0.15  # 15% max drawdown

# Data collection settings
CRYPTO_SYMBOLS_COUNT = 100
UPDATE_INTERVALS = ['1m', '5m', '1h', '1d', '7d']
NEWS_FETCH_INTERVAL = 300  # 5 minutes

# Validate required settings
required_settings = [
    'DATABASE_URL', 'REDIS_URL', 'BINANCE_API_KEY',
    'NEWS_API_KEY', 'SECRET_KEY'
]

for setting in required_settings:
    if not globals().get(setting):
        raise ValueError(f"Required setting {setting} is not configured")
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All API keys obtained and saved</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>.env file created and secured</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>settings.py loads without errors</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 6: Frontend Setup -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">6</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Frontend Dashboard Setup</h3>

                        <h4 class="font-semibold mb-2">Initialize React Frontend</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Navigate to frontend directory
cd frontend

# Create React app with Vite (faster than create-react-app)
npm create vite@latest . -- --template react

# Install additional dependencies
npm install axios recharts lucide-react @headlessui/react
npm install tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install socket.io-client

# Install development dependencies
npm install -D @types/node

# Initialize Tailwind CSS
npx tailwindcss init -p
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Configure Tailwind CSS</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Update tailwind.config.js
cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
EOF

# Update src/index.css
cat > src/index.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
EOF
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Update Package Scripts</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Update package.json scripts section
cat > package.json << 'EOF'
{
  "name": "mathematical-trading-frontend",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.5.0",
    "recharts": "^2.8.0",
    "lucide-react": "^0.279.0",
    "@headlessui/react": "^1.7.0",
    "tailwindcss": "^3.3.0",
    "socket.io-client": "^4.7.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react": "^7.32.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.0",
    "vite": "^4.4.0",
    "@tailwindcss/forms": "^0.5.0",
    "@tailwindcss/typography": "^0.5.0"
  }
}
EOF

# Install dependencies
npm install

# Return to project root
cd ..
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>React app created successfully</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All npm packages installed</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Frontend starts with <code>npm run dev</code></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Configuration Section -->
    <section id="configuration" class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Configuration & Testing</h2>

            <!-- Test Installation -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">7</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Test Your Installation</h3>

                        <h4 class="font-semibold mb-2">Create Test Scripts</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create test_installation.py
cat > test_installation.py << 'EOF'
#!/usr/bin/env python3
"""
Test script to verify all components are installed correctly
"""
import sys

def test_imports():
    """Test if all required packages can be imported"""
    packages = [
        'pandas', 'numpy', 'scipy', 'matplotlib', 'plotly',
        'sympy', 'statsmodels', 'sklearn', 'lightgbm',
        'ccxt', 'yfinance', 'requests', 'langchain',
        'fastapi', 'sqlalchemy', 'redis', 'jinja2'
    ]

    failed_imports = []

    for package in packages:
        try:
            __import__(package)
            print(f"✓ {package} imported successfully")
        except ImportError as e:
            print(f"✗ Failed to import {package}: {e}")
            failed_imports.append(package)

    return failed_imports

def test_database_connection():
    """Test database connectivity"""
    try:
        import psycopg2
        from config.settings import DATABASE_URL

        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✓ PostgreSQL connected: {version[0]}")
        conn.close()
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def test_redis_connection():
    """Test Redis connectivity"""
    try:
        import redis
        from config.settings import REDIS_URL

        r = redis.from_url(REDIS_URL)
        r.ping()
        print("✓ Redis connection successful")
        return True
    except Exception as e:
        print(f"✗ Redis connection failed: {e}")
        return False

def test_api_keys():
    """Test API key configuration"""
    try:
        from config.settings import (
            BINANCE_API_KEY, NEWS_API_KEY,
            ALPHA_VANTAGE_API_KEY
        )

        keys = {
            'Binance': BINANCE_API_KEY,
            'NewsAPI': NEWS_API_KEY,
            'Alpha Vantage': ALPHA_VANTAGE_API_KEY
        }

        for name, key in keys.items():
            if key and len(key) > 10:
                print(f"✓ {name} API key configured")
            else:
                print(f"⚠ {name} API key missing or invalid")

        return True
    except Exception as e:
        print(f"✗ API key test failed: {e}")
        return False

def main():
    print("🔍 Testing Mathematical Trading System Installation\n")

    # Test imports
    print("1. Testing package imports...")
    failed_imports = test_imports()

    # Test database
    print("\n2. Testing database connection...")
    db_ok = test_database_connection()

    # Test Redis
    print("\n3. Testing Redis connection...")
    redis_ok = test_redis_connection()

    # Test API keys
    print("\n4. Testing API key configuration...")
    api_ok = test_api_keys()

    # Summary
    print(f"\n📊 Installation Test Summary:")
    print(f"   Package imports: {'✓ PASS' if not failed_imports else '✗ FAIL'}")
    print(f"   Database: {'✓ PASS' if db_ok else '✗ FAIL'}")
    print(f"   Redis: {'✓ PASS' if redis_ok else '✗ FAIL'}")
    print(f"   API Keys: {'✓ PASS' if api_ok else '⚠ PARTIAL'}")

    if failed_imports:
        print(f"\n❌ Failed imports: {', '.join(failed_imports)}")
        print("Run: pip install " + ' '.join(failed_imports))

    if not db_ok:
        print("❌ Fix database connection issues")

    if not redis_ok:
        print("❌ Fix Redis connection issues")

    overall_success = not failed_imports and db_ok and redis_ok

    print(f"\n🎯 Overall Status: {'✅ READY' if overall_success else '❌ NEEDS ATTENTION'}")

    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
EOF

# Make it executable
chmod +x test_installation.py

# Run the test
python test_installation.py
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Quick API Test</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Test CCXT crypto data fetching
python -c "
import ccxt
exchange = ccxt.binance()
ticker = exchange.fetch_ticker('BTC/USDT')
print(f'BTC/USDT Price: ${ticker[\"last\"]:,.2f}')
print('✓ CCXT working correctly')
"

# Test yfinance stock data
python -c "
import yfinance as yf
ticker = yf.Ticker('SPY')
info = ticker.info
print(f'SPY Price: ${info[\"regularMarketPrice\"]:,.2f}')
print('✓ yfinance working correctly')
"

# Test mathematical libraries
python -c "
import numpy as np
import pandas as pd
from scipy import stats
import sympy as sp

# Test calculations
data = np.random.normal(0, 1, 1000)
correlation = np.corrcoef(data, data)[0,1]
print(f'Correlation test: {correlation:.3f} (should be ~1.0)')

# Test symbolic math
x = sp.Symbol('x')
derivative = sp.diff(x**2, x)
print(f'Derivative of x²: {derivative}')
print('✓ Mathematical libraries working correctly')
"
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All package imports successful</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Database connection working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Redis connection working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>API data fetching successful</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Development Tools Setup -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">8</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Development Tools & IDE Setup</h3>

                        <h4 class="font-semibold mb-2">Recommended IDEs & Extensions</h4>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="tool-card">
                                <h5 class="font-semibold mb-2">Visual Studio Code</h5>
                                <p class="text-sm mb-3">Recommended IDE with excellent Python support</p>
                                <div class="text-sm space-y-1">
                                    <div><strong>Essential Extensions:</strong></div>
                                    <div>• Python (Microsoft)</div>
                                    <div>• Pylance (Microsoft)</div>
                                    <div>• Python Docstring Generator</div>
                                    <div>• GitLens</div>
                                    <div>• Thunder Client (API testing)</div>
                                    <div>• ES7+ React/Redux/React-Native snippets</div>
                                </div>
                            </div>

                            <div class="tool-card">
                                <h5 class="font-semibold mb-2">PyCharm Professional</h5>
                                <p class="text-sm mb-3">Full-featured Python IDE (paid/free for students)</p>
                                <div class="text-sm space-y-1">
                                    <div><strong>Built-in Features:</strong></div>
                                    <div>• Advanced debugging</div>
                                    <div>• Database tools</div>
                                    <div>• Git integration</div>
                                    <div>• Remote development</div>
                                    <div>• Jupyter notebook support</div>
                                </div>
                            </div>
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Install Development Tools</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install code formatting and linting tools
pip install black isort flake8 mypy pre-commit

# Install Jupyter for data exploration
pip install jupyter jupyter-lab ipywidgets

# Install testing tools
pip install pytest pytest-cov pytest-mock

# Install documentation tools
pip install sphinx sphinx-rtd-theme

# Set up pre-commit hooks
cat > .pre-commit-config.yaml << 'EOF'
repos:
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests]
EOF

# Initialize pre-commit
pre-commit install
                        </div>

                        <h4 class="font-semibold mb-2 mt-4">Create Development Scripts</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create scripts/dev_setup.sh
mkdir -p scripts
cat > scripts/dev_setup.sh << 'EOF'
#!/bin/bash
# Development environment setup script

echo "🔧 Setting up development environment..."

# Activate virtual environment
source venv/bin/activate

# Install/upgrade all dependencies
pip install --upgrade pip
pip install -r requirements.txt

# Install development dependencies
pip install pytest pytest-cov black isort flake8 mypy pre-commit jupyter

# Set up pre-commit hooks
pre-commit install

# Create necessary directories
mkdir -p {logs,data/raw,data/processed,reports/output}

# Set up database (if not already done)
python test_installation.py

echo "✅ Development environment ready!"
echo "📝 Next steps:"
echo "   1. Configure your IDE with Python interpreter: $(which python)"
echo "   2. Test with: python test_installation.py"
echo "   3. Start coding!"
EOF

chmod +x scripts/dev_setup.sh

# Create scripts/start_services.sh
cat > scripts/start_services.sh << 'EOF'
#!/bin/bash
# Start all required services

echo "🚀 Starting all services..."

# Start PostgreSQL (varies by OS)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    sudo systemctl start postgresql redis-server
elif [[ "$OSTYPE" == "darwin"* ]]; then
    brew services start postgresql redis
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo "Start PostgreSQL and Redis manually on Windows"
fi

# Wait for services to start
sleep 2

# Test connections
python -c "
import psycopg2
import redis
from config.settings import DATABASE_URL, REDIS_URL

try:
    # Test PostgreSQL
    conn = psycopg2.connect(DATABASE_URL)
    conn.close()
    print('✅ PostgreSQL running')
except:
    print('❌ PostgreSQL not running')

try:
    # Test Redis
    r = redis.from_url(REDIS_URL)
    r.ping()
    print('✅ Redis running')
except:
    print('❌ Redis not running')
"

echo "🎯 Services status checked!"
EOF

chmod +x scripts/start_services.sh
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>IDE installed and configured</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Development tools installed</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Pre-commit hooks working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Scripts executable and functional</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Verification -->
    <section id="verification" class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Final Verification & Next Steps</h2>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🎯 Complete Installation Checklist</h3>

                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="font-semibold mb-4 text-green-600">✅ Core Components</h4>
                        <div class="space-y-2">
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Python 3.11+ installed and working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Virtual environment created and activated</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All Python packages installed</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>PostgreSQL database setup and connected</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Redis cache server running</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Node.js and npm installed</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>React frontend initialized</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold mb-4 text-blue-600">🔧 Configuration</h4>
                        <div class="space-y-2">
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>API keys obtained and configured</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Environment variables set up</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Project structure created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Database schema initialized</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Development tools configured</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All tests passing</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>IDE set up with extensions</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">🎉 Congratulations!</h4>
                    <p class="text-green-700">If all items are checked, your Mathematical Trading System development environment is ready!</p>
                </div>

                <div class="mt-6">
                    <h4 class="font-semibold mb-4">🚀 Next Steps</h4>
                    <ol class="list-decimal list-inside space-y-2">
                        <li>Start with the <strong>Data Collection Agent</strong> implementation</li>
                        <li>Set up continuous integration with GitHub Actions</li>
                        <li>Begin implementing the mathematical analysis engines</li>
                        <li>Create your first agent and test data fetching</li>
                        <li>Follow the detailed development manual for step-by-step coding</li>
                    </ol>
                </div>

                <div class="mt-6 flex space-x-4">
                    <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                        <i data-lucide="play" class="w-4 h-4 inline mr-2"></i>
                        Start Development
                    </button>
                    <button class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition">
                        <i data-lucide="book-open" class="w-4 h-4 inline mr-2"></i>
                        View Development Manual
                    </button>
                    <button class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition">
                        <i data-lucide="github" class="w-4 h-4 inline mr-2"></i>
                        Initialize Git Repository
                    </button>
                </div>
            </div>

            <!-- Troubleshooting Section -->
            <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-bold mb-4">🔧 Common Issues & Solutions</h3>

                <div class="expandable-section">
                    <div class="expandable-header" onclick="toggleExpansion('python-issues')">
                        <span><strong>Python/Pip Issues</strong></span>
                        <i data-lucide="chevron-down" class="w-4 h-4"></i>
                    </div>
                    <div id="python-issues" class="expandable-content">
                        <div class="space-y-3 text-sm">
                            <div><strong>Issue:</strong> "pip not found" or "python not found"</div>
                            <div><strong>Solution:</strong> Use <code>python3</code> and <code>pip3</code> explicitly, or add Python to PATH</div>

                            <div><strong>Issue:</strong> Permission denied when installing packages</div>
                            <div><strong>Solution:</strong> Make sure virtual environment is activated, don't use sudo</div>

                            <div><strong>Issue:</strong> Package installation fails with compiler errors</div>
                            <div><strong>Solution:</strong> Install build tools: <code>sudo apt install build-essential python3-dev</code> (Linux)</div>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <div class="expandable-header" onclick="toggleExpansion('database-issues')">
                        <span><strong>Database Connection Issues</strong></span>
                        <i data-lucide="chevron-down" class="w-4 h-4"></i>
                    </div>
                    <div id="database-issues" class="expandable-content">
                        <div class="space-y-3 text-sm">
                            <div><strong>Issue:</strong> "Connection refused" to PostgreSQL</div>
                            <div><strong>Solution:</strong> Start PostgreSQL service: <code>sudo systemctl start postgresql</code></div>

                            <div><strong>Issue:</strong> Authentication failed for user</div>
                            <div><strong>Solution:</strong> Reset password: <code>sudo -u postgres psql -c "ALTER USER trading_user PASSWORD 'new_password';"</code></div>

                            <div><strong>Issue:</strong> Database does not exist</div>
                            <div><strong>Solution:</strong> Create database: <code>sudo -u postgres createdb mathematical_trading</code></div>
                        </div>
                    </div>
                </div>

                <div class="expandable-section">
                    <div class="expandable-header" onclick="toggleExpansion('api-issues')">
                        <span><strong>API Key Issues</strong></span>
                        <i data-lucide="chevron-down" class="w-4 h-4"></i>
                    </div>
                    <div id="api-issues" class="expandable-content">
                        <div class="space-y-3 text-sm">
                            <div><strong>Issue:</strong> "Invalid API key" errors</div>
                            <div><strong>Solution:</strong> Double-check API key in .env file, ensure no extra spaces</div>

                            <div><strong>Issue:</strong> Rate limit exceeded</div>
                            <div><strong>Solution:</strong> Implement rate limiting in code, use premium API plans if needed</div>

                            <div><strong>Issue:</strong> CORS errors from frontend</div>
                            <div><strong>Solution:</strong> Set up proper CORS middleware in FastAPI backend</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="gradient-bg text-white py-8">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h3 class="text-xl font-bold mb-4">🎯 Installation Complete!</h3>
            <p class="mb-6">Your Mathematical Trading System development environment is ready. Time to start building!</p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    Begin Development
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    View Documentation
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    Join Community
                </button>
            </div>
            <p class="text-sm mt-6 opacity-75">© 2025 Mathematical Trading System Installation Guide</p>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Platform switching
        function switchPlatform(platform) {
            // Hide all platform content
            document.querySelectorAll('.platform-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.platform-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected platform content
            document.getElementById(platform + '-content').classList.add('active');

            // Add active class to selected tab
            event.target.classList.add('active');
        }

        // Copy to clipboard function
        function copyToClipboard(button) {
            const codeBlock = button.parentElement;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(() => {
                button.textContent = 'Copied!';
                button.style.background = 'rgba(16, 185, 129, 0.4)';

                setTimeout(() => {
                    button.textContent = 'Copy';
                    button.style.background = 'rgba(16, 185, 129, 0.2)';
                }, 2000);
            });
        }

        // Toggle expandable sections
        function toggleExpansion(sectionId) {
            const content = document.getElementById(sectionId);
            const header = content.previousElementSibling;
            const icon = header.querySelector('[data-lucide]');

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Auto-check checkboxes when clicked
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // You can add logic here to save progress or update completion status
                console.log('Checkbox toggled:', this.checked);
            });
        });

        // Track completion progress
        function updateProgress() {
            const totalCheckboxes = document.querySelectorAll('input[type="checkbox"]').length;
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const progress = (checkedBoxes / totalCheckboxes) * 100;

            console.log(`Installation progress: ${progress.toFixed(1)}%`);

            // You can add a progress bar here if desired
        }

        // Add event listeners to track progress
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', updateProgress);
        });

        // Initialize
        updateProgress();
    </script>
</body>
</html>