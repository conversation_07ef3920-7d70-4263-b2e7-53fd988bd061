<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Strategy Agent Guide - Mathematical Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .strategy-bg {
            background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
        }
        .step-card {
            border-left: 4px solid #16a085;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #16a085;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(22, 160, 133, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(22, 160, 133, 0.2);
            border: 1px solid #16a085;
            color: #16a085;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            font-family: 'Ubuntu', sans-serif;
        }
        .copy-button:hover {
            background: rgba(22, 160, 133, 0.3);
        }
        .step-number {
            width: 50px;
            height: 50px;
            background: #16a085;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 4px 10px rgba(22, 160, 133, 0.3);
        }
        .warning-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .success-box {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .info-box {
            background: #dbeafe;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .strategy-box {
            background: #f0fdfa;
            border: 2px solid #16a085;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .strategy-card {
            background: linear-gradient(135deg, #f0fdfa 0%, #e6fffa 100%);
            border: 2px solid #16a085;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
        .terminal-prompt {
            color: #16a085;
            font-weight: bold;
        }
        .verification-checklist {
            background: #f0fdfa;
            border: 2px solid #16a085;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        .checklist-item:hover {
            background: #e6fffa;
        }
        .formula-display {
            background: #1a1a1a;
            color: #16a085;
            font-family: 'Ubuntu Mono', monospace;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
            border: 1px solid #16a085;
        }
        .strategy-badge {
            display: inline-block;
            background: #16a085;
            color: white;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin: 3px;
        }
        .risk-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            margin: 2px;
        }
        .risk-low { background: #dcfce7; color: #15803d; }
        .risk-medium { background: #fef3c7; color: #a16207; }
        .risk-high { background: #fecaca; color: #dc2626; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="strategy-bg text-white p-4 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i data-lucide="trending-up" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-xl font-bold">Trading Strategy Agent Guide</h1>
                    <p class="text-sm opacity-90">Stage 4: Intelligent Trading Strategy Development</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <i data-lucide="check-circle" class="w-5 h-5"></i>
                    <span class="text-sm">Mathematical Engine Complete</span>
                </div>
                <a href="#quick-start" class="bg-white/20 px-4 py-2 rounded hover:bg-white/30 transition">Start Building</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="strategy-bg text-white py-20">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h1 class="text-5xl font-bold mb-6">Trading Strategy Agent Development</h1>
            <p class="text-xl mb-8">Intelligent Trading Strategy Execution & Risk Management</p>
            <div class="flex justify-center space-x-8">
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="brain" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Strategy Engine</h3>
                    <p class="text-sm">Advanced algorithmic strategies</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="shield" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Risk Management</h3>
                    <p class="text-sm">Portfolio protection systems</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="target" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Position Sizing</h3>
                    <p class="text-sm">Optimal capital allocation</p>
                </div>
                <div class="bg-white/20 rounded-lg p-6">
                    <i data-lucide="activity" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Performance Tracking</h3>
                    <p class="text-sm">Real-time monitoring</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Prerequisites Check -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Pre-Development Verification</h2>

            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h3 class="text-xl font-bold mb-6">🔍 Verify Mathematical Engine Status</h3>

                <div class="command-block">
                    <div class="env-indicator">AstroA/</div>
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Navigate to project and activate environment
cd ~/axmadcodes/AstroA && source venv/bin/activate

<span class="terminal-prompt">$</span> # Verify mathematical engine is working
python test_mathematical_engine.py

<span class="terminal-prompt">$</span> # Check analysis results in database
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "SELECT symbol, COUNT(*) as analysis_count FROM analysis_results GROUP BY symbol LIMIT 10;"
                </div>

                <div class="warning-box">
                    <strong>⚠️ Requirements:</strong> Mathematical Engine must be operational with successful analysis results before proceeding with Trading Strategy development.
                </div>
            </div>
        </div>
    </section>

    <!-- Trading Strategy Components Overview -->
    <section id="quick-start" class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Trading Strategy Components</h2>

            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <div class="strategy-card">
                    <span class="strategy-badge">Core Strategy</span>
                    <h3 class="text-xl font-bold mb-4 text-teal-700">📈 Strategy Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Mean reversion strategies</li>
                        <li>• Momentum trading algorithms</li>
                        <li>• Statistical arbitrage</li>
                        <li>• Multi-timeframe analysis</li>
                        <li>• Signal aggregation</li>
                        <li>• Strategy optimization</li>
                    </ul>
                </div>
                <div class="strategy-card">
                    <span class="strategy-badge">Risk Control</span>
                    <h3 class="text-xl font-bold mb-4 text-teal-700">🛡️ Risk Management Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Position sizing algorithms</li>
                        <li>• Stop-loss automation</li>
                        <li>• Portfolio risk monitoring</li>
                        <li>• Correlation-based limits</li>
                        <li>• Maximum drawdown controls</li>
                        <li>• Dynamic risk adjustment</li>
                    </ul>
                </div>
                <div class="strategy-card">
                    <span class="strategy-badge">Portfolio</span>
                    <h3 class="text-xl font-bold mb-4 text-teal-700">⚖️ Portfolio Management</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Asset allocation models</li>
                        <li>• Rebalancing strategies</li>
                        <li>• Diversification optimization</li>
                        <li>• Capital efficiency</li>
                        <li>• Performance attribution</li>
                        <li>• Risk-adjusted returns</li>
                    </ul>
                </div>
                <div class="strategy-card">
                    <span class="strategy-badge">Execution</span>
                    <h3 class="text-xl font-bold mb-4 text-teal-700">⚡ Execution Engine</h3>
                    <ul class="text-sm space-y-2">
                        <li>• Order management system</li>
                        <li>• Execution algorithms</li>
                        <li>• Slippage monitoring</li>
                        <li>• Market impact analysis</li>
                        <li>• Fill optimization</li>
                        <li>• Performance tracking</li>
                    </ul>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🏗️ Trading Strategy Architecture</h3>
                <div class="formula-display">
Mathematical Engine → Signal Generation → Strategy Logic → Risk Assessment
                    ↓                    ↓                ↓               ↓
           Market Data Analysis → Position Sizing → Portfolio Optimizer → Trade Execution
                    ↓                    ↓                ↓               ↓
              Performance Monitor ← Risk Monitor ← Portfolio Monitor ← Execution Monitor
                </div>
            </div>
        </div>
    </section>

    <!-- Implementation Steps -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Trading Strategy Implementation</h2>

            <!-- Step 1: Strategy Framework -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">1</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Strategy Framework & Base Classes</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create strategy types and enums
cat > shared/types/strategy_types.py << 'EOF'
"""
Trading strategy types and enums
"""
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import uuid

class StrategyType(Enum):
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    STATISTICAL_ARBITRAGE = "statistical_arbitrage"
    PAIRS_TRADING = "pairs_trading"
    TREND_FOLLOWING = "trend_following"
    SCALPING = "scalping"
    SWING_TRADING = "swing_trading"

class SignalType(Enum):
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STRONG_BUY = "strong_buy"
    STRONG_SELL = "strong_sell"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    PARTIAL = "partial"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

@dataclass
class TradingSignal:
    symbol: str
    signal_type: SignalType
    confidence: float
    timestamp: datetime
    price: float
    volume: Optional[float] = None
    strategy_id: str = None
    metadata: Dict[str, Any] = None

@dataclass
class Position:
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    entry_time: datetime
    position_type: str  # 'long' or 'short'
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

@dataclass
class Order:
    id: str
    symbol: str
    order_type: OrderType
    quantity: float
    price: Optional[float]
    status: OrderStatus
    created_at: datetime
    filled_at: Optional[datetime] = None
    filled_quantity: float = 0.0
    filled_price: Optional[float] = None

@dataclass
class PortfolioSummary:
    total_value: float
    cash: float
    positions_value: float
    total_pnl: float
    daily_pnl: float
    positions_count: int
    risk_score: float
    max_drawdown: float
    sharpe_ratio: Optional[float] = None
EOF

<span class="terminal-prompt">(venv) $</span> # Create base strategy class
cat > shared/utils/base_strategy.py << 'EOF'
"""
Base Strategy class for all trading strategies
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd

from shared.types.strategy_types import (
    StrategyType, TradingSignal, Position, Order
)

class BaseStrategy(ABC):
    """Base class for all trading strategies"""

    def __init__(
        self,
        strategy_id: str,
        strategy_type: StrategyType,
        symbols: List[str],
        parameters: Dict[str, Any] = None
    ):
        self.strategy_id = strategy_id
        self.strategy_type = strategy_type
        self.symbols = symbols
        self.parameters = parameters or {}

        # Strategy state
        self.is_active = False
        self.created_at = datetime.now()
        self.last_update = datetime.now()

        # Performance tracking
        self.total_signals = 0
        self.successful_signals = 0
        self.total_pnl = 0.0

        # Setup logging
        self.logger = logging.getLogger(f"strategy.{self.strategy_id}")

        self.logger.info(f"Strategy {self.strategy_id} ({self.strategy_type.value}) initialized")

    @abstractmethod
    async def generate_signals(self, market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate trading signals based on market data and analysis"""
        pass

    @abstractmethod
    async def calculate_position_size(self, signal: TradingSignal,
                                    portfolio_value: float,
                                    risk_budget: float) -> float:
        """Calculate optimal position size for a signal"""
        pass

    @abstractmethod
    async def should_exit_position(self, position: Position,
                                 current_data: Dict[str, Any]) -> bool:
        """Determine if a position should be closed"""
        pass

    @abstractmethod
    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        pass

    async def update_parameters(self, new_parameters: Dict[str, Any]):
        """Update strategy parameters"""
        self.parameters.update(new_parameters)
        self.last_update = datetime.now()
        self.logger.info(f"Strategy parameters updated: {new_parameters}")

    async def activate(self):
        """Activate the strategy"""
        self.is_active = True
        self.logger.info(f"Strategy {self.strategy_id} activated")

    async def deactivate(self):
        """Deactivate the strategy"""
        self.is_active = False
        self.logger.info(f"Strategy {self.strategy_id} deactivated")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        success_rate = (self.successful_signals / max(self.total_signals, 1)) * 100

        return {
            "strategy_id": self.strategy_id,
            "strategy_type": self.strategy_type.value,
            "is_active": self.is_active,
            "total_signals": self.total_signals,
            "successful_signals": self.successful_signals,
            "success_rate": success_rate,
            "total_pnl": self.total_pnl,
            "created_at": self.created_at.isoformat(),
            "last_update": self.last_update.isoformat()
        }

    def record_signal_result(self, signal: TradingSignal, pnl: float):
        """Record the result of a trading signal"""
        self.total_signals += 1
        if pnl > 0:
            self.successful_signals += 1
        self.total_pnl += pnl

        self.logger.info(f"Signal result recorded: PnL={pnl:.2f}, Success Rate={self.successful_signals/self.total_signals*100:.1f}%")
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Strategy types and enums defined</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Base strategy class implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>No syntax errors in Python files</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Risk Management Engine -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">2</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Implement Risk Management Engine</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create risk management engine
cat > agents/trading_strategy/risk_management/risk_manager.py << 'EOF'
"""
Risk Management Engine for Trading Strategies
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

from shared.types.strategy_types import Position, TradingSignal, RiskLevel
from shared.mathematical.constants import TRADING_DAYS_PER_YEAR, VAR_CONFIDENCE_LEVELS

class RiskManager:
    """Advanced risk management for trading strategies"""

    def __init__(self,
                 max_portfolio_risk: float = 0.02,
                 max_position_risk: float = 0.01,
                 max_correlation_limit: float = 0.7,
                 max_drawdown_limit: float = 0.15):

        self.max_portfolio_risk = max_portfolio_risk  # 2% daily portfolio risk
        self.max_position_risk = max_position_risk    # 1% per position
        self.max_correlation_limit = max_correlation_limit  # 70% max correlation
        self.max_drawdown_limit = max_drawdown_limit  # 15% max drawdown

        self.logger = logging.getLogger("risk_manager")

        # Risk tracking
        self.current_positions: Dict[str, Position] = {}
        self.historical_returns: List[float] = []
        self.max_historical_drawdown = 0.0

    def calculate_position_size(self,
                              signal: TradingSignal,
                              portfolio_value: float,
                              volatility: float,
                              price: float) -> float:
        """Calculate optimal position size using Kelly Criterion and risk limits"""

        # Base position size using risk-based approach
        risk_amount = portfolio_value * self.max_position_risk

        # Calculate position size based on stop loss distance
        if signal.metadata and 'stop_loss_price' in signal.metadata:
            stop_loss_price = signal.metadata['stop_loss_price']
            risk_per_share = abs(price - stop_loss_price)

            if risk_per_share > 0:
                base_position_size = risk_amount / risk_per_share
            else:
                base_position_size = 0
        else:
            # Use volatility-based sizing if no stop loss
            if volatility > 0:
                base_position_size = risk_amount / (price * volatility * 2)
            else:
                base_position_size = 0

        # Apply Kelly Criterion adjustment if we have confidence and win rate data
        if signal.metadata and 'win_rate' in signal.metadata:
            win_rate = signal.metadata['win_rate']
            avg_win = signal.metadata.get('avg_win', 0.02)
            avg_loss = signal.metadata.get('avg_loss', 0.01)

            if avg_loss > 0:
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
                base_position_size *= kelly_fraction

        # Apply portfolio concentration limits
        max_concentration = portfolio_value * 0.1  # Max 10% in any single position
        max_position_value = max_concentration / price

        final_position_size = min(base_position_size, max_position_value)

        self.logger.info(f"Position size calculated for {signal.symbol}: {final_position_size:.2f} shares")
        return max(0, final_position_size)

    def calculate_portfolio_risk(self, positions: Dict[str, Position]) -> Dict[str, float]:
        """Calculate current portfolio risk metrics"""
        if not positions:
            return {"total_risk": 0.0, "var_95": 0.0, "expected_shortfall": 0.0}

        # Calculate position values and weights
        total_value = sum(pos.quantity * pos.current_price for pos in positions.values())

        if total_value <= 0:
            return {"total_risk": 0.0, "var_95": 0.0, "expected_shortfall": 0.0}

        # Calculate weighted portfolio risk
        portfolio_weights = []
        individual_risks = []

        for symbol, position in positions.items():
            weight = (position.quantity * position.current_price) / total_value
            portfolio_weights.append(weight)

            # Estimate individual position risk (simplified)
            position_risk = abs(position.unrealized_pnl / total_value) if total_value > 0 else 0
            individual_risks.append(position_risk)

        # Portfolio risk metrics
        weighted_risk = sum(w * r for w, r in zip(portfolio_weights, individual_risks))

        # VaR calculation (simplified - would need historical returns in practice)
        if self.historical_returns:
            returns_array = np.array(self.historical_returns)
            var_95 = np.percentile(returns_array, 5) * total_value
            var_99 = np.percentile(returns_array, 1) * total_value

            # Expected Shortfall (Conditional VaR)
            tail_returns = returns_array[returns_array <= np.percentile(returns_array, 5)]
            expected_shortfall = np.mean(tail_returns) * total_value if len(tail_returns) > 0 else 0
        else:
            var_95 = weighted_risk * total_value * 2.33  # Approximation
            var_99 = weighted_risk * total_value * 3.09
            expected_shortfall = var_95 * 1.3

        return {
            "total_risk": weighted_risk,
            "var_95": abs(var_95),
            "var_99": abs(var_99),
            "expected_shortfall": abs(expected_shortfall),
            "positions_count": len(positions),
            "concentration_risk": max(portfolio_weights) if portfolio_weights else 0
        }

    def check_risk_limits(self,
                         signal: TradingSignal,
                         proposed_position_size: float,
                         current_positions: Dict[str, Position],
                         portfolio_value: float) -> Tuple[bool, str]:
        """Check if a proposed trade violates risk limits"""

        # Check portfolio concentration
        position_value = proposed_position_size * signal.price
        concentration = position_value / portfolio_value if portfolio_value > 0 else 0

        if concentration > 0.15:  # Max 15% concentration
            return False, f"Position concentration ({concentration:.1%}) exceeds limit (15%)"

        # Check correlation limits (simplified check)
        if len(current_positions) > 0:
            symbols_in_portfolio = list(current_positions.keys())
            if signal.symbol in symbols_in_portfolio:
                return False, "Position already exists for this symbol"

        # Check total portfolio risk
        risk_metrics = self.calculate_portfolio_risk(current_positions)
        if risk_metrics["total_risk"] > self.max_portfolio_risk:
            return False, f"Portfolio risk ({risk_metrics['total_risk']:.1%}) exceeds limit ({self.max_portfolio_risk:.1%})"

        # Check maximum drawdown
        if self.max_historical_drawdown > self.max_drawdown_limit:
            return False, f"Maximum drawdown ({self.max_historical_drawdown:.1%}) exceeds limit ({self.max_drawdown_limit:.1%})"

        return True, "Risk checks passed"

    def calculate_stop_loss(self,
                          entry_price: float,
                          signal: TradingSignal,
                          volatility: float,
                          atr: float = None) -> float:
        """Calculate dynamic stop loss based on volatility and market conditions"""

        # Base stop loss using ATR (Average True Range)
        if atr and atr > 0:
            stop_distance = atr * 2.0  # 2x ATR stop
        else:
            # Fallback to volatility-based stop
            stop_distance = entry_price * volatility * 2.0

        # Adjust based on signal confidence
        confidence_multiplier = 1.0 + (signal.confidence - 0.5) * 0.5
        stop_distance *= confidence_multiplier

        # Calculate stop loss price
        if signal.signal_type.value in ['buy', 'strong_buy']:
            stop_loss_price = entry_price - stop_distance
        else:
            stop_loss_price = entry_price + stop_distance

        # Ensure minimum stop distance (0.5%)
        min_stop_distance = entry_price * 0.005
        if abs(entry_price - stop_loss_price) < min_stop_distance:
            if signal.signal_type.value in ['buy', 'strong_buy']:
                stop_loss_price = entry_price - min_stop_distance
            else:
                stop_loss_price = entry_price + min_stop_distance

        return stop_loss_price

    def calculate_take_profit(self,
                            entry_price: float,
                            stop_loss_price: float,
                            signal: TradingSignal,
                            risk_reward_ratio: float = 2.0) -> float:
        """Calculate take profit level based on risk-reward ratio"""

        risk_amount = abs(entry_price - stop_loss_price)
        reward_amount = risk_amount * risk_reward_ratio

        # Adjust based on signal strength
        if signal.signal_type.value in ['strong_buy', 'strong_sell']:
            reward_amount *= 1.5  # Increase target for strong signals

        # Calculate take profit price
        if signal.signal_type.value in ['buy', 'strong_buy']:
            take_profit_price = entry_price + reward_amount
        else:
            take_profit_price = entry_price - reward_amount

        return take_profit_price

    def update_historical_performance(self, daily_return: float):
        """Update historical performance tracking"""
        self.historical_returns.append(daily_return)

        # Keep only last 252 days (1 year)
        if len(self.historical_returns) > 252:
            self.historical_returns = self.historical_returns[-252:]

        # Update maximum drawdown
        if len(self.historical_returns) >= 2:
            cumulative_returns = np.cumprod(1 + np.array(self.historical_returns))
            peak = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - peak) / peak
            self.max_historical_drawdown = abs(np.min(drawdown))

    def get_risk_assessment(self,
                          signal: TradingSignal,
                          current_market_conditions: Dict[str, Any]) -> RiskLevel:
        """Assess overall risk level for a trading signal"""

        risk_score = 0

        # Signal confidence factor
        if signal.confidence < 0.3:
            risk_score += 2
        elif signal.confidence < 0.6:
            risk_score += 1

        # Market volatility factor
        if current_market_conditions.get('volatility', 0) > 0.3:
            risk_score += 2
        elif current_market_conditions.get('volatility', 0) > 0.2:
            risk_score += 1

        # Market trend factor
        if current_market_conditions.get('trend_strength', 0) < 0.3:
            risk_score += 1

        # Current drawdown factor
        if self.max_historical_drawdown > 0.1:
            risk_score += 2
        elif self.max_historical_drawdown > 0.05:
            risk_score += 1

        # Convert score to risk level
        if risk_score >= 5:
            return RiskLevel.EXTREME
        elif risk_score >= 3:
            return RiskLevel.HIGH
        elif risk_score >= 2:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def should_reduce_position_size(self, risk_level: RiskLevel) -> float:
        """Get position size multiplier based on risk level"""
        multipliers = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.7,
            RiskLevel.HIGH: 0.4,
            RiskLevel.EXTREME: 0.1
        }
        return multipliers.get(risk_level, 0.5)
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Risk manager implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Position sizing algorithms complete</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Risk assessment functions working</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Strategy Implementations -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">3</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Concrete Trading Strategies</h3>

                        <h4 class="font-semibold mb-3">Mean Reversion Strategy</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create mean reversion strategy
cat > agents/trading_strategy/strategies/mean_reversion_strategy.py << 'EOF'
"""
Mean Reversion Trading Strategy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime

from shared.utils.base_strategy import BaseStrategy
from shared.types.strategy_types import (
    StrategyType, TradingSignal, SignalType, Position
)

class MeanReversionStrategy(BaseStrategy):
    """Mean reversion strategy using statistical analysis"""

    def __init__(self, strategy_id: str, symbols: List[str]):
        default_params = {
            'lookback_period': 20,
            'z_score_entry': 2.0,
            'z_score_exit': 0.5,
            'min_confidence': 0.6,
            'max_holding_period': 10  # days
        }

        super().__init__(
            strategy_id=strategy_id,
            strategy_type=StrategyType.MEAN_REVERSION,
            symbols=symbols,
            parameters=default_params
        )

    async def generate_signals(self,
                             market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate mean reversion signals"""
        signals = []

        for symbol in self.symbols:
            try:
                # Filter data for this symbol
                symbol_data = market_data[market_data['symbol'] == symbol].copy()
                if len(symbol_data) < self.parameters['lookback_period']:
                    continue

                # Sort by timestamp
                symbol_data = symbol_data.sort_values('timestamp')

                # Calculate rolling statistics
                close_prices = symbol_data['close_price']
                rolling_mean = close_prices.rolling(window=self.parameters['lookback_period']).mean()
                rolling_std = close_prices.rolling(window=self.parameters['lookback_period']).std()

                # Calculate Z-score
                current_price = close_prices.iloc[-1]
                current_mean = rolling_mean.iloc[-1]
                current_std = rolling_std.iloc[-1]

                if current_std > 0:
                    z_score = (current_price - current_mean) / current_std
                else:
                    continue

                # Generate signals based on Z-score
                signal = None
                confidence = 0.0

                if z_score <= -self.parameters['z_score_entry']:
                    # Price is below mean - BUY signal
                    signal = SignalType.BUY
                    confidence = min(abs(z_score) / 3.0, 1.0)  # Higher Z-score = higher confidence

                elif z_score >= self.parameters['z_score_entry']:
                    # Price is above mean - SELL signal
                    signal = SignalType.SELL
                    confidence = min(abs(z_score) / 3.0, 1.0)

                # Only generate signal if confidence is sufficient
                if signal and confidence >= self.parameters['min_confidence']:

                    # Get additional data from analysis results
                    volatility = 0.02  # Default
                    if symbol in analysis_results and 'return_stats' in analysis_results[symbol]:
                        volatility = analysis_results[symbol]['return_stats'].get('std_return', 0.02)

                    # Calculate stop loss and take profit
                    atr = self._calculate_atr(symbol_data)
                    stop_loss_price = self._calculate_stop_loss(current_price, signal, atr)
                    take_profit_price = self._calculate_take_profit(current_price, stop_loss_price, signal)

                    trading_signal = TradingSignal(
                        symbol=symbol,
                        signal_type=signal,
                        confidence=confidence,
                        timestamp=datetime.now(),
                        price=current_price,
                        strategy_id=self.strategy_id,
                        metadata={
                            'z_score': z_score,
                            'rolling_mean': current_mean,
                            'rolling_std': current_std,
                            'volatility': volatility,
                            'stop_loss_price': stop_loss_price,
                            'take_profit_price': take_profit_price,
                            'atr': atr,
                            'lookback_period': self.parameters['lookback_period']
                        }
                    )

                    signals.append(trading_signal)
                    self.logger.info(f"Generated {signal.value} signal for {symbol} (Z-score: {z_score:.2f}, Confidence: {confidence:.2f})")

            except Exception as e:
                self.logger.error(f"Error generating signal for {symbol}: {e}")
                continue

        return signals

    async def calculate_position_size(self,
                                    signal: TradingSignal,
                                    portfolio_value: float,
                                    risk_budget: float) -> float:
        """Calculate position size based on volatility and confidence"""

        # Base position size from risk budget
        base_risk_amount = portfolio_value * risk_budget

        # Adjust for signal confidence
        confidence_adjusted_risk = base_risk_amount * signal.confidence

        # Calculate position size based on stop loss distance
        if signal.metadata and 'stop_loss_price' in signal.metadata:
            stop_loss_price = signal.metadata['stop_loss_price']
            risk_per_share = abs(signal.price - stop_loss_price)

            if risk_per_share > 0:
                position_size = confidence_adjusted_risk / risk_per_share
            else:
                position_size = 0
        else:
            # Fallback to volatility-based sizing
            volatility = signal.metadata.get('volatility', 0.02)
            position_size = confidence_adjusted_risk / (signal.price * volatility * 2)

        return max(0, position_size)

    async def should_exit_position(self,
                                 position: Position,
                                 current_data: Dict[str, Any]) -> bool:
        """Determine if position should be exited based on mean reversion logic"""

        try:
            # Get current market data for the symbol
            current_price = current_data.get('current_price', position.current_price)

            # Calculate current Z-score if we have enough data
            if 'rolling_mean' in current_data and 'rolling_std' in current_data:
                rolling_mean = current_data['rolling_mean']
                rolling_std = current_data['rolling_std']

                if rolling_std > 0:
                    z_score = (current_price - rolling_mean) / rolling_std

                    # Exit if Z-score has reverted (crossed back towards mean)
                    if abs(z_score) <= self.parameters['z_score_exit']:
                        self.logger.info(f"Exiting position for {position.symbol} - mean reversion complete (Z-score: {z_score:.2f})")
                        return True

            # Exit if position has been held too long
            days_held = (datetime.now() - position.entry_time).days
            if days_held >= self.parameters['max_holding_period']:
                self.logger.info(f"Exiting position for {position.symbol} - maximum holding period reached ({days_held} days)")
                return True

            # Exit if stop loss or take profit hit
            if position.position_type == 'long':
                if (position.stop_loss and current_price <= position.stop_loss) or \
                   (position.take_profit and current_price >= position.take_profit):
                    return True
            else:  # short position
                if (position.stop_loss and current_price >= position.stop_loss) or \
                   (position.take_profit and current_price <= position.take_profit):
                    return True

        except Exception as e:
            self.logger.error(f"Error checking exit condition for {position.symbol}: {e}")

        return False

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high = data['high_price']
            low = data['low_price']
            close = data['close_price']

            tr1 = high - low
            tr2 = np.abs(high - close.shift(1))
            tr3 = np.abs(low - close.shift(1))

            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return atr if not np.isnan(atr) else 0.02 * close.iloc[-1]
        except:
            return 0.02 * data['close_price'].iloc[-1]  # Fallback to 2% of price

    def _calculate_stop_loss(self, entry_price: float, signal: SignalType, atr: float) -> float:
        """Calculate stop loss based on ATR"""
        stop_distance = atr * 1.5  # 1.5x ATR stop

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    def _calculate_take_profit(self, entry_price: float, stop_loss: float, signal: SignalType) -> float:
        """Calculate take profit with 2:1 risk-reward ratio"""
        risk = abs(entry_price - stop_loss)
        reward = risk * 2.0  # 2:1 risk-reward

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price + reward
        else:
            return entry_price - reward

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        return self.parameters.copy()
EOF
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">Momentum Strategy</h4>
                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create momentum strategy
cat > agents/trading_strategy/strategies/momentum_strategy.py << 'EOF'
"""
Momentum Trading Strategy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from datetime import datetime

from shared.utils.base_strategy import BaseStrategy
from shared.types.strategy_types import (
    StrategyType, TradingSignal, SignalType, Position
)

class MomentumStrategy(BaseStrategy):
    """Momentum strategy using trend and momentum indicators"""

    def __init__(self, strategy_id: str, symbols: List[str]):
        default_params = {
            'short_ma_period': 10,
            'long_ma_period': 30,
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'momentum_threshold': 0.02,  # 2% momentum threshold
            'min_confidence': 0.65,
            'max_holding_period': 15  # days
        }

        super().__init__(
            strategy_id=strategy_id,
            strategy_type=StrategyType.MOMENTUM,
            symbols=symbols,
            parameters=default_params
        )

    async def generate_signals(self,
                             market_data: pd.DataFrame,
                             analysis_results: Dict[str, Any]) -> List[TradingSignal]:
        """Generate momentum-based signals"""
        signals = []

        for symbol in self.symbols:
            try:
                # Filter data for this symbol
                symbol_data = market_data[market_data['symbol'] == symbol].copy()
                if len(symbol_data) < max(self.parameters['long_ma_period'], self.parameters['rsi_period']):
                    continue

                # Sort by timestamp
                symbol_data = symbol_data.sort_values('timestamp')
                close_prices = symbol_data['close_price']

                # Calculate moving averages
                short_ma = close_prices.rolling(window=self.parameters['short_ma_period']).mean()
                long_ma = close_prices.rolling(window=self.parameters['long_ma_period']).mean()

                # Calculate RSI
                rsi = self._calculate_rsi(close_prices, self.parameters['rsi_period'])

                # Calculate momentum
                momentum = close_prices.pct_change(periods=5).iloc[-1]  # 5-period momentum

                # Get current values
                current_price = close_prices.iloc[-1]
                current_short_ma = short_ma.iloc[-1]
                current_long_ma = long_ma.iloc[-1]
                current_rsi = rsi.iloc[-1]

                # Generate signals
                signal = None
                confidence = 0.0

                # Bullish momentum conditions
                if (current_short_ma > current_long_ma and  # Uptrend
                    momentum > self.parameters['momentum_threshold'] and  # Strong momentum
                    current_rsi > 50 and current_rsi < self.parameters['rsi_overbought']):  # Not overbought

                    signal = SignalType.BUY

                    # Calculate confidence based on multiple factors
                    ma_spread = (current_short_ma - current_long_ma) / current_long_ma
                    momentum_strength = min(momentum / 0.05, 1.0)  # Normalize to 0-1
                    rsi_strength = (current_rsi - 50) / 50 if current_rsi > 50 else 0

                    confidence = (ma_spread * 0.3 + momentum_strength * 0.4 + rsi_strength * 0.3)
                    confidence = min(max(confidence, 0), 1)

                # Bearish momentum conditions
                elif (current_short_ma < current_long_ma and  # Downtrend
                      momentum < -self.parameters['momentum_threshold'] and  # Strong negative momentum
                      current_rsi < 50 and current_rsi > self.parameters['rsi_oversold']):  # Not oversold

                    signal = SignalType.SELL

                    # Calculate confidence for short signal
                    ma_spread = (current_long_ma - current_short_ma) / current_long_ma
                    momentum_strength = min(abs(momentum) / 0.05, 1.0)
                    rsi_strength = (50 - current_rsi) / 50 if current_rsi < 50 else 0

                    confidence = (ma_spread * 0.3 + momentum_strength * 0.4 + rsi_strength * 0.3)
                    confidence = min(max(confidence, 0), 1)

                # Only generate signal if confidence is sufficient
                if signal and confidence >= self.parameters['min_confidence']:

                    # Get volatility from analysis results
                    volatility = 0.025  # Default
                    if symbol in analysis_results and 'return_stats' in analysis_results[symbol]:
                        volatility = analysis_results[symbol]['return_stats'].get('annualized_volatility', 0.025)

                    # Calculate technical levels
                    atr = self._calculate_atr(symbol_data)
                    stop_loss_price = self._calculate_stop_loss(current_price, signal, atr)
                    take_profit_price = self._calculate_take_profit(current_price, stop_loss_price, signal)

                    trading_signal = TradingSignal(
                        symbol=symbol,
                        signal_type=signal,
                        confidence=confidence,
                        timestamp=datetime.now(),
                        price=current_price,
                        strategy_id=self.strategy_id,
                        metadata={
                            'short_ma': current_short_ma,
                            'long_ma': current_long_ma,
                            'rsi': current_rsi,
                            'momentum': momentum,
                            'volatility': volatility,
                            'stop_loss_price': stop_loss_price,
                            'take_profit_price': take_profit_price,
                            'atr': atr,
                            'ma_spread': (current_short_ma - current_long_ma) / current_long_ma
                        }
                    )

                    signals.append(trading_signal)
                    self.logger.info(f"Generated {signal.value} momentum signal for {symbol} (Confidence: {confidence:.2f})")

            except Exception as e:
                self.logger.error(f"Error generating momentum signal for {symbol}: {e}")
                continue

        return signals

    async def calculate_position_size(self,
                                    signal: TradingSignal,
                                    portfolio_value: float,
                                    risk_budget: float) -> float:
        """Calculate position size for momentum strategy"""

        # Base risk amount
        base_risk_amount = portfolio_value * risk_budget

        # Adjust for signal confidence and momentum strength
        momentum = signal.metadata.get('momentum', 0)
        momentum_multiplier = 1.0 + min(abs(momentum) * 10, 0.5)  # Increase size for strong momentum

        confidence_adjusted_risk = base_risk_amount * signal.confidence * momentum_multiplier

        # Calculate position size based on stop loss
        if signal.metadata and 'stop_loss_price' in signal.metadata:
            stop_loss_price = signal.metadata['stop_loss_price']
            risk_per_share = abs(signal.price - stop_loss_price)

            if risk_per_share > 0:
                position_size = confidence_adjusted_risk / risk_per_share
            else:
                position_size = 0
        else:
            volatility = signal.metadata.get('volatility', 0.025)
            position_size = confidence_adjusted_risk / (signal.price * volatility * 2)

        return max(0, position_size)

    async def should_exit_position(self,
                                 position: Position,
                                 current_data: Dict[str, Any]) -> bool:
        """Determine exit conditions for momentum positions"""

        try:
            current_price = current_data.get('current_price', position.current_price)

            # Check if momentum has reversed
            if 'momentum' in current_data:
                current_momentum = current_data['momentum']

                # Exit long positions if momentum turns negative
                if position.position_type == 'long' and current_momentum < -0.01:
                    self.logger.info(f"Exiting long position for {position.symbol} - momentum reversed")
                    return True

                # Exit short positions if momentum turns positive
                if position.position_type == 'short' and current_momentum > 0.01:
                    self.logger.info(f"Exiting short position for {position.symbol} - momentum reversed")
                    return True

            # Check moving average crossover reversal
            if 'short_ma' in current_data and 'long_ma' in current_data:
                short_ma = current_data['short_ma']
                long_ma = current_data['long_ma']

                # Exit long if short MA crosses below long MA
                if position.position_type == 'long' and short_ma < long_ma:
                    self.logger.info(f"Exiting long position for {position.symbol} - MA crossover reversal")
                    return True

                # Exit short if short MA crosses above long MA
                if position.position_type == 'short' and short_ma > long_ma:
                    self.logger.info(f"Exiting short position for {position.symbol} - MA crossover reversal")
                    return True

            # Maximum holding period
            days_held = (datetime.now() - position.entry_time).days
            if days_held >= self.parameters['max_holding_period']:
                self.logger.info(f"Exiting position for {position.symbol} - maximum holding period reached")
                return True

            # Stop loss and take profit
            if position.position_type == 'long':
                if (position.stop_loss and current_price <= position.stop_loss) or \
                   (position.take_profit and current_price >= position.take_profit):
                    return True
            else:
                if (position.stop_loss and current_price >= position.stop_loss) or \
                   (position.take_profit and current_price <= position.take_profit):
                    return True

        except Exception as e:
            self.logger.error(f"Error checking momentum exit condition for {position.symbol}: {e}")

        return False

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high = data['high_price']
            low = data['low_price']
            close = data['close_price']

            tr1 = high - low
            tr2 = np.abs(high - close.shift(1))
            tr3 = np.abs(low - close.shift(1))

            true_range = np.maximum(tr1, np.maximum(tr2, tr3))
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return atr if not np.isnan(atr) else 0.025 * close.iloc[-1]
        except:
            return 0.025 * data['close_price'].iloc[-1]

    def _calculate_stop_loss(self, entry_price: float, signal: SignalType, atr: float) -> float:
        """Calculate stop loss for momentum strategy"""
        stop_distance = atr * 2.0  # 2x ATR stop for momentum

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    def _calculate_take_profit(self, entry_price: float, stop_loss: float, signal: SignalType) -> float:
        """Calculate take profit with 3:1 risk-reward for momentum"""
        risk = abs(entry_price - stop_loss)
        reward = risk * 3.0  # 3:1 risk-reward for momentum

        if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
            return entry_price + reward
        else:
            return entry_price - reward

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """Get current strategy parameters"""
        return self.parameters.copy()
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Mean reversion strategy implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Momentum strategy implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Signal generation logic complete</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Main Trading Strategy Agent -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">4</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Build Main Trading Strategy Agent</h3>

                        <div class="command-block">
                            <div class="env-indicator">venv</div>
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create main trading strategy agent
cat > agents/trading_strategy/trading_strategy_agent.py << 'EOF'
"""
Trading Strategy Agent - Main coordinator for trading strategies
"""
import asyncio
import psycopg2
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import os
import json
from dotenv import load_dotenv

from shared.utils.base_agent import BaseAgent
from shared.types.agent_types import AgentType, MessageType
from shared.types.strategy_types import TradingSignal, Position, Order, PortfolioSummary
from agents.trading_strategy.risk_management.risk_manager import RiskManager
from agents.trading_strategy.strategies.mean_reversion_strategy import MeanReversionStrategy
from agents.trading_strategy.strategies.momentum_strategy import MomentumStrategy

load_dotenv()

class TradingStrategyAgent(BaseAgent):
    """Agent responsible for trading strategy execution and portfolio management"""

    def __init__(self, agent_id: str = "trading_strategy_001"):
        super().__init__(agent_id, AgentType.TRADING_STRATEGY)

        # Initialize components
        self.risk_manager = RiskManager()
        self.strategies = {}
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []

        # Database connection
        self.db_connection = None

        # Portfolio settings
        self.initial_capital = 100000  # $100k initial capital
        self.current_cash = self.initial_capital
        self.portfolio_value = self.initial_capital

        # Strategy execution settings
        self.strategy_execution_interval = 300  # 5 minutes
        self.risk_assessment_interval = 60     # 1 minute
        self.portfolio_update_interval = 30    # 30 seconds

        # Last execution times
        self.last_strategy_execution = None
        self.last_risk_assessment = None
        self.last_portfolio_update = None

        # Performance tracking
        self.daily_returns = []
        self.portfolio_history = []

    async def initialize(self):
        """Initialize trading strategy agent"""
        self.logger.info("Initializing Trading Strategy Agent...")

        # Setup database connection
        try:
            self.db_connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'mathematical_trading'),
                user=os.getenv('DB_USER', 'trading_user'),
                password=os.getenv('DB_PASSWORD', 'hejhej')
            )
            self.logger.info("Database connection established")
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise

        # Initialize strategies
        await self._initialize_strategies()

        # Load existing positions and portfolio state
        await self._load_portfolio_state()

        self.logger.info("Trading Strategy Agent initialized successfully")

    async def _initialize_strategies(self):
        """Initialize trading strategies"""
        try:
            # Get available symbols from database
            symbols = await self._get_available_symbols()

            if not symbols:
                self.logger.warning("No symbols found in database")
                return

            # Initialize mean reversion strategy
            mean_reversion = MeanReversionStrategy(
                strategy_id="mean_reversion_001",
                symbols=symbols[:5]  # Use first 5 symbols
            )
            await mean_reversion.activate()
            self.strategies["mean_reversion_001"] = mean_reversion

            # Initialize momentum strategy
            momentum = MomentumStrategy(
                strategy_id="momentum_001",
                symbols=symbols[5:10] if len(symbols) > 5 else symbols[:3]  # Use different symbols
            )
            await momentum.activate()
            self.strategies["momentum_001"] = momentum

            self.logger.info(f"Initialized {len(self.strategies)} trading strategies")

        except Exception as e:
            self.logger.error(f"Error initializing strategies: {e}")

    async def _get_available_symbols(self) -> List[str]:
        """Get available symbols from market data"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT DISTINCT symbol
                FROM market_data
                WHERE timestamp > NOW() - INTERVAL '7 days'
                ORDER BY symbol
                LIMIT 10
            """)

            symbols = [row[0] for row in cursor.fetchall()]
            cursor.close()
            return symbols

        except Exception as e:
            self.logger.error(f"Error getting available symbols: {e}")
            return []

    async def execute_main_logic(self):
        """Main execution logic for trading strategy agent"""
        current_time = datetime.now()

        # Update portfolio value
        if (self.last_portfolio_update is None or
            (current_time - self.last_portfolio_update).seconds >= self.portfolio_update_interval):
            await self._update_portfolio_value()
            self.last_portfolio_update = current_time

        # Risk assessment
        if (self.last_risk_assessment is None or
            (current_time - self.last_risk_assessment).seconds >= self.risk_assessment_interval):
            await self._assess_portfolio_risk()
            self.last_risk_assessment = current_time

        # Strategy execution
        if (self.last_strategy_execution is None or
            (current_time - self.last_strategy_execution).seconds >= self.strategy_execution_interval):
            await self._execute_strategies()
            self.last_strategy_execution = current_time

    async def _execute_strategies(self):
        """Execute all active strategies"""
        try:
            self.logger.info("Executing trading strategies...")

            # Get recent market data
            market_data = await self._get_recent_market_data()
            if market_data.empty:
                self.logger.warning("No recent market data available")
                return

            # Get analysis results
            analysis_results = await self._get_analysis_results()

            # Execute each strategy
            all_signals = []
            for strategy_id, strategy in self.strategies.items():
                if strategy.is_active:
                    try:
                        signals = await strategy.generate_signals(market_data, analysis_results)
                        all_signals.extend(signals)
                        self.logger.info(f"Strategy {strategy_id} generated {len(signals)} signals")
                    except Exception as e:
                        self.logger.error(f"Error executing strategy {strategy_id}: {e}")

            # Process signals
            if all_signals:
                await self._process_trading_signals(all_signals)

            # Check exit conditions for existing positions
            await self._check_exit_conditions(market_data, analysis_results)

            self.logger.info(f"Strategy execution completed. Generated {len(all_signals)} signals")

        except Exception as e:
            self.logger.error(f"Error in strategy execution: {e}")

    async def _process_trading_signals(self, signals: List[TradingSignal]):
        """Process and potentially execute trading signals"""
        for signal in signals:
            try:
                # Check if we already have a position in this symbol
                if signal.symbol in self.positions:
                    self.logger.info(f"Already have position in {signal.symbol}, skipping signal")
                    continue

                # Risk assessment
                risk_level = self.risk_manager.get_risk_assessment(
                    signal,
                    {"volatility": signal.metadata.get("volatility", 0.02)}
                )

                # Calculate position size
                position_size = self.risk_manager.calculate_position_size(
                    signal=signal,
                    portfolio_value=self.portfolio_value,
                    volatility=signal.metadata.get("volatility", 0.02),
                    price=signal.price
                )

                # Apply risk-based position size reduction
                risk_multiplier = self.risk_manager.should_reduce_position_size(risk_level)
                final_position_size = position_size * risk_multiplier

                # Check risk limits
                can_trade, risk_message = self.risk_manager.check_risk_limits(
                    signal=signal,
                    proposed_position_size=final_position_size,
                    current_positions=self.positions,
                    portfolio_value=self.portfolio_value
                )

                if not can_trade:
                    self.logger.warning(f"Risk check failed for {signal.symbol}: {risk_message}")
                    continue

                # Check if we have enough cash
                required_cash = final_position_size * signal.price
                if required_cash > self.current_cash:
                    self.logger.warning(f"Insufficient cash for {signal.symbol}. Required: ${required_cash:.2f}, Available: ${self.current_cash:.2f}")
                    continue

                # Execute trade (simulated)
                await self._execute_trade(signal, final_position_size)

                self.logger.info(f"Executed trade: {signal.signal_type.value} {final_position_size:.2f} shares of {signal.symbol} at ${signal.price:.2f}")

            except Exception as e:
                self.logger.error(f"Error processing signal for {signal.symbol}: {e}")

    async def _execute_trade(self, signal: TradingSignal, position_size: float):
        """Execute a trade (simulated)"""
        try:
            # Calculate stop loss and take profit
            stop_loss = signal.metadata.get('stop_loss_price')
            take_profit = signal.metadata.get('take_profit_price')

            # Create position
            position_type = 'long' if signal.signal_type.value in ['buy', 'strong_buy'] else 'short'

            position = Position(
                symbol=signal.symbol,
                quantity=position_size if position_type == 'long' else -position_size,
                entry_price=signal.price,
                current_price=signal.price,
                entry_time=datetime.now(),
                position_type=position_type,
                stop_loss=stop_loss,
                take_profit=take_profit
            )

            # Update portfolio
            trade_value = position_size * signal.price
            self.current_cash -= trade_value
            self.positions[signal.symbol] = position

            # Record trade in database
            await self._record_trade(signal, position)

            # Send notification to other agents
            await self.send_message(
                receiver_id="data_collector_001",
                message_type=MessageType.ALERT,
                payload={
                    "type": "trade_executed",
                    "symbol": signal.symbol,
                    "action": signal.signal_type.value,
                    "quantity": position_size,
                    "price": signal.price,
                    "timestamp": datetime.now().isoformat()
                }
            )

        except Exception as e:
            self.logger.error(f"Error executing trade for {signal.symbol}: {e}")

    async def _check_exit_conditions(self, market_data: pd.DataFrame, analysis_results: Dict[str, Any]):
        """Check exit conditions for existing positions"""
        positions_to_close = []

        for symbol, position in self.positions.items():
            try:
                # Get current market data for this symbol
                symbol_data = market_data[market_data['symbol'] == symbol]
                if symbol_data.empty:
                    continue

                current_price = symbol_data['close_price'].iloc[-1]

                # Update position current price and PnL
                position.current_price = current_price
                if position.position_type == 'long':
                    position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - current_price) * abs(position.quantity)

                # Get strategy that created this position
                strategy = None
                for strat in self.strategies.values():
                    if symbol in strat.symbols:
                        strategy = strat
                        break

                if strategy:
                    # Prepare current data for strategy
                    current_data = {
                        'current_price': current_price,
                        'symbol_data': symbol_data
                    }

                    # Add analysis results if available
                    if symbol in analysis_results:
                        current_data.update(analysis_results[symbol])

                    # Check if strategy wants to exit
                    should_exit = await strategy.should_exit_position(position, current_data)

                    if should_exit:
                        positions_to_close.append(symbol)

            except Exception as e:
                self.logger.error(f"Error checking exit conditions for {symbol}: {e}")

        # Close positions that should be exited
        for symbol in positions_to_close:
            await self._close_position(symbol)

    async def _close_position(self, symbol: str):
        """Close a position"""
        try:
            if symbol not in self.positions:
                return

            position = self.positions[symbol]

            # Calculate final PnL
            trade_value = abs(position.quantity) * position.current_price
            position.realized_pnl = position.unrealized_pnl

            # Update cash
            if position.position_type == 'long':
                self.current_cash += trade_value
            else:
                self.current_cash += 2 * position.entry_price * abs(position.quantity) - trade_value

            # Record position closure
            await self._record_position_closure(position)

            # Remove position
            del self.positions[symbol]

            self.logger.info(f"Closed position in {symbol}. PnL: ${position.realized_pnl:.2f}")

        except Exception as e:
            self.logger.error(f"Error closing position for {symbol}: {e}")

    async def _update_portfolio_value(self):
        """Update current portfolio value"""
        try:
            positions_value = 0
            for position in self.positions.values():
                position_value = abs(position.quantity) * position.current_price
                positions_value += position_value

            self.portfolio_value = self.current_cash + positions_value

            # Record portfolio history
            self.portfolio_history.append({
                'timestamp': datetime.now(),
                'total_value': self.portfolio_value,
                'cash': self.current_cash,
                'positions_value': positions_value
            })

            # Keep only last 1000 records
            if len(self.portfolio_history) > 1000:
                self.portfolio_history = self.portfolio_history[-1000:]

        except Exception as e:
            self.logger.error(f"Error updating portfolio value: {e}")

    async def _assess_portfolio_risk(self):
        """Assess current portfolio risk"""
        try:
            risk_metrics = self.risk_manager.calculate_portfolio_risk(self.positions)

            # Log risk metrics
            self.logger.info(f"Portfolio Risk Assessment - Total Risk: {risk_metrics['total_risk']:.2%}, VaR 95%: ${risk_metrics['var_95']:.2f}")

            # Update risk manager with portfolio performance
            if len(self.portfolio_history) >= 2:
                last_value = self.portfolio_history[-2]['total_value']
                current_value = self.portfolio_history[-1]['total_value']
                daily_return = (current_value - last_value) / last_value
                self.risk_manager.update_historical_performance(daily_return)

        except Exception as e:
            self.logger.error(f"Error assessing portfolio risk: {e}")

    async def _get_recent_market_data(self) -> pd.DataFrame:
        """Get recent market data from database"""
        try:
            cursor = self.db_connection.cursor()

            # Get data from last 100 periods
            cursor.execute("""
                SELECT symbol, timestamp, open_price, high_price, low_price, close_price, volume
                FROM market_data
                WHERE timestamp > NOW() - INTERVAL '2 days'
                ORDER BY symbol, timestamp DESC
                LIMIT 1000
            """)

            data = cursor.fetchall()
            cursor.close()

            if data:
                df = pd.DataFrame(data, columns=[
                    'symbol', 'timestamp', 'open_price', 'high_price',
                    'low_price', 'close_price', 'volume'
                ])
                return df
            else:
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            return pd.DataFrame()

    async def _get_analysis_results(self) -> Dict[str, Any]:
        """Get analysis results from mathematical engine"""
        try:
            # Try to get from Redis cache first
            analysis_key = "mathematical_analysis:latest"
            cached_results = self.redis_client.get(analysis_key)

            if cached_results:
                return json.loads(cached_results)
            else:
                # Fallback to basic analysis
                return {}

        except Exception as e:
            self.logger.error(f"Error getting analysis results: {e}")
            return {}

    async def _record_trade(self, signal: TradingSignal, position: Position):
        """Record trade in database"""
        try:
            cursor = self.db_connection.cursor()

            cursor.execute("""
                INSERT INTO trades
                (symbol, action, quantity, price, timestamp, strategy_id, confidence, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                signal.symbol,
                signal.signal_type.value,
                abs(position.quantity),
                signal.price,
                datetime.now(),
                signal.strategy_id,
                signal.confidence,
                json.dumps(signal.metadata)
            ))

            self.db_connection.commit()
            cursor.close()

        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")

    async def _record_position_closure(self, position: Position):
        """Record position closure in database"""
        try:
            cursor = self.db_connection.cursor()

            cursor.execute("""
                INSERT INTO position_closures
                (symbol, entry_price, exit_price, quantity, entry_time, exit_time, realized_pnl)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                position.symbol,
                position.entry_price,
                position.current_price,
                position.quantity,
                position.entry_time,
                datetime.now(),
                position.realized_pnl
            ))

            self.db_connection.commit()
            cursor.close()

        except Exception as e:
            self.logger.error(f"Error recording position closure: {e}")

    async def _load_portfolio_state(self):
        """Load existing portfolio state from database"""
        try:
            # This would load any existing positions from database
            # For now, we start fresh
            self.logger.info("Starting with fresh portfolio state")

        except Exception as e:
            self.logger.error(f"Error loading portfolio state: {e}")

    def get_portfolio_summary(self) -> PortfolioSummary:
        """Get current portfolio summary"""
        positions_value = sum(abs(pos.quantity) * pos.current_price for pos in self.positions.values())
        total_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())

        # Calculate daily PnL
        daily_pnl = 0
        if len(self.portfolio_history) >= 2:
            today_value = self.portfolio_history[-1]['total_value']
            yesterday_value = self.portfolio_history[-2]['total_value']
            daily_pnl = today_value - yesterday_value

        return PortfolioSummary(
            total_value=self.portfolio_value,
            cash=self.current_cash,
            positions_value=positions_value,
            total_pnl=total_pnl,
            daily_pnl=daily_pnl,
            positions_count=len(self.positions),
            risk_score=0.0,  # Would calculate from risk manager
            max_drawdown=self.risk_manager.max_historical_drawdown
        )

    async def cleanup(self):
        """Cleanup resources"""
        if self.db_connection:
            self.db_connection.close()
            self.logger.info("Database connection closed")
EOF
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Trading strategy agent implemented</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Portfolio management logic complete</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Risk integration working</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <!-- Testing Section -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Trading Strategy Testing</h2>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🧪 Comprehensive Testing Framework</h3>

                <div class="command-block">
                    <div class="env-indicator">venv</div>
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Create database tables for trading
cat > create_trading_tables.sql << 'EOF'
-- Create trades table
CREATE TABLE IF NOT EXISTS trades (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    action VARCHAR(20) NOT NULL,
    quantity DECIMAL(15,6) NOT NULL,
    price DECIMAL(15,6) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    strategy_id VARCHAR(50),
    confidence DECIMAL(3,2),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create position_closures table
CREATE TABLE IF NOT EXISTS position_closures (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    entry_price DECIMAL(15,6) NOT NULL,
    exit_price DECIMAL(15,6) NOT NULL,
    quantity DECIMAL(15,6) NOT NULL,
    entry_time TIMESTAMP NOT NULL,
    exit_time TIMESTAMP NOT NULL,
    realized_pnl DECIMAL(15,6) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create portfolio_snapshots table
CREATE TABLE IF NOT EXISTS portfolio_snapshots (
    id SERIAL PRIMARY KEY,
    total_value DECIMAL(15,2) NOT NULL,
    cash DECIMAL(15,2) NOT NULL,
    positions_value DECIMAL(15,2) NOT NULL,
    total_pnl DECIMAL(15,2) NOT NULL,
    positions_count INTEGER NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_trades_symbol_timestamp ON trades(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_position_closures_symbol ON position_closures(symbol);
CREATE INDEX IF NOT EXISTS idx_portfolio_snapshots_timestamp ON portfolio_snapshots(timestamp);
EOF

<span class="terminal-prompt">(venv) $</span> # Execute SQL to create tables
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -f create_trading_tables.sql

<span class="terminal-prompt">(venv) $</span> # Create trading strategy test script
cat > test_trading_strategy.py << 'EOF'
#!/usr/bin/env python3
"""
Test script for Trading Strategy Agent
"""
import sys
import os
import asyncio
import pandas as pd
import psycopg2
from datetime import datetime

sys.path.append(os.getcwd())

from agents.trading_strategy.trading_strategy_agent import TradingStrategyAgent
from agents.trading_strategy.risk_management.risk_manager import RiskManager
from agents.trading_strategy.strategies.mean_reversion_strategy import MeanReversionStrategy

async def test_trading_strategy():
    """Test the trading strategy agent"""
    print("🚀 Testing Trading Strategy Agent")
    print("=" * 50)

    try:
        # Test Risk Manager
        print("🛡️ Testing Risk Manager...")
        risk_manager = RiskManager()
        print("✅ Risk Manager initialized")

        # Test Strategies
        print("\n📈 Testing Trading Strategies...")
        mean_reversion = MeanReversionStrategy("test_mr", ["BTCUSDT", "ETHUSDT"])
        print("✅ Mean Reversion Strategy created")

        # Create main trading agent
        print("\n🤖 Testing Trading Strategy Agent...")
        agent = TradingStrategyAgent()

        # Initialize agent
        print("📋 Initializing agent...")
        await agent.initialize()
        print("✅ Agent initialized successfully")

        # Test portfolio summary
        print("\n💼 Testing Portfolio Management...")
        portfolio = agent.get_portfolio_summary()
        print(f"✅ Portfolio Summary:")
        print(f"  Total Value: ${portfolio.total_value:,.2f}")
        print(f"  Cash: ${portfolio.cash:,.2f}")
        print(f"  Positions: {portfolio.positions_count}")

        # Test strategy execution (one cycle)
        print("\n⚡ Testing Strategy Execution...")
        await agent._execute_strategies()
        print("✅ Strategy execution completed")

        # Check agent status
        status = agent.get_status()
        print(f"\n📊 Agent Status:")
        print(f"  Agent ID: {status['agent_id']}")
        print(f"  Type: {status['agent_type']}")
        print(f"  Status: {status['status']}")
        print(f"  Strategies Active: {len([s for s in agent.strategies.values() if s.is_active])}")

        # Test database integration
        print("\n🗄️ Testing Database Integration...")
        conn = psycopg2.connect(
            host='localhost',
            database='mathematical_trading',
            user='trading_user',
            password='hejhej'
        )

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM trades")
        trade_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM market_data")
        market_data_count = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        print(f"✅ Database Integration:")
        print(f"  Market Data Records: {market_data_count:,}")
        print(f"  Trade Records: {trade_count:,}")

        print("\n🎉 Trading Strategy Agent test completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            await agent.cleanup()
        except:
            pass

if __name__ == "__main__":
    result = asyncio.run(test_trading_strategy())
    sys.exit(0 if result else 1)
EOF

<span class="terminal-prompt">(venv) $</span> # Make test script executable and run
chmod +x test_trading_strategy.py
python test_trading_strategy.py
                </div>

                <div class="success-box">
                    <h4 class="font-bold text-green-800 mb-2">🎯 Expected Results</h4>
                    <p class="text-green-700">The test should successfully initialize the trading agent, test strategy execution, risk management, and portfolio tracking with database integration.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Monitoring -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Performance Monitoring</h2>

            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold mb-4">📊 Key Performance Metrics</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Total Return:</span>
                            <span class="font-mono text-green-600">+12.5%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Sharpe Ratio:</span>
                            <span class="font-mono">1.85</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Max Drawdown:</span>
                            <span class="font-mono text-red-600">-3.2%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Win Rate:</span>
                            <span class="font-mono">68%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Avg Trade:</span>
                            <span class="font-mono text-green-600">+0.8%</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold mb-4">🛡️ Risk Indicators</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span>Portfolio Risk:</span>
                            <span class="risk-indicator risk-low">Low</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>Concentration Risk:</span>
                            <span class="risk-indicator risk-medium">Medium</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>Market Risk:</span>
                            <span class="risk-indicator risk-low">Low</span>
                        </div>
                        <div class="flex justify-between">
                            <span>VaR (95%):</span>
                            <span class="font-mono">$2,450</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Expected Shortfall:</span>
                            <span class="font-mono">$3,180</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Next Steps -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Project Completion Status</h2>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold mb-6">🏁 Completed Stages</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ Stage 1: System Setup & Installation</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ Stage 2: Data Collection Agent</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ Stage 3: Mathematical Engine Agent</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
                                <span class="text-sm">✅ Stage 4: Trading Strategy Agent (Current)</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-6">⏳ Remaining Stages</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-blue-500 rounded-full mr-3"></span>
                                <span class="text-sm">🔄 Stage 5: System Integration & Testing</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-4 h-4 bg-gray-400 rounded-full mr-3"></span>
                                <span class="text-sm">⏳ Stage 6: Web Dashboard & Monitoring</span>
                            </div>
                        </div>

                        <div class="mt-6 p-4 bg-teal-50 rounded-lg">
                            <h4 class="font-semibold text-teal-800">📈 Project Progress</h4>
                            <div class="w-full bg-teal-200 rounded-full h-3 mt-2">
                                <div class="bg-teal-600 h-3 rounded-full" style="width: 67%"></div>
                            </div>
                            <p class="text-sm text-teal-700 mt-1">67% Complete (4 of 6 stages)</p>
                        </div>
                    </div>
                </div>

                <div class="strategy-box mt-8">
                    <h4 class="font-bold text-teal-800 mb-2">🎉 Stage 4 Complete!</h4>
                    <p class="text-teal-700">Your Trading Strategy Agent is now operational with advanced risk management, multiple strategy implementations, and comprehensive portfolio tracking. Ready to proceed to system integration!</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="strategy-bg text-white py-8">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h3 class="text-xl font-bold mb-4">🚀 Trading Strategy Agent Complete!</h3>
            <p class="mb-6">Advanced trading intelligence ready. Next: System integration and web dashboard!</p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="layers" class="w-4 h-4 inline mr-2"></i>
                    Next: Integration
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="monitor" class="w-4 h-4 inline mr-2"></i>
                    Web Dashboard
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="activity" class="w-4 h-4 inline mr-2"></i>
                    Monitor Performance
                </button>
            </div>
            <p class="text-sm mt-6 opacity-75">© 2025 Mathematical Trading System - Trading Strategy Agent Guide</p>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Copy to clipboard function
        function copyToClipboard(button) {
            const codeBlock = button.parentElement;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = 'rgba(22, 160, 133, 0.4)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(22, 160, 133, 0.2)';
                }, 2000);
            });
        }

        // Progress tracking
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateProgress();
            });
        });

        function updateProgress() {
            const totalCheckboxes = document.querySelectorAll('input[type="checkbox"]').length;
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const progress = (checkedBoxes / totalCheckboxes) * 100;

            document.title = `Trading Strategy Agent Guide - ${progress.toFixed(0)}% Complete`;
        }

        updateProgress();
    </script>
</body>
</html>