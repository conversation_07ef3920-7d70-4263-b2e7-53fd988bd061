<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Monitoring Dashboard Guide - AstroA Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .dashboard-bg {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
        }
        .step-card {
            border-left: 4px solid #f59e0b;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #f59e0b;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(245, 158, 11, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid #f59e0b;
            color: #f59e0b;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .copy-button:hover {
            background: rgba(245, 158, 11, 0.3);
        }
        .warning-box {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            border: 2px solid #f59e0b;
            color: #92400e;
        }
        .success-box {
            background: linear-gradient(45deg, #d1fae5, #a7f3d0);
            border: 2px solid #059669;
            color: #065f46;
        }
        .info-box {
            background: linear-gradient(45deg, #dbeafe, #bfdbfe);
            border: 2px solid #2563eb;
            color: #1e40af;
        }
        .code-file {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            margin: 16px 0;
        }
        .file-header {
            background: #1e293b;
            padding: 8px 16px;
            border-bottom: 1px solid #334155;
            font-family: 'Ubuntu Mono', monospace;
            color: #94a3b8;
            font-size: 14px;
        }
        .file-content {
            padding: 16px;
            color: #e2e8f0;
            font-family: 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .nav-tabs {
            display: flex;
            background: #1e293b;
            border-radius: 8px 8px 0 0;
        }
        .nav-tab {
            padding: 12px 24px;
            cursor: pointer;
            background: #334155;
            color: #94a3b8;
            border-right: 1px solid #475569;
            transition: all 0.3s ease;
        }
        .nav-tab:first-child {
            border-radius: 8px 0 0 0;
        }
        .nav-tab:last-child {
            border-right: none;
            border-radius: 0 8px 0 0;
        }
        .nav-tab.active {
            background: #f59e0b;
            color: white;
        }
        .nav-tab:hover {
            background: #475569;
        }
        .tab-content {
            display: none;
            background: #0f172a;
            padding: 24px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #334155;
            border-top: none;
        }
        .tab-content.active {
            display: block;
        }
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #f59e0b;
        }
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="dashboard-bg text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex items-center space-x-4">
                <i data-lucide="bar-chart" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-4xl font-bold">Portfolio Monitoring Dashboard Guide</h1>
                    <p class="text-xl opacity-90">Real-Time Performance Tracking for AstroA System</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8">
        <!-- Navigation -->
        <div class="mb-8">
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showTab('overview')">
                    <i data-lucide="info" class="w-4 h-4 inline mr-2"></i>Overview
                </div>
                <div class="nav-tab" onclick="showTab('architecture')">
                    <i data-lucide="layers" class="w-4 h-4 inline mr-2"></i>Architecture
                </div>
                <div class="nav-tab" onclick="showTab('implementation')">
                    <i data-lucide="code" class="w-4 h-4 inline mr-2"></i>Implementation
                </div>
                <div class="nav-tab" onclick="showTab('deployment')">
                    <i data-lucide="rocket" class="w-4 h-4 inline mr-2"></i>Deployment
                </div>
                <div class="nav-tab" onclick="showTab('features')">
                    <i data-lucide="star" class="w-4 h-4 inline mr-2"></i>Advanced Features
                </div>
                <div class="nav-tab" onclick="showTab('alerts')">
                    <i data-lucide="bell" class="w-4 h-4 inline mr-2"></i>Alerts & Notifications
                </div>
            </div>

            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 Dashboard Overview</h2>

                <div class="info-box p-6 rounded-lg mb-6">
                    <div class="flex items-center mb-3">
                        <i data-lucide="target" class="w-6 h-6 mr-3"></i>
                        <h3 class="text-xl font-bold">🎯 Why You Need a Portfolio Dashboard</h3>
                    </div>
                    <ul class="space-y-2">
                        <li>📈 Real-time portfolio performance monitoring</li>
                        <li>⚖️ Risk metrics and exposure analysis</li>
                        <li>📊 Strategy performance attribution</li>
                        <li>🚨 Alert system for significant events</li>
                        <li>📱 Mobile-responsive for monitoring anywhere</li>
                        <li>🔍 Deep-dive analytics and backtesting results</li>
                    </ul>
                </div>

                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="metric-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="trending-up" class="w-6 h-6 inline mr-2 text-orange-600"></i>
                            Performance Tracking
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>💰 Portfolio value over time</li>
                            <li>📈 Daily/monthly returns</li>
                            <li>📉 Drawdown analysis</li>
                            <li>🎯 Sharpe ratio calculation</li>
                            <li>📊 Benchmark comparisons</li>
                        </ul>
                    </div>

                    <div class="metric-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="shield" class="w-6 h-6 inline mr-2 text-orange-600"></i>
                            Risk Management
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>⚖️ Position sizing monitoring</li>
                            <li>🔄 Correlation analysis</li>
                            <li>📊 VaR calculations</li>
                            <li>🚨 Risk limit alerts</li>
                            <li>📈 Volatility tracking</li>
                        </ul>
                    </div>

                    <div class="metric-card">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="brain" class="w-6 h-6 inline mr-2 text-orange-600"></i>
                            Strategy Analytics
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>🧠 Strategy performance breakdown</li>
                            <li>📊 Win/loss ratios</li>
                            <li>⏱️ Trade duration analysis</li>
                            <li>🎯 Signal accuracy tracking</li>
                            <li>📈 Strategy optimization insights</li>
                        </ul>
                    </div>
                </div>

                <div class="success-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="check-circle" class="w-6 h-6 inline mr-2"></i>
                        🌟 Dashboard Features
                    </h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <p class="font-semibold mb-2">Real-Time Monitoring:</p>
                            <ul class="space-y-1 text-sm">
                                <li>• Live portfolio value updates</li>
                                <li>• Real-time P&L calculations</li>
                                <li>• Position monitoring</li>
                                <li>• Market data integration</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold mb-2">Advanced Analytics:</p>
                            <ul class="space-y-1 text-sm">
                                <li>• Interactive charts with Plotly</li>
                                <li>• Strategy performance heatmaps</li>
                                <li>• Risk decomposition analysis</li>
                                <li>• Custom metric calculations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Architecture Tab -->
            <div id="architecture" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🏗️ Dashboard Architecture</h2>

                <div class="chart-container mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">System Architecture Diagram</h3>
                    <div class="bg-gray-100 p-6 rounded-lg">
                        <pre class="text-sm">
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AstroA DB     │    │  Real-Time      │    │   Dashboard     │
│                 │◄───┤  Data Feeds     │◄───┤   Frontend      │
│ • Portfolio     │    │                 │    │                 │
│ • Trades        │    │ • WebSocket     │    │ • Streamlit     │
│ • Performance   │    │ • Market Data   │    │ • Plotly        │
│ • Risk Metrics  │    │ • Events        │    │ • Real-time     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                    ┌─────────────────┐
                    │  Alert System   │
                    │                 │
                    │ • Email/SMS     │
                    │ • Slack/Discord │
                    │ • Telegram      │
                    │ • Push Notify   │
                    └─────────────────┘
                        </pre>
                    </div>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Frontend Technologies</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><strong>Streamlit:</strong> Main dashboard framework</li>
                            <li><strong>Plotly:</strong> Interactive charts and graphs</li>
                            <li><strong>Pandas:</strong> Data manipulation and analysis</li>
                            <li><strong>Bootstrap:</strong> Responsive design components</li>
                            <li><strong>WebSocket:</strong> Real-time data updates</li>
                        </ul>
                    </div>

                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Backend Integration</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li><strong>PostgreSQL:</strong> Primary data source</li>
                            <li><strong>Redis:</strong> Caching and real-time updates</li>
                            <li><strong>FastAPI:</strong> API endpoints for data</li>
                            <li><strong>AsyncIO:</strong> Asynchronous data processing</li>
                            <li><strong>APScheduler:</strong> Background tasks</li>
                        </ul>
                    </div>
                </div>

                <div class="warning-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="layers" class="w-6 h-6 inline mr-2"></i>
                        🏗️ Component Overview
                    </h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div>
                            <p class="font-semibold">Data Layer:</p>
                            <ul class="text-sm space-y-1">
                                <li>• Portfolio snapshots</li>
                                <li>• Trade history</li>
                                <li>• Performance metrics</li>
                                <li>• Risk calculations</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold">Processing Layer:</p>
                            <ul class="text-sm space-y-1">
                                <li>• Real-time calculations</li>
                                <li>• Metric aggregations</li>
                                <li>• Alert evaluations</li>
                                <li>• Data transformations</li>
                            </ul>
                        </div>
                        <div>
                            <p class="font-semibold">Presentation Layer:</p>
                            <ul class="text-sm space-y-1">
                                <li>• Interactive dashboards</li>
                                <li>• Real-time charts</li>
                                <li>• Mobile responsive UI</li>
                                <li>• Export capabilities</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Implementation Tab -->
            <div id="implementation" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">💻 Implementation</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Main Dashboard Application</h3>

                    <div class="code-file">
                        <div class="file-header">dashboard/portfolio_dashboard.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
AstroA Portfolio Monitoring Dashboard
Real-time portfolio performance and risk monitoring
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import psycopg2
from datetime import datetime, timedelta
import json
import asyncio
import time
from typing import Dict, List, Optional

# Page configuration
st.set_page_config(
    page_title="AstroA Portfolio Dashboard",
    page_icon="🌟",
    layout="wide",
    initial_sidebar_state="expanded"
)

class PortfolioDashboard:
    """Main portfolio monitoring dashboard"""

    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'database': 'mathematical_trading',
            'user': 'trading_user',
            'password': 'hejhej'
        }
        self.refresh_interval = 30  # seconds

    def run(self):
        """Run the main dashboard"""
        # Custom CSS
        st.markdown("""
        <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 10px;
            color: white;
            margin: 10px 0;
        }
        .alert-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 15px;
            border-radius: 8px;
            color: white;
            margin: 5px 0;
        }
        .success-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 15px;
            border-radius: 8px;
            color: white;
            margin: 5px 0;
        }
        </style>
        """, unsafe_allow_html=True)

        # Header
        st.title("🌟 AstroA Portfolio Dashboard")
        st.markdown("Real-time performance monitoring and risk analytics")

        # Sidebar controls
        self._render_sidebar()

        # Auto-refresh toggle
        auto_refresh = st.checkbox("Auto-refresh (30s)", value=True)
        if auto_refresh:
            time.sleep(30)
            st.rerun()

        # Main dashboard content
        self._render_main_metrics()
        self._render_portfolio_charts()
        self._render_strategy_performance()
        self._render_risk_analytics()
        self._render_recent_activity()

    def _render_sidebar(self):
        """Render sidebar controls"""
        st.sidebar.header("📊 Dashboard Controls")

        # Time range selector
        time_range = st.sidebar.selectbox(
            "Time Range",
            ["24H", "7D", "30D", "90D", "1Y", "ALL"],
            index=2
        )

        # Strategy filter
        strategies = self._get_available_strategies()
        selected_strategies = st.sidebar.multiselect(
            "Strategies",
            strategies,
            default=strategies
        )

        # Portfolio metrics toggle
        st.sidebar.header("📈 Metrics")
        show_pnl = st.sidebar.checkbox("P&L Analysis", value=True)
        show_risk = st.sidebar.checkbox("Risk Metrics", value=True)
        show_sharpe = st.sidebar.checkbox("Sharpe Ratio", value=True)
        show_drawdown = st.sidebar.checkbox("Drawdown", value=True)

        # Export options
        st.sidebar.header("📋 Export")
        if st.sidebar.button("Export CSV"):
            self._export_portfolio_data()

        if st.sidebar.button("Export PDF Report"):
            self._export_pdf_report()

        return {
            'time_range': time_range,
            'strategies': selected_strategies,
            'show_pnl': show_pnl,
            'show_risk': show_risk,
            'show_sharpe': show_sharpe,
            'show_drawdown': show_drawdown
        }

    def _render_main_metrics(self):
        """Render main portfolio metrics"""
        st.header("💼 Portfolio Overview")

        try:
            metrics = self._get_portfolio_metrics()

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.markdown(f"""
                <div class="metric-card">
                    <h3>💰 Portfolio Value</h3>
                    <h2>${metrics['total_value']:,.2f}</h2>
                    <p>Change: {metrics['daily_change']:+.2f}%</p>
                </div>
                """, unsafe_allow_html=True)

            with col2:
                st.markdown(f"""
                <div class="metric-card">
                    <h3>📈 Total Return</h3>
                    <h2>{metrics['total_return']:+.2f}%</h2>
                    <p>Since inception</p>
                </div>
                """, unsafe_allow_html=True)

            with col3:
                st.markdown(f"""
                <div class="metric-card">
                    <h3>📊 Sharpe Ratio</h3>
                    <h2>{metrics['sharpe_ratio']:.2f}</h2>
                    <p>Risk-adjusted return</p>
                </div>
                """, unsafe_allow_html=True)

            with col4:
                st.markdown(f"""
                <div class="metric-card">
                    <h3>📉 Max Drawdown</h3>
                    <h2>{metrics['max_drawdown']:.2f}%</h2>
                    <p>Worst period</p>
                </div>
                """, unsafe_allow_html=True)

            # Additional metrics row
            col5, col6, col7, col8 = st.columns(4)

            with col5:
                st.metric("Open Positions", metrics['open_positions'])

            with col6:
                st.metric("Win Rate", f"{metrics['win_rate']:.1f}%")

            with col7:
                st.metric("Avg Trade Duration", f"{metrics['avg_trade_duration']:.1f}h")

            with col8:
                st.metric("Daily Volatility", f"{metrics['daily_volatility']:.2f}%")

        except Exception as e:
            st.error(f"Error loading portfolio metrics: {e}")

    def _render_portfolio_charts(self):
        """Render portfolio performance charts"""
        st.header("📈 Performance Charts")

        try:
            # Get portfolio history
            portfolio_data = self._get_portfolio_history()

            col1, col2 = st.columns(2)

            with col1:
                # Portfolio value over time
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=portfolio_data['timestamp'],
                    y=portfolio_data['total_value'],
                    mode='lines',
                    name='Portfolio Value',
                    line=dict(color='#667eea', width=3)
                ))

                fig.update_layout(
                    title="Portfolio Value Over Time",
                    xaxis_title="Date",
                    yaxis_title="Value ($)",
                    hovermode='x unified',
                    showlegend=False
                )
                st.plotly_chart(fig, use_container_width=True)

            with col2:
                # Daily returns distribution
                daily_returns = portfolio_data['daily_return'].dropna()

                fig = go.Figure()
                fig.add_trace(go.Histogram(
                    x=daily_returns,
                    nbinsx=30,
                    name='Daily Returns',
                    marker_color='#764ba2'
                ))

                fig.update_layout(
                    title="Daily Returns Distribution",
                    xaxis_title="Daily Return (%)",
                    yaxis_title="Frequency",
                    showlegend=False
                )
                st.plotly_chart(fig, use_container_width=True)

            # Cumulative returns comparison
            st.subheader("📊 Cumulative Returns vs Benchmark")

            fig = go.Figure()

            # Portfolio cumulative returns
            portfolio_cumulative = (1 + portfolio_data['daily_return'] / 100).cumprod() - 1
            fig.add_trace(go.Scatter(
                x=portfolio_data['timestamp'],
                y=portfolio_cumulative * 100,
                mode='lines',
                name='AstroA Portfolio',
                line=dict(color='#667eea', width=3)
            ))

            # Benchmark (BTC) comparison
            benchmark_data = self._get_benchmark_data()
            if not benchmark_data.empty:
                benchmark_cumulative = (1 + benchmark_data['daily_return'] / 100).cumprod() - 1
                fig.add_trace(go.Scatter(
                    x=benchmark_data['timestamp'],
                    y=benchmark_cumulative * 100,
                    mode='lines',
                    name='BTC Benchmark',
                    line=dict(color='#f59e0b', width=2, dash='dash')
                ))

            fig.update_layout(
                title="Cumulative Returns Comparison",
                xaxis_title="Date",
                yaxis_title="Cumulative Return (%)",
                hovermode='x unified'
            )
            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading portfolio charts: {e}")

    def _render_strategy_performance(self):
        """Render strategy performance breakdown"""
        st.header("🧠 Strategy Performance")

        try:
            strategy_data = self._get_strategy_performance()

            col1, col2 = st.columns(2)

            with col1:
                # Strategy returns pie chart
                fig = px.pie(
                    values=strategy_data['total_pnl'],
                    names=strategy_data['strategy_name'],
                    title="P&L by Strategy",
                    color_discrete_sequence=px.colors.qualitative.Set3
                )
                st.plotly_chart(fig, use_container_width=True)

            with col2:
                # Strategy performance table
                st.subheader("Strategy Metrics")

                display_data = strategy_data.copy()
                display_data['total_pnl'] = display_data['total_pnl'].apply(lambda x: f"${x:,.2f}")
                display_data['win_rate'] = display_data['win_rate'].apply(lambda x: f"{x:.1f}%")
                display_data['avg_return'] = display_data['avg_return'].apply(lambda x: f"{x:.2f}%")

                st.dataframe(display_data, use_container_width=True)

            # Strategy performance over time
            st.subheader("📈 Strategy Performance Timeline")

            strategy_timeline = self._get_strategy_timeline()
            fig = go.Figure()

            for strategy in strategy_timeline['strategy_name'].unique():
                strategy_data = strategy_timeline[strategy_timeline['strategy_name'] == strategy]
                fig.add_trace(go.Scatter(
                    x=strategy_data['date'],
                    y=strategy_data['cumulative_pnl'],
                    mode='lines',
                    name=strategy,
                    line=dict(width=2)
                ))

            fig.update_layout(
                title="Cumulative P&L by Strategy",
                xaxis_title="Date",
                yaxis_title="Cumulative P&L ($)",
                hovermode='x unified'
            )
            st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.error(f"Error loading strategy performance: {e}")

    def _render_risk_analytics(self):
        """Render risk analytics section"""
        st.header("⚖️ Risk Analytics")

        try:
            risk_data = self._get_risk_metrics()

            col1, col2, col3 = st.columns(3)

            with col1:
                # VaR gauge
                var_value = risk_data['value_at_risk']
                fig = go.Figure(go.Indicator(
                    mode="gauge+number+delta",
                    value=abs(var_value),
                    domain={'x': [0, 1], 'y': [0, 1]},
                    title={'text': "Value at Risk (95%)"},
                    delta={'reference': 2.0},
                    gauge={
                        'axis': {'range': [None, 5]},
                        'bar': {'color': "darkred"},
                        'steps': [
                            {'range': [0, 1], 'color': "lightgray"},
                            {'range': [1, 2], 'color': "gray"},
                            {'range': [2, 5], 'color': "red"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 2.5
                        }
                    }
                ))
                fig.update_layout(height=300)
                st.plotly_chart(fig, use_container_width=True)

            with col2:
                # Position concentration
                position_data = risk_data['position_concentration']
                fig = px.bar(
                    x=position_data['symbol'],
                    y=position_data['weight'],
                    title="Position Concentration",
                    labels={'y': 'Weight (%)', 'x': 'Symbol'}
                )
                fig.update_layout(height=300)
                st.plotly_chart(fig, use_container_width=True)

            with col3:
                # Correlation heatmap
                correlation_matrix = risk_data['correlation_matrix']
                fig = px.imshow(
                    correlation_matrix,
                    title="Asset Correlation Matrix",
                    color_continuous_scale="RdBu",
                    aspect="auto"
                )
                fig.update_layout(height=300)
                st.plotly_chart(fig, use_container_width=True)

            # Risk limits monitoring
            st.subheader("🚨 Risk Limits Monitoring")

            risk_limits = risk_data['risk_limits']
            for limit_name, limit_data in risk_limits.items():
                current_value = limit_data['current']
                limit_value = limit_data['limit']
                utilization = (current_value / limit_value) * 100

                color = "red" if utilization > 80 else "orange" if utilization > 60 else "green"

                st.progress(
                    min(utilization / 100, 1.0),
                    text=f"{limit_name}: {current_value:.2f} / {limit_value:.2f} ({utilization:.1f}%)"
                )

        except Exception as e:
            st.error(f"Error loading risk analytics: {e}")

    def _render_recent_activity(self):
        """Render recent trading activity"""
        st.header("📋 Recent Activity")

        try:
            col1, col2 = st.columns(2)

            with col1:
                # Recent trades
                st.subheader("💼 Recent Trades")
                recent_trades = self._get_recent_trades()

                if not recent_trades.empty:
                    # Format trades for display
                    display_trades = recent_trades.copy()
                    display_trades['pnl'] = display_trades['pnl'].apply(
                        lambda x: f"${x:,.2f}" if pd.notnull(x) else "Open"
                    )
                    display_trades['timestamp'] = display_trades['timestamp'].dt.strftime('%m/%d %H:%M')

                    st.dataframe(display_trades[['timestamp', 'symbol', 'side', 'quantity', 'price', 'pnl']], use_container_width=True)
                else:
                    st.info("No recent trades")

            with col2:
                # Alerts and notifications
                st.subheader("🚨 Active Alerts")
                active_alerts = self._get_active_alerts()

                if not active_alerts.empty:
                    for _, alert in active_alerts.iterrows():
                        alert_type = alert['alert_type']
                        message = alert['message']
                        severity = alert['severity']

                        if severity == 'high':
                            st.markdown(f"""
                            <div class="alert-card">
                                <strong>🚨 {alert_type.upper()}</strong><br>
                                {message}
                            </div>
                            """, unsafe_allow_html=True)
                        else:
                            st.markdown(f"""
                            <div class="success-card">
                                <strong>ℹ️ {alert_type.upper()}</strong><br>
                                {message}
                            </div>
                            """, unsafe_allow_html=True)
                else:
                    st.success("No active alerts")

        except Exception as e:
            st.error(f"Error loading recent activity: {e}")

    def _get_portfolio_metrics(self) -> Dict:
        """Get current portfolio metrics"""
        try:
            conn = psycopg2.connect(**self.db_config)

            # Get latest portfolio snapshot
            df = pd.read_sql("""
                SELECT
                    total_value,
                    cash_balance,
                    positions_value,
                    total_pnl,
                    daily_pnl
                FROM portfolio_snapshots
                ORDER BY timestamp DESC
                LIMIT 1
            """, conn)

            if df.empty:
                return self._get_default_metrics()

            latest = df.iloc[0]

            # Calculate additional metrics
            metrics = {
                'total_value': float(latest['total_value']),
                'daily_change': float(latest['daily_pnl']) / float(latest['total_value']) * 100,
                'total_return': (float(latest['total_value']) - 100000) / 100000 * 100,  # Assuming $100k initial
                'sharpe_ratio': self._calculate_sharpe_ratio(),
                'max_drawdown': self._calculate_max_drawdown(),
                'open_positions': self._get_open_positions_count(),
                'win_rate': self._calculate_win_rate(),
                'avg_trade_duration': self._calculate_avg_trade_duration(),
                'daily_volatility': self._calculate_daily_volatility()
            }

            return metrics

        except Exception as e:
            st.error(f"Error calculating portfolio metrics: {e}")
            return self._get_default_metrics()
        finally:
            if conn:
                conn.close()

    def _get_portfolio_history(self) -> pd.DataFrame:
        """Get portfolio history for charts"""
        try:
            conn = psycopg2.connect(**self.db_config)

            df = pd.read_sql("""
                SELECT
                    timestamp,
                    total_value,
                    daily_pnl,
                    LAG(total_value) OVER (ORDER BY timestamp) as prev_value
                FROM portfolio_snapshots
                WHERE timestamp > CURRENT_DATE - INTERVAL '30 days'
                ORDER BY timestamp
            """, conn)

            if not df.empty:
                df['daily_return'] = ((df['total_value'] - df['prev_value']) / df['prev_value'] * 100).fillna(0)

            return df

        except Exception as e:
            st.error(f"Error loading portfolio history: {e}")
            return pd.DataFrame()
        finally:
            if conn:
                conn.close()

    def _get_default_metrics(self) -> Dict:
        """Return default metrics when no data available"""
        return {
            'total_value': 100000.0,
            'daily_change': 0.0,
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'open_positions': 0,
            'win_rate': 0.0,
            'avg_trade_duration': 0.0,
            'daily_volatility': 0.0
        }

    # Add more helper methods...
    def _calculate_sharpe_ratio(self) -> float:
        """Calculate Sharpe ratio"""
        # Implementation here
        return 1.2

    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown"""
        # Implementation here
        return -5.2

    def _get_open_positions_count(self) -> int:
        """Get count of open positions"""
        # Implementation here
        return 3

    def _calculate_win_rate(self) -> float:
        """Calculate win rate"""
        # Implementation here
        return 65.5

    def _calculate_avg_trade_duration(self) -> float:
        """Calculate average trade duration in hours"""
        # Implementation here
        return 24.5

    def _calculate_daily_volatility(self) -> float:
        """Calculate daily volatility"""
        # Implementation here
        return 2.1

if __name__ == "__main__":
    dashboard = PortfolioDashboard()
    dashboard.run()</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Database Views for Dashboard</h3>

                    <div class="code-file">
                        <div class="file-header">setup_dashboard_views.sql</div>
                        <div class="file-content">-- Dashboard database views and functions
-- Run this to create optimized views for the dashboard

-- Portfolio performance view
CREATE OR REPLACE VIEW portfolio_performance AS
SELECT
    DATE(timestamp) as date,
    total_value,
    daily_pnl,
    LAG(total_value) OVER (ORDER BY timestamp) as prev_value,
    ((total_value - LAG(total_value) OVER (ORDER BY timestamp)) /
     LAG(total_value) OVER (ORDER BY timestamp) * 100) as daily_return_pct
FROM portfolio_snapshots
ORDER BY timestamp;

-- Strategy performance summary
CREATE OR REPLACE VIEW strategy_performance_summary AS
SELECT
    strategy_name,
    COUNT(*) as total_trades,
    SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
    SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END)::FLOAT / COUNT(*) * 100 as win_rate,
    SUM(pnl) as total_pnl,
    AVG(pnl) as avg_pnl,
    MAX(pnl) as best_trade,
    MIN(pnl) as worst_trade,
    AVG(EXTRACT(EPOCH FROM (exit_time - entry_time))/3600) as avg_duration_hours
FROM trades
WHERE exit_time IS NOT NULL
GROUP BY strategy_name;

-- Current positions view
CREATE OR REPLACE VIEW current_positions AS
SELECT
    p.symbol,
    p.quantity,
    p.entry_price,
    p.current_price,
    p.unrealized_pnl,
    p.unrealized_pnl_pct,
    p.risk_amount,
    s.strategy_name,
    p.timestamp as entry_time
FROM positions p
LEFT JOIN trades t ON p.trade_id = t.id
LEFT JOIN strategy_performance s ON t.strategy_name = s.strategy_name
WHERE p.status = 'open';

-- Risk metrics view
CREATE OR REPLACE VIEW risk_metrics_view AS
SELECT
    timestamp,
    portfolio_value,
    var_95,
    max_position_size,
    correlation_risk,
    concentration_risk,
    volatility_metric
FROM risk_metrics
WHERE timestamp > CURRENT_DATE - INTERVAL '30 days'
ORDER BY timestamp DESC;

-- Daily summary view
CREATE OR REPLACE VIEW daily_summary AS
SELECT
    DATE(timestamp) as date,
    MAX(total_value) as end_value,
    MIN(total_value) as start_value,
    SUM(daily_pnl) as daily_pnl,
    COUNT(DISTINCT symbol) as symbols_traded,
    COUNT(*) as total_trades
FROM portfolio_snapshots
GROUP BY DATE(timestamp)
ORDER BY date DESC;

-- Functions for dashboard calculations

-- Calculate Sharpe ratio
CREATE OR REPLACE FUNCTION calculate_sharpe_ratio(days INTEGER DEFAULT 30)
RETURNS DECIMAL(10,4) AS $$
DECLARE
    returns_avg DECIMAL(10,4);
    returns_std DECIMAL(10,4);
    sharpe DECIMAL(10,4);
BEGIN
    -- Calculate average and standard deviation of daily returns
    SELECT
        AVG(daily_return_pct),
        STDDEV(daily_return_pct)
    INTO returns_avg, returns_std
    FROM portfolio_performance
    WHERE date > CURRENT_DATE - INTERVAL '1 day' * days
      AND daily_return_pct IS NOT NULL;

    -- Calculate Sharpe ratio (assuming 0% risk-free rate)
    IF returns_std > 0 THEN
        sharpe := returns_avg / returns_std * SQRT(365);
    ELSE
        sharpe := 0;
    END IF;

    RETURN COALESCE(sharpe, 0);
END;
$$ LANGUAGE plpgsql;

-- Calculate maximum drawdown
CREATE OR REPLACE FUNCTION calculate_max_drawdown(days INTEGER DEFAULT 30)
RETURNS DECIMAL(10,4) AS $$
DECLARE
    max_dd DECIMAL(10,4) := 0;
    rec RECORD;
    running_max DECIMAL(20,8) := 0;
    current_dd DECIMAL(10,4);
BEGIN
    -- Calculate running maximum and drawdowns
    FOR rec IN
        SELECT total_value
        FROM portfolio_snapshots
        WHERE timestamp > CURRENT_DATE - INTERVAL '1 day' * days
        ORDER BY timestamp
    LOOP
        IF rec.total_value > running_max THEN
            running_max := rec.total_value;
        END IF;

        current_dd := (rec.total_value - running_max) / running_max * 100;

        IF current_dd < max_dd THEN
            max_dd := current_dd;
        END IF;
    END LOOP;

    RETURN max_dd;
END;
$$ LANGUAGE plpgsql;

-- Get portfolio composition
CREATE OR REPLACE FUNCTION get_portfolio_composition()
RETURNS TABLE(
    symbol VARCHAR(20),
    weight DECIMAL(10,4),
    value DECIMAL(20,8),
    pnl DECIMAL(20,8)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.symbol,
        (p.current_price * p.quantity /
         (SELECT SUM(current_price * quantity) FROM current_positions)) * 100 as weight,
        p.current_price * p.quantity as value,
        p.unrealized_pnl as pnl
    FROM current_positions p
    ORDER BY weight DESC;
END;
$$ LANGUAGE plpgsql;

-- Alert triggers
CREATE OR REPLACE FUNCTION check_risk_alerts()
RETURNS VOID AS $$
DECLARE
    current_drawdown DECIMAL(10,4);
    var_95 DECIMAL(10,4);
    max_position DECIMAL(10,4);
BEGIN
    -- Check drawdown alert
    SELECT calculate_max_drawdown(7) INTO current_drawdown;

    IF current_drawdown < -10 THEN
        INSERT INTO alerts (alert_type, severity, message, timestamp)
        VALUES ('drawdown', 'high',
                'Maximum drawdown exceeded 10%: ' || current_drawdown || '%',
                CURRENT_TIMESTAMP);
    END IF;

    -- Check concentration risk
    SELECT MAX(weight) INTO max_position FROM get_portfolio_composition();

    IF max_position > 20 THEN
        INSERT INTO alerts (alert_type, severity, message, timestamp)
        VALUES ('concentration', 'medium',
                'Position concentration risk: ' || max_position || '%',
                CURRENT_TIMESTAMP);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create alerts table if not exists
CREATE TABLE IF NOT EXISTS alerts (
    id SERIAL PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    acknowledged BOOLEAN DEFAULT FALSE,
    resolved BOOLEAN DEFAULT FALSE
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_portfolio_snapshots_timestamp_desc
ON portfolio_snapshots(timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_trades_strategy_exit_time
ON trades(strategy_name, exit_time) WHERE exit_time IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_alerts_unresolved
ON alerts(timestamp DESC) WHERE resolved = FALSE;</div>
                    </div>
                </div>
            </div>

            <!-- Deployment Tab -->
            <div id="deployment" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🚀 Deployment & Setup</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 1: Install Dependencies</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Navigate to AstroA directory
cd /home/<USER>/axmadcodes/AstroA

# Activate virtual environment
source venv/bin/activate

# Install dashboard dependencies
pip install streamlit
pip install plotly
pip install streamlit-autorefresh
pip install streamlit-aggrid
pip install streamlit-option-menu

# Verify installations
python -c "import streamlit; print('Streamlit installed')"
python -c "import plotly; print('Plotly installed')"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 2: Setup Database Views</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create dashboard database views
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -f setup_dashboard_views.sql

# Verify views created
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT viewname FROM pg_views WHERE schemaname = 'public' AND viewname LIKE '%performance%';"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 3: Create Dashboard Directory</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Create dashboard directory structure
mkdir -p dashboard/pages
mkdir -p dashboard/components
mkdir -p dashboard/utils
mkdir -p dashboard/assets

# Create configuration file
cat > dashboard/config.py << 'EOF'
"""Dashboard configuration"""
import os

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'mathematical_trading',
    'user': 'trading_user',
    'password': 'hejhej'
}

# Dashboard settings
REFRESH_INTERVAL = 30  # seconds
PAGE_TITLE = "AstroA Portfolio Dashboard"
PAGE_ICON = "🌟"
LAYOUT = "wide"

# Chart settings
CHART_HEIGHT = 400
COLOR_PALETTE = [
    '#667eea', '#764ba2', '#f093fb', '#f5576c',
    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
]
EOF</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 4: Start Dashboard</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Start the dashboard
streamlit run dashboard/portfolio_dashboard.py --server.port 8501

# Access dashboard at: http://localhost:8501</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 5: Test Dashboard</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Test database connectivity
python -c "
import psycopg2
import pandas as pd

db_config = {
    'host': 'localhost',
    'database': 'mathematical_trading',
    'user': 'trading_user',
    'password': 'hejhej'
}

try:
    conn = psycopg2.connect(**db_config)
    df = pd.read_sql('SELECT COUNT(*) FROM portfolio_snapshots', conn)
    print(f'✅ Database connected. Portfolio snapshots: {df.iloc[0, 0]}')
    conn.close()
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"</div>
                </div>
            </div>

            <!-- Advanced Features Tab -->
            <div id="features" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">⭐ Advanced Features</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Multi-Page Dashboard</h3>

                    <div class="code-file">
                        <div class="file-header">dashboard/pages/strategy_analysis.py</div>
                        <div class="file-content">"""
Strategy Analysis Page
Detailed analysis of individual trading strategies
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import psycopg2
from datetime import datetime, timedelta

def render_strategy_analysis():
    """Render strategy analysis page"""
    st.title("🧠 Strategy Analysis")

    # Strategy selector
    strategies = get_available_strategies()
    selected_strategy = st.selectbox("Select Strategy", strategies)

    if selected_strategy:
        render_strategy_overview(selected_strategy)
        render_strategy_performance_chart(selected_strategy)
        render_strategy_trades_analysis(selected_strategy)
        render_strategy_optimization(selected_strategy)

def render_strategy_overview(strategy_name):
    """Render strategy overview metrics"""
    st.subheader(f"📊 {strategy_name} Overview")

    metrics = get_strategy_metrics(strategy_name)

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Trades", metrics['total_trades'])

    with col2:
        st.metric("Win Rate", f"{metrics['win_rate']:.1f}%")

    with col3:
        st.metric("Total P&L", f"${metrics['total_pnl']:,.2f}")

    with col4:
        st.metric("Avg Trade", f"${metrics['avg_trade']:,.2f}")

def render_strategy_performance_chart(strategy_name):
    """Render strategy performance chart"""
    st.subheader("📈 Performance Chart")

    performance_data = get_strategy_performance_data(strategy_name)

    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Cumulative P&L', 'Trade P&L Distribution'),
        vertical_spacing=0.1
    )

    # Cumulative P&L
    fig.add_trace(
        go.Scatter(
            x=performance_data['date'],
            y=performance_data['cumulative_pnl'],
            mode='lines',
            name='Cumulative P&L',
            line=dict(color='#667eea', width=3)
        ),
        row=1, col=1
    )

    # Trade P&L histogram
    fig.add_trace(
        go.Histogram(
            x=performance_data['trade_pnl'],
            name='Trade P&L',
            marker_color='#764ba2',
            nbinsx=20
        ),
        row=2, col=1
    )

    fig.update_layout(height=600, showlegend=False)
    st.plotly_chart(fig, use_container_width=True)

def render_strategy_trades_analysis(strategy_name):
    """Render detailed trades analysis"""
    st.subheader("📋 Trades Analysis")

    trades_data = get_strategy_trades(strategy_name)

    if not trades_data.empty:
        # Trades table
        st.dataframe(trades_data, use_container_width=True)

        # Trade duration analysis
        col1, col2 = st.columns(2)

        with col1:
            fig = px.histogram(
                trades_data,
                x='duration_hours',
                title='Trade Duration Distribution',
                nbins=20
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            fig = px.scatter(
                trades_data,
                x='duration_hours',
                y='pnl',
                title='P&L vs Duration',
                color='pnl',
                color_continuous_scale='RdYlGn'
            )
            st.plotly_chart(fig, use_container_width=True)

def render_strategy_optimization(strategy_name):
    """Render strategy optimization suggestions"""
    st.subheader("🔧 Optimization Suggestions")

    optimization_data = analyze_strategy_optimization(strategy_name)

    for suggestion in optimization_data['suggestions']:
        st.info(f"💡 {suggestion}")

    # Parameter sensitivity analysis
    if optimization_data['parameter_analysis']:
        st.subheader("📊 Parameter Sensitivity")

        param_data = optimization_data['parameter_analysis']
        fig = px.line(
            param_data,
            x='parameter_value',
            y='performance_metric',
            title='Parameter Sensitivity Analysis'
        )
        st.plotly_chart(fig, use_container_width=True)

# Helper functions
def get_available_strategies():
    """Get list of available strategies"""
    # Implementation here
    return ['Mean Reversion', 'Momentum', 'Pairs Trading']

def get_strategy_metrics(strategy_name):
    """Get strategy performance metrics"""
    # Implementation here
    return {
        'total_trades': 50,
        'win_rate': 65.5,
        'total_pnl': 5420.50,
        'avg_trade': 108.41
    }

def get_strategy_performance_data(strategy_name):
    """Get strategy performance data"""
    # Implementation here
    return pd.DataFrame()

def get_strategy_trades(strategy_name):
    """Get strategy trades data"""
    # Implementation here
    return pd.DataFrame()

def analyze_strategy_optimization(strategy_name):
    """Analyze strategy for optimization opportunities"""
    # Implementation here
    return {
        'suggestions': [
            "Consider reducing position size during high volatility periods",
            "Exit strategy could be optimized for better risk-adjusted returns",
            "Entry timing shows room for improvement"
        ],
        'parameter_analysis': pd.DataFrame()
    }</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Real-Time Data Updates</h3>

                    <div class="code-file">
                        <div class="file-header">dashboard/components/realtime_updates.py</div>
                        <div class="file-content">"""
Real-time dashboard updates using WebSocket and caching
"""

import streamlit as st
import asyncio
import websocket
import json
from datetime import datetime
import threading
import time
import pandas as pd

class RealTimeUpdater:
    """Handle real-time data updates for dashboard"""

    def __init__(self):
        self.ws = None
        self.is_connected = False
        self.data_cache = {}
        self.update_callbacks = []

    def start_websocket_connection(self):
        """Start WebSocket connection for real-time updates"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                self.handle_real_time_data(data)
            except Exception as e:
                st.error(f"WebSocket message error: {e}")

        def on_error(ws, error):
            st.error(f"WebSocket error: {error}")
            self.is_connected = False

        def on_close(ws, close_status_code, close_msg):
            st.warning("WebSocket connection closed")
            self.is_connected = False

        def on_open(ws):
            st.success("WebSocket connected")
            self.is_connected = True

        # Connect to real-time data feed
        websocket_url = "ws://localhost:8765/realtime"
        self.ws = websocket.WebSocketApp(
            websocket_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )

        # Run in separate thread
        wst = threading.Thread(target=self.ws.run_forever)
        wst.daemon = True
        wst.start()

    def handle_real_time_data(self, data):
        """Handle incoming real-time data"""
        data_type = data.get('type')
        payload = data.get('data')

        if data_type == 'portfolio_update':
            self.update_portfolio_cache(payload)
        elif data_type == 'new_trade':
            self.update_trades_cache(payload)
        elif data_type == 'price_update':
            self.update_prices_cache(payload)

        # Trigger dashboard refresh
        self.trigger_refresh()

    def update_portfolio_cache(self, data):
        """Update portfolio data cache"""
        self.data_cache['portfolio'] = {
            'timestamp': datetime.now(),
            'data': data
        }

    def update_trades_cache(self, data):
        """Update trades data cache"""
        if 'trades' not in self.data_cache:
            self.data_cache['trades'] = []

        self.data_cache['trades'].append({
            'timestamp': datetime.now(),
            'data': data
        })

        # Keep only last 100 trades
        self.data_cache['trades'] = self.data_cache['trades'][-100:]

    def update_prices_cache(self, data):
        """Update price data cache"""
        symbol = data.get('symbol')
        if 'prices' not in self.data_cache:
            self.data_cache['prices'] = {}

        self.data_cache['prices'][symbol] = {
            'timestamp': datetime.now(),
            'price': data.get('price'),
            'volume': data.get('volume')
        }

    def trigger_refresh(self):
        """Trigger dashboard refresh"""
        # Use Streamlit's session state to trigger refresh
        if 'refresh_counter' not in st.session_state:
            st.session_state.refresh_counter = 0

        st.session_state.refresh_counter += 1

    def get_cached_data(self, data_type):
        """Get cached data by type"""
        return self.data_cache.get(data_type)

    def is_data_fresh(self, data_type, max_age_seconds=30):
        """Check if cached data is fresh"""
        cached = self.data_cache.get(data_type)
        if not cached:
            return False

        age = (datetime.now() - cached['timestamp']).total_seconds()
        return age <= max_age_seconds

# Streamlit components for real-time updates
def render_realtime_indicator():
    """Render real-time connection indicator"""
    updater = get_realtime_updater()

    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        status_color = "🟢" if updater.is_connected else "🔴"
        st.write(f"{status_color} Real-time: {'Connected' if updater.is_connected else 'Disconnected'}")

    with col2:
        if st.button("Reconnect"):
            updater.start_websocket_connection()

    with col3:
        refresh_counter = st.session_state.get('refresh_counter', 0)
        st.write(f"Updates: {refresh_counter}")

def render_realtime_prices():
    """Render real-time price updates"""
    updater = get_realtime_updater()
    prices_data = updater.get_cached_data('prices')

    if prices_data:
        st.subheader("💰 Live Prices")

        price_df = pd.DataFrame([
            {
                'Symbol': symbol,
                'Price': f"${data['price']:.2f}",
                'Volume': f"{data['volume']:,.0f}",
                'Updated': data['timestamp'].strftime('%H:%M:%S')
            }
            for symbol, data in prices_data.items()
        ])

        st.dataframe(price_df, use_container_width=True)
    else:
        st.info("No real-time price data available")

@st.cache_resource
def get_realtime_updater():
    """Get or create real-time updater instance"""
    return RealTimeUpdater()

# Auto-refresh component
def setup_auto_refresh():
    """Setup auto-refresh for dashboard"""
    if st.checkbox("Auto-refresh", value=True):
        # Use JavaScript to refresh page
        st.markdown(
            """
            <script>
            setTimeout(function(){
                window.location.reload();
            }, 30000);
            </script>
            """,
            unsafe_allow_html=True
        )

# Performance optimization
@st.cache_data(ttl=30)
def get_cached_portfolio_data():
    """Get cached portfolio data with TTL"""
    updater = get_realtime_updater()
    return updater.get_cached_data('portfolio')

@st.cache_data(ttl=60)
def get_cached_historical_data():
    """Get cached historical data with longer TTL"""
    # Implementation for historical data
    return pd.DataFrame()</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Export and Reporting</h3>

                    <div class="code-file">
                        <div class="file-header">dashboard/utils/export_utils.py</div>
                        <div class="file-content">"""
Export and reporting utilities for dashboard
"""

import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import io
import base64
from datetime import datetime
import jinja2
import pdfkit
import streamlit as st

class DashboardExporter:
    """Handle dashboard exports and reports"""

    def __init__(self, db_config):
        self.db_config = db_config

    def export_portfolio_csv(self, date_range=30):
        """Export portfolio data as CSV"""
        try:
            import psycopg2

            conn = psycopg2.connect(**self.db_config)

            # Portfolio performance data
            portfolio_df = pd.read_sql(f"""
                SELECT
                    timestamp,
                    total_value,
                    cash_balance,
                    positions_value,
                    daily_pnl,
                    total_pnl
                FROM portfolio_snapshots
                WHERE timestamp > CURRENT_DATE - INTERVAL '{date_range} days'
                ORDER BY timestamp
            """, conn)

            # Trades data
            trades_df = pd.read_sql(f"""
                SELECT
                    symbol,
                    side,
                    quantity,
                    entry_price,
                    exit_price,
                    pnl,
                    strategy_name,
                    entry_time,
                    exit_time
                FROM trades
                WHERE entry_time > CURRENT_DATE - INTERVAL '{date_range} days'
                ORDER BY entry_time
            """, conn)

            conn.close()

            # Create Excel file with multiple sheets
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                portfolio_df.to_excel(writer, sheet_name='Portfolio', index=False)
                trades_df.to_excel(writer, sheet_name='Trades', index=False)

            output.seek(0)
            return output

        except Exception as e:
            st.error(f"Export error: {e}")
            return None

    def generate_pdf_report(self, date_range=30):
        """Generate comprehensive PDF report"""
        try:
            # Get data
            portfolio_data = self._get_portfolio_data(date_range)
            trades_data = self._get_trades_data(date_range)
            performance_metrics = self._calculate_performance_metrics(portfolio_data)

            # Generate charts
            charts = self._generate_report_charts(portfolio_data, trades_data)

            # Create HTML report
            html_content = self._generate_html_report(
                portfolio_data, trades_data, performance_metrics, charts
            )

            # Convert to PDF
            pdf = pdfkit.from_string(html_content, False, options={
                'page-size': 'A4',
                'margin-top': '0.75in',
                'margin-right': '0.75in',
                'margin-bottom': '0.75in',
                'margin-left': '0.75in',
                'encoding': "UTF-8",
                'no-outline': None
            })

            return pdf

        except Exception as e:
            st.error(f"PDF generation error: {e}")
            return None

    def _generate_html_report(self, portfolio_data, trades_data, metrics, charts):
        """Generate HTML report content"""
        template = jinja2.Template("""
        <!DOCTYPE html>
        <html>
        <head>
            <title>AstroA Portfolio Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { text-align: center; margin-bottom: 30px; }
                .metric { display: inline-block; margin: 10px; padding: 15px;
                         background: #f0f0f0; border-radius: 5px; min-width: 150px; }
                .chart { margin: 20px 0; text-align: center; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌟 AstroA Portfolio Report</h1>
                <p>Generated on: {{ report_date }}</p>
            </div>

            <h2>📊 Performance Summary</h2>
            <div class="metric">
                <strong>Portfolio Value</strong><br>
                ${{ "%.2f"|format(metrics.total_value) }}
            </div>
            <div class="metric">
                <strong>Total Return</strong><br>
                {{ "%.2f"|format(metrics.total_return) }}%
            </div>
            <div class="metric">
                <strong>Sharpe Ratio</strong><br>
                {{ "%.2f"|format(metrics.sharpe_ratio) }}
            </div>
            <div class="metric">
                <strong>Max Drawdown</strong><br>
                {{ "%.2f"|format(metrics.max_drawdown) }}%
            </div>

            <h2>📈 Portfolio Performance</h2>
            <div class="chart">
                {{ charts.portfolio_chart }}
            </div>

            <h2>🧠 Strategy Performance</h2>
            <div class="chart">
                {{ charts.strategy_chart }}
            </div>

            <h2>📋 Recent Trades</h2>
            <table>
                <tr>
                    <th>Date</th>
                    <th>Symbol</th>
                    <th>Side</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>P&L</th>
                    <th>Strategy</th>
                </tr>
                {% for trade in recent_trades %}
                <tr>
                    <td>{{ trade.entry_time.strftime('%Y-%m-%d') }}</td>
                    <td>{{ trade.symbol }}</td>
                    <td>{{ trade.side }}</td>
                    <td>{{ trade.quantity }}</td>
                    <td>${{ "%.2f"|format(trade.entry_price) }}</td>
                    <td>${{ "%.2f"|format(trade.pnl) }}</td>
                    <td>{{ trade.strategy_name }}</td>
                </tr>
                {% endfor %}
            </table>

            <div style="margin-top: 50px; text-align: center; color: #666;">
                <p>Generated by AstroA Autonomous Trading System</p>
            </div>
        </body>
        </html>
        """)

        return template.render(
            report_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            metrics=metrics,
            charts=charts,
            recent_trades=trades_data.tail(20).to_dict('records')
        )

    def _generate_report_charts(self, portfolio_data, trades_data):
        """Generate charts for PDF report"""
        charts = {}

        # Portfolio performance chart
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=portfolio_data['timestamp'],
            y=portfolio_data['total_value'],
            mode='lines',
            name='Portfolio Value'
        ))
        fig.update_layout(title='Portfolio Performance', height=300)
        charts['portfolio_chart'] = fig.to_html(div_id="portfolio_chart")

        # Strategy performance chart
        strategy_performance = trades_data.groupby('strategy_name')['pnl'].sum()
        fig = px.pie(
            values=strategy_performance.values,
            names=strategy_performance.index,
            title='Strategy Performance'
        )
        fig.update_layout(height=300)
        charts['strategy_chart'] = fig.to_html(div_id="strategy_chart")

        return charts

    def create_download_link(self, data, filename, link_text):
        """Create download link for data"""
        if data:
            b64 = base64.b64encode(data).decode()
            href = f'<a href="data:application/octet-stream;base64,{b64}" download="{filename}">{link_text}</a>'
            return href
        return None

# Usage in dashboard
def render_export_section():
    """Render export section in dashboard"""
    st.header("📋 Export & Reports")

    exporter = DashboardExporter(st.session_state.db_config)

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📄 Export CSV"):
            csv_data = exporter.export_portfolio_csv()
            if csv_data:
                st.download_button(
                    label="Download CSV",
                    data=csv_data,
                    file_name=f"portfolio_export_{datetime.now().strftime('%Y%m%d')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )

    with col2:
        if st.button("📊 Generate PDF Report"):
            with st.spinner("Generating PDF report..."):
                pdf_data = exporter.generate_pdf_report()
                if pdf_data:
                    st.download_button(
                        label="Download PDF",
                        data=pdf_data,
                        file_name=f"portfolio_report_{datetime.now().strftime('%Y%m%d')}.pdf",
                        mime="application/pdf"
                    )

    with col3:
        if st.button("📧 Email Report"):
            st.info("Email functionality will be implemented with alerts system")</div>
                    </div>
                </div>
            </div>

            <!-- Alerts Tab -->
            <div id="alerts" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🚨 Alerts & Notifications</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Alert System Implementation</h3>

                    <div class="code-file">
                        <div class="file-header">dashboard/alerts/alert_system.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
Alert System for AstroA Portfolio Dashboard
Monitors portfolio metrics and sends notifications
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import psycopg2
import json
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import requests
import telegram

class AlertSystem:
    """Comprehensive alert system for portfolio monitoring"""

    def __init__(self, db_config: Dict, alert_config: Dict):
        self.db_config = db_config
        self.alert_config = alert_config
        self.logger = logging.getLogger(__name__)

        # Alert thresholds
        self.thresholds = {
            'max_drawdown': -10.0,  # 10% max drawdown
            'daily_loss': -5.0,     # 5% daily loss
            'position_concentration': 20.0,  # 20% max position
            'var_limit': 5.0,       # 5% VaR limit
            'consecutive_losses': 5,  # 5 consecutive losing trades
            'portfolio_volatility': 15.0  # 15% annualized volatility
        }

        # Notification channels
        self.email_enabled = alert_config.get('email_enabled', False)
        self.telegram_enabled = alert_config.get('telegram_enabled', False)
        self.slack_enabled = alert_config.get('slack_enabled', False)
        self.sms_enabled = alert_config.get('sms_enabled', False)

    async def run_alert_monitoring(self):
        """Run continuous alert monitoring"""
        self.logger.info("Starting alert monitoring system")

        while True:
            try:
                await self.check_all_alerts()
                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Alert monitoring error: {str(e)}")
                await asyncio.sleep(5)

    async def check_all_alerts(self):
        """Check all alert conditions"""
        alerts_triggered = []

        # Portfolio-level alerts
        portfolio_alerts = await self.check_portfolio_alerts()
        alerts_triggered.extend(portfolio_alerts)

        # Risk-based alerts
        risk_alerts = await self.check_risk_alerts()
        alerts_triggered.extend(risk_alerts)

        # Strategy-based alerts
        strategy_alerts = await self.check_strategy_alerts()
        alerts_triggered.extend(strategy_alerts)

        # Market-based alerts
        market_alerts = await self.check_market_alerts()
        alerts_triggered.extend(market_alerts)

        # Send notifications for triggered alerts
        for alert in alerts_triggered:
            await self.send_alert_notification(alert)

    async def check_portfolio_alerts(self) -> List[Dict]:
        """Check portfolio-level alert conditions"""
        alerts = []

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # Check drawdown
            cursor.execute("""
                SELECT calculate_max_drawdown(7) as drawdown
            """)
            drawdown = cursor.fetchone()[0]

            if drawdown < self.thresholds['max_drawdown']:
                alerts.append({
                    'type': 'portfolio_drawdown',
                    'severity': 'high',
                    'message': f"Portfolio drawdown exceeded limit: {drawdown:.2f}%",
                    'value': drawdown,
                    'threshold': self.thresholds['max_drawdown']
                })

            # Check daily loss
            cursor.execute("""
                SELECT daily_pnl / LAG(total_value) OVER (ORDER BY timestamp) * 100 as daily_return
                FROM portfolio_snapshots
                ORDER BY timestamp DESC
                LIMIT 1
            """)
            daily_return = cursor.fetchone()
            if daily_return and daily_return[0] < self.thresholds['daily_loss']:
                alerts.append({
                    'type': 'daily_loss',
                    'severity': 'medium',
                    'message': f"Daily loss exceeded limit: {daily_return[0]:.2f}%",
                    'value': daily_return[0],
                    'threshold': self.thresholds['daily_loss']
                })

            # Check position concentration
            cursor.execute("""
                SELECT MAX(weight) as max_concentration
                FROM get_portfolio_composition()
            """)
            max_concentration = cursor.fetchone()[0]

            if max_concentration > self.thresholds['position_concentration']:
                alerts.append({
                    'type': 'position_concentration',
                    'severity': 'medium',
                    'message': f"Position concentration risk: {max_concentration:.2f}%",
                    'value': max_concentration,
                    'threshold': self.thresholds['position_concentration']
                })

        except Exception as e:
            self.logger.error(f"Portfolio alerts check error: {str(e)}")
        finally:
            if conn:
                conn.close()

        return alerts

    async def check_risk_alerts(self) -> List[Dict]:
        """Check risk-based alert conditions"""
        alerts = []

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # Check VaR
            cursor.execute("""
                SELECT var_95
                FROM risk_metrics
                ORDER BY timestamp DESC
                LIMIT 1
            """)
            var_result = cursor.fetchone()
            if var_result and abs(var_result[0]) > self.thresholds['var_limit']:
                alerts.append({
                    'type': 'var_limit',
                    'severity': 'high',
                    'message': f"VaR limit exceeded: {abs(var_result[0]):.2f}%",
                    'value': abs(var_result[0]),
                    'threshold': self.thresholds['var_limit']
                })

            # Check portfolio volatility
            cursor.execute("""
                SELECT STDDEV(daily_return_pct) * SQRT(365) as annualized_volatility
                FROM portfolio_performance
                WHERE date > CURRENT_DATE - INTERVAL '30 days'
            """)
            volatility = cursor.fetchone()[0]
            if volatility > self.thresholds['portfolio_volatility']:
                alerts.append({
                    'type': 'high_volatility',
                    'severity': 'medium',
                    'message': f"Portfolio volatility elevated: {volatility:.2f}%",
                    'value': volatility,
                    'threshold': self.thresholds['portfolio_volatility']
                })

        except Exception as e:
            self.logger.error(f"Risk alerts check error: {str(e)}")
        finally:
            if conn:
                conn.close()

        return alerts

    async def check_strategy_alerts(self) -> List[Dict]:
        """Check strategy-based alert conditions"""
        alerts = []

        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # Check consecutive losses by strategy
            cursor.execute("""
                WITH consecutive_losses AS (
                    SELECT
                        strategy_name,
                        ROW_NUMBER() OVER (PARTITION BY strategy_name ORDER BY exit_time DESC) as rn,
                        pnl,
                        CASE WHEN pnl < 0 THEN 1 ELSE 0 END as is_loss
                    FROM trades
                    WHERE exit_time IS NOT NULL
                      AND exit_time > CURRENT_DATE - INTERVAL '30 days'
                ),
                loss_streaks AS (
                    SELECT
                        strategy_name,
                        SUM(is_loss) as consecutive_losses
                    FROM consecutive_losses
                    WHERE rn <= 10  -- Check last 10 trades
                    GROUP BY strategy_name
                )
                SELECT strategy_name, consecutive_losses
                FROM loss_streaks
                WHERE consecutive_losses >= %s
            """, (self.thresholds['consecutive_losses'],))

            for strategy_name, losses in cursor.fetchall():
                alerts.append({
                    'type': 'consecutive_losses',
                    'severity': 'medium',
                    'message': f"Strategy {strategy_name} has {losses} consecutive losses",
                    'value': losses,
                    'threshold': self.thresholds['consecutive_losses'],
                    'strategy': strategy_name
                })

        except Exception as e:
            self.logger.error(f"Strategy alerts check error: {str(e)}")
        finally:
            if conn:
                conn.close()

        return alerts

    async def check_market_alerts(self) -> List[Dict]:
        """Check market-based alert conditions"""
        alerts = []

        try:
            # Check for unusual market movements
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    symbol,
                    event_data
                FROM market_events
                WHERE timestamp > CURRENT_TIMESTAMP - INTERVAL '1 hour'
                  AND event_type = 'price_move'
                  AND processed = FALSE
            """)

            for symbol, event_data in cursor.fetchall():
                data = json.loads(event_data)
                price_change = data.get('price_change_pct', 0)

                if abs(price_change) > 10:  # 10% price move
                    alerts.append({
                        'type': 'market_volatility',
                        'severity': 'low',
                        'message': f"{symbol} moved {price_change:.2f}% in last hour",
                        'value': price_change,
                        'symbol': symbol
                    })

        except Exception as e:
            self.logger.error(f"Market alerts check error: {str(e)}")
        finally:
            if conn:
                conn.close()

        return alerts

    async def send_alert_notification(self, alert: Dict):
        """Send alert notification through configured channels"""
        try:
            # Log alert to database
            await self.log_alert_to_database(alert)

            # Send notifications
            if self.email_enabled:
                await self.send_email_alert(alert)

            if self.telegram_enabled:
                await self.send_telegram_alert(alert)

            if self.slack_enabled:
                await self.send_slack_alert(alert)

            if self.sms_enabled:
                await self.send_sms_alert(alert)

        except Exception as e:
            self.logger.error(f"Alert notification error: {str(e)}")

    async def log_alert_to_database(self, alert: Dict):
        """Log alert to database"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO alerts (alert_type, severity, message, metadata, timestamp)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                alert['type'],
                alert['severity'],
                alert['message'],
                json.dumps(alert),
                datetime.now()
            ))

            conn.commit()

        except Exception as e:
            self.logger.error(f"Database alert logging error: {str(e)}")
        finally:
            if conn:
                conn.close()

    async def send_email_alert(self, alert: Dict):
        """Send email alert"""
        try:
            smtp_config = self.alert_config.get('email', {})

            msg = MimeMultipart()
            msg['From'] = smtp_config.get('from_email')
            msg['To'] = smtp_config.get('to_email')
            msg['Subject'] = f"AstroA Alert: {alert['type'].replace('_', ' ').title()}"

            body = f"""
            Alert Details:

            Type: {alert['type']}
            Severity: {alert['severity']}
            Message: {alert['message']}
            Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            Value: {alert.get('value', 'N/A')}
            Threshold: {alert.get('threshold', 'N/A')}

            Please review your AstroA dashboard for more details.

            Best regards,
            AstroA Alert System
            """

            msg.attach(MimeText(body, 'plain'))

            server = smtplib.SMTP(smtp_config.get('smtp_server'), smtp_config.get('smtp_port'))
            server.starttls()
            server.login(smtp_config.get('username'), smtp_config.get('password'))
            text = msg.as_string()
            server.sendmail(smtp_config.get('from_email'), smtp_config.get('to_email'), text)
            server.quit()

        except Exception as e:
            self.logger.error(f"Email alert error: {str(e)}")

    async def send_telegram_alert(self, alert: Dict):
        """Send Telegram alert"""
        try:
            telegram_config = self.alert_config.get('telegram', {})
            bot_token = telegram_config.get('bot_token')
            chat_id = telegram_config.get('chat_id')

            if bot_token and chat_id:
                bot = telegram.Bot(token=bot_token)

                severity_emoji = {
                    'low': 'ℹ️',
                    'medium': '⚠️',
                    'high': '🚨'
                }

                message = f"""
{severity_emoji.get(alert['severity'], '🔔')} *AstroA Alert*

*Type:* {alert['type'].replace('_', ' ').title()}
*Severity:* {alert['severity'].upper()}
*Message:* {alert['message']}

*Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """

                await bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode=telegram.ParseMode.MARKDOWN
                )

        except Exception as e:
            self.logger.error(f"Telegram alert error: {str(e)}")

    async def send_slack_alert(self, alert: Dict):
        """Send Slack alert"""
        try:
            slack_config = self.alert_config.get('slack', {})
            webhook_url = slack_config.get('webhook_url')

            if webhook_url:
                color_map = {
                    'low': '#36a64f',
                    'medium': '#ff9f00',
                    'high': '#ff0000'
                }

                payload = {
                    "attachments": [
                        {
                            "color": color_map.get(alert['severity'], '#808080'),
                            "title": f"AstroA Alert: {alert['type'].replace('_', ' ').title()}",
                            "text": alert['message'],
                            "fields": [
                                {
                                    "title": "Severity",
                                    "value": alert['severity'].upper(),
                                    "short": True
                                },
                                {
                                    "title": "Time",
                                    "value": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    "short": True
                                }
                            ]
                        }
                    ]
                }

                requests.post(webhook_url, json=payload)

        except Exception as e:
            self.logger.error(f"Slack alert error: {str(e)}")

    async def send_sms_alert(self, alert: Dict):
        """Send SMS alert (using Twilio or similar service)"""
        try:
            # Implementation depends on SMS provider
            # Example with Twilio:
            sms_config = self.alert_config.get('sms', {})

            if sms_config.get('enabled'):
                # Twilio implementation would go here
                pass

        except Exception as e:
            self.logger.error(f"SMS alert error: {str(e)}")

# Configuration
alert_config = {
    'email_enabled': True,
    'telegram_enabled': True,
    'slack_enabled': False,
    'sms_enabled': False,
    'email': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '<EMAIL>',
        'password': 'your_app_password',
        'from_email': '<EMAIL>',
        'to_email': '<EMAIL>'
    },
    'telegram': {
        'bot_token': 'your_telegram_bot_token',
        'chat_id': 'your_chat_id'
    },
    'slack': {
        'webhook_url': 'your_slack_webhook_url'
    }
}

# Usage
async def main():
    db_config = {
        'host': 'localhost',
        'database': 'mathematical_trading',
        'user': 'trading_user',
        'password': 'hejhej'
    }

    alert_system = AlertSystem(db_config, alert_config)
    await alert_system.run_alert_monitoring()

if __name__ == "__main__":
    asyncio.run(main())</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Alert Configuration</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install alert dependencies
pip install python-telegram-bot
pip install twilio
pip install slack-sdk

# Set up alert configuration
cat > dashboard/alerts/alert_config.json << 'EOF'
{
  "thresholds": {
    "max_drawdown": -10.0,
    "daily_loss": -5.0,
    "position_concentration": 20.0,
    "var_limit": 5.0,
    "consecutive_losses": 5,
    "portfolio_volatility": 15.0
  },
  "notifications": {
    "email": {
      "enabled": true,
      "smtp_server": "smtp.gmail.com",
      "smtp_port": 587,
      "username": "<EMAIL>",
      "password": "your_app_password",
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>"
    },
    "telegram": {
      "enabled": true,
      "bot_token": "your_telegram_bot_token",
      "chat_id": "your_chat_id"
    },
    "slack": {
      "enabled": false,
      "webhook_url": "your_slack_webhook_url"
    }
  }
}
EOF

# Start alert monitoring
python dashboard/alerts/alert_system.py</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 bg-gray-800 text-white py-8">
            <div class="container mx-auto px-6 text-center">
                <p class="text-lg">📊 AstroA Portfolio Monitoring Dashboard Guide</p>
                <p class="mt-2 opacity-75">Real-time performance tracking and risk management</p>
                <div class="mt-4 flex justify-center space-x-6">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="trending-up" class="w-5 h-5"></i>
                        <span>Performance Analytics</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="shield" class="w-5 h-5"></i>
                        <span>Risk Monitoring</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="bell" class="w-5 h-5"></i>
                        <span>Smart Alerts</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Copy to clipboard functionality
        function copyToClipboard(button) {
            const codeBlock = button.parentNode;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(function() {
                button.textContent = 'Copied!';
                setTimeout(function() {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        // Add click event listeners to all copy buttons
        document.addEventListener('DOMContentLoaded', function() {
            const copyButtons = document.querySelectorAll('.copy-button');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    copyToClipboard(this);
                });
            });
        });
    </script>
</body>
</html>