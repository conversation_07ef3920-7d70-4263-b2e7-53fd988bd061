<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Integration Guide - AstroA Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .code-block {
            background-color: #1a1a1a;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
        }
        .step-number {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex items-center space-x-4">
                <i data-lucide="trending-up" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-3xl font-bold">TradingView Integration Guide</h1>
                    <p class="text-blue-100 mt-2">Professional Chart Analysis & Signal Visualization for AstroA</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8">
        <!-- Overview -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i data-lucide="info" class="w-6 h-6 mr-2 text-blue-600"></i>
                Integration Overview
            </h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold mb-2">What You'll Get:</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Professional charting interface</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Real-time signal visualization</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Custom indicator overlays</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Trade execution alerts</li>
                        <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-500"></i>Portfolio performance tracking</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">Integration Methods:</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center"><i data-lucide="monitor" class="w-4 h-4 mr-2 text-blue-500"></i>TradingView Webhooks</li>
                        <li class="flex items-center"><i data-lucide="code" class="w-4 h-4 mr-2 text-blue-500"></i>Pine Script Strategies</li>
                        <li class="flex items-center"><i data-lucide="database" class="w-4 h-4 mr-2 text-blue-500"></i>Data Sync API</li>
                        <li class="flex items-center"><i data-lucide="bell" class="w-4 h-4 mr-2 text-blue-500"></i>Alert Management</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Step 1: TradingView Account Setup -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">1</div>
                <h2 class="text-xl font-bold">TradingView Account & Webhook Setup</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-2">Account Requirements:</h3>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <p class="text-blue-800"><strong>Pro+ Subscription Required:</strong> Webhooks are only available with TradingView Pro, Pro+, or Premium plans ($14.95/month minimum)</p>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Create Webhook Endpoint in AstroA:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create webhook handler for TradingView
mkdir -p integrations/tradingview
cd integrations/tradingview

# Create webhook server
cat > webhook_server.py << 'EOF'
from flask import Flask, request, jsonify
import json
import logging
from datetime import datetime
import os
import sys

# Add project root to path
sys.path.append('/home/<USER>/axmadcodes/AstroA')

from agents.portfolio_manager.portfolio_management_agent import PortfolioManagementAgent
from agents.risk_manager.risk_management_agent import RiskManagementAgent

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class TradingViewWebhook:
    def __init__(self):
        self.portfolio_agent = PortfolioManagementAgent()
        self.risk_agent = RiskManagementAgent()
        self.webhook_secret = os.getenv('TRADINGVIEW_WEBHOOK_SECRET', 'your_secret_key_here')

    def validate_webhook(self, data):
        """Validate incoming webhook data"""
        required_fields = ['symbol', 'action', 'strategy', 'price']
        return all(field in data for field in required_fields)

    def process_signal(self, signal_data):
        """Process trading signal from TradingView"""
        try:
            symbol = signal_data['symbol']
            action = signal_data['action']  # buy/sell/close
            strategy = signal_data['strategy']
            price = float(signal_data['price'])
            quantity = signal_data.get('quantity', 1.0)

            # Log the signal
            logging.info(f"TradingView Signal: {action} {quantity} {symbol} at {price}")

            # Risk assessment
            risk_analysis = self.risk_agent.assess_trade_risk({
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': price,
                'strategy_source': 'tradingview'
            })

            if not risk_analysis['approved']:
                logging.warning(f"Trade rejected by risk management: {risk_analysis['reason']}")
                return {'status': 'rejected', 'reason': risk_analysis['reason']}

            # Execute trade through portfolio manager
            trade_result = self.portfolio_agent.execute_trade({
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': price,
                'strategy': strategy,
                'source': 'tradingview_webhook'
            })

            return {'status': 'success', 'trade_id': trade_result.get('trade_id')}

        except Exception as e:
            logging.error(f"Error processing TradingView signal: {str(e)}")
            return {'status': 'error', 'message': str(e)}

webhook_handler = TradingViewWebhook()

@app.route('/webhook/tradingview', methods=['POST'])
def handle_tradingview_webhook():
    try:
        # Verify content type
        if not request.is_json:
            return jsonify({'error': 'Content-Type must be application/json'}), 400

        data = request.get_json()

        # Validate webhook secret if provided
        webhook_secret = request.headers.get('X-Webhook-Secret')
        if webhook_secret != webhook_handler.webhook_secret:
            logging.warning("Invalid webhook secret")
            return jsonify({'error': 'Unauthorized'}), 401

        # Validate required fields
        if not webhook_handler.validate_webhook(data):
            return jsonify({'error': 'Missing required fields'}), 400

        # Process the signal
        result = webhook_handler.process_signal(data)

        return jsonify(result)

    except Exception as e:
        logging.error(f"Webhook error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/webhook/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    # Run on port 8080 for webhook access
    app.run(host='0.0.0.0', port=8080, debug=False)
EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Install Dependencies & Start Webhook Server:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Install Flask for webhook handling
pip install flask

# Set webhook secret (replace with your own secure key)
export TRADINGVIEW_WEBHOOK_SECRET="astro_tradingview_2024_secure"

# Start the webhook server
python webhook_server.py

# Test webhook endpoint
curl -X GET http://localhost:8080/webhook/health</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Pine Script Strategy -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">2</div>
                <h2 class="text-xl font-bold">Create Pine Script Strategy</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">AstroA Mathematical Strategy in Pine Script:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code>//@version=5
strategy("AstroA Mathematical Strategy", overlay=true, initial_capital=10000)

// Input parameters
lookback_period = input.int(20, "Lookback Period", minval=5)
rsi_period = input.int(14, "RSI Period", minval=5)
ema_fast = input.int(12, "Fast EMA", minval=5)
ema_slow = input.int(26, "Slow EMA", minval=10)
webhook_url = input.string("http://your-server.com:8080/webhook/tradingview", "Webhook URL")

// Mathematical indicators (matching your AstroA system)
price = close
ema_fast_line = ta.ema(price, ema_fast)
ema_slow_line = ta.ema(price, ema_slow)
rsi = ta.rsi(price, rsi_period)

// Advanced mathematical signals
price_momentum = (price - price[lookback_period]) / price[lookback_period] * 100
volatility = ta.stdev(ta.change(price), lookback_period)
volume_profile = volume / ta.sma(volume, lookback_period)

// AstroA Mean Reversion Signal
mean_price = ta.sma(price, lookback_period)
price_deviation = (price - mean_price) / mean_price
mean_reversion_signal = price_deviation < -0.02 and rsi < 30

// AstroA Momentum Signal
momentum_signal = ema_fast_line > ema_slow_line and price_momentum > 2 and volume_profile > 1.2

// Combined AstroA Signal
astro_buy_signal = mean_reversion_signal or momentum_signal
astro_sell_signal = price_deviation > 0.02 and rsi > 70

// Plot indicators
plot(ema_fast_line, color=color.blue, title="Fast EMA")
plot(ema_slow_line, color=color.red, title="Slow EMA")
plot(mean_price, color=color.yellow, title="Mean Price")

// Plot signals
plotshape(astro_buy_signal, style=shape.triangleup, location=location.belowbar,
         color=color.green, size=size.small, title="AstroA Buy")
plotshape(astro_sell_signal, style=shape.triangledown, location=location.abovebar,
         color=color.red, size=size.small, title="AstroA Sell")

// Strategy execution
if astro_buy_signal
    strategy.entry("AstroA_Long", strategy.long,
                  alert_message='{"symbol":"' + syminfo.ticker + '","action":"buy","strategy":"astro_mathematical","price":' + str.tostring(close) + ',"quantity":1}')

if astro_sell_signal
    strategy.close("AstroA_Long",
                  alert_message='{"symbol":"' + syminfo.ticker + '","action":"sell","strategy":"astro_mathematical","price":' + str.tostring(close) + ',"quantity":1}')

// Alert conditions for webhook
alertcondition(astro_buy_signal, title="AstroA Buy Signal",
              message='{"symbol":"{{ticker}}","action":"buy","strategy":"astro_mathematical","price":{{close}},"quantity":1}')

alertcondition(astro_sell_signal, title="AstroA Sell Signal",
              message='{"symbol":"{{ticker}}","action":"sell","strategy":"astro_mathematical","price":{{close}},"quantity":1}')</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Deploy Pine Script Strategy:</h3>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Open TradingView and go to Pine Editor</li>
                        <li>Copy and paste the Pine Script code above</li>
                        <li>Click "Save" and name it "AstroA Mathematical Strategy"</li>
                        <li>Click "Add to Chart" to deploy on any symbol</li>
                        <li>Set up alerts for the buy/sell conditions</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Step 3: Alert Configuration -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">3</div>
                <h2 class="text-xl font-bold">Configure TradingView Alerts</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Set Up Webhook Alerts:</h3>
                    <ol class="list-decimal list-inside space-y-3 text-gray-700">
                        <li><strong>Create Alert:</strong> Right-click on chart → "Add Alert"</li>
                        <li><strong>Condition:</strong> Select your "AstroA Mathematical Strategy"</li>
                        <li><strong>Options:</strong> Choose "AstroA Buy Signal" or "AstroA Sell Signal"</li>
                        <li><strong>Alert Actions:</strong> Check "Webhook URL"</li>
                        <li><strong>Webhook URL:</strong> Enter your server URL: <code>http://your-server-ip:8080/webhook/tradingview</code></li>
                        <li><strong>Message:</strong> Use the JSON format from Pine Script</li>
                    </ol>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Alert Message Format:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code>{
  "symbol": "{{ticker}}",
  "action": "buy",
  "strategy": "astro_mathematical",
  "price": {{close}},
  "quantity": 1,
  "timestamp": "{{time}}",
  "timeframe": "{{interval}}"
}</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Test Alert Configuration:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Test webhook manually
curl -X POST http://localhost:8080/webhook/tradingview \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Secret: astro_tradingview_2024_secure" \
  -d '{
    "symbol": "AAPL",
    "action": "buy",
    "strategy": "astro_mathematical",
    "price": 150.25,
    "quantity": 1
  }'</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Signal Processing Integration -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">4</div>
                <h2 class="text-xl font-bold">Integrate with AstroA Signal Processing</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create TradingView Signal Adapter:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create signal adapter
cat > tradingview_adapter.py << 'EOF'
import sys
import json
from datetime import datetime
sys.path.append('/home/<USER>/axmadcodes/AstroA')

from agents.signal_generator.signal_generation_agent import SignalGenerationAgent
from agents.market_analyzer.market_analysis_agent import MarketAnalysisAgent

class TradingViewSignalAdapter:
    def __init__(self):
        self.signal_agent = SignalGenerationAgent()
        self.market_agent = MarketAnalysisAgent()

    def convert_tradingview_signal(self, tv_signal):
        """Convert TradingView signal to AstroA format"""

        # Get current market analysis for the symbol
        market_data = self.market_agent.get_market_data(tv_signal['symbol'])

        # Convert to AstroA signal format
        astro_signal = {
            'timestamp': datetime.now().isoformat(),
            'symbol': tv_signal['symbol'],
            'signal_type': 'external_tradingview',
            'action': tv_signal['action'],
            'confidence': self.calculate_confidence(tv_signal, market_data),
            'price_target': tv_signal['price'],
            'quantity': tv_signal['quantity'],
            'strategy_source': tv_signal['strategy'],
            'timeframe': tv_signal.get('timeframe', '1h'),
            'metadata': {
                'source': 'tradingview_webhook',
                'original_signal': tv_signal,
                'market_conditions': market_data
            }
        }

        return astro_signal

    def calculate_confidence(self, tv_signal, market_data):
        """Calculate confidence score for TradingView signal"""
        base_confidence = 0.7  # Base confidence for TradingView signals

        # Adjust based on market conditions
        if market_data.get('volatility', 0) > 0.02:
            base_confidence -= 0.1  # Reduce confidence in high volatility

        if market_data.get('volume_ratio', 1) > 1.5:
            base_confidence += 0.1  # Increase confidence with high volume

        return max(0.3, min(0.95, base_confidence))

    def validate_signal(self, astro_signal):
        """Validate converted signal meets AstroA standards"""
        return self.signal_agent.validate_signal(astro_signal)

    def process_signal(self, tv_signal):
        """Full signal processing pipeline"""
        try:
            # Convert to AstroA format
            astro_signal = self.convert_tradingview_signal(tv_signal)

            # Validate signal
            if not self.validate_signal(astro_signal):
                return {'status': 'invalid', 'reason': 'Signal validation failed'}

            # Send to AstroA signal processing
            result = self.signal_agent.process_external_signal(astro_signal)

            return {'status': 'processed', 'signal_id': result.get('signal_id')}

        except Exception as e:
            return {'status': 'error', 'message': str(e)}

EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Update Webhook Handler to Use Adapter:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Add to webhook_server.py (replace the process_signal method)
from tradingview_adapter import TradingViewSignalAdapter

class TradingViewWebhook:
    def __init__(self):
        self.portfolio_agent = PortfolioManagementAgent()
        self.risk_agent = RiskManagementAgent()
        self.signal_adapter = TradingViewSignalAdapter()  # Add this line
        self.webhook_secret = os.getenv('TRADINGVIEW_WEBHOOK_SECRET', 'your_secret_key_here')

    def process_signal(self, signal_data):
        """Process trading signal from TradingView with full AstroA integration"""
        try:
            # Convert TradingView signal to AstroA format
            adapter_result = self.signal_adapter.process_signal(signal_data)

            if adapter_result['status'] != 'processed':
                return adapter_result

            # Continue with existing risk assessment and execution...
            symbol = signal_data['symbol']
            action = signal_data['action']
            price = float(signal_data['price'])
            quantity = signal_data.get('quantity', 1.0)

            # Enhanced risk assessment with TradingView context
            risk_analysis = self.risk_agent.assess_trade_risk({
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': price,
                'strategy_source': 'tradingview',
                'signal_id': adapter_result['signal_id']
            })

            if not risk_analysis['approved']:
                return {'status': 'rejected', 'reason': risk_analysis['reason']}

            # Execute trade
            trade_result = self.portfolio_agent.execute_trade({
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': price,
                'strategy': 'tradingview_' + signal_data['strategy'],
                'source': 'tradingview_webhook',
                'signal_id': adapter_result['signal_id']
            })

            return {'status': 'success', 'trade_id': trade_result.get('trade_id')}

        except Exception as e:
            logging.error(f"Error processing TradingView signal: {str(e)}")
            return {'status': 'error', 'message': str(e)}</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 5: Dashboard Integration -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">5</div>
                <h2 class="text-xl font-bold">Dashboard & Monitoring Integration</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create TradingView Monitoring Dashboard:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create monitoring dashboard
cat > tradingview_monitor.py << 'EOF'
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys

sys.path.append('/home/<USER>/axmadcodes/AstroA')
from database.database_manager import DatabaseManager

class TradingViewMonitor:
    def __init__(self):
        self.db = DatabaseManager()

    def get_tradingview_signals(self, days_back=7):
        """Get TradingView signals from database"""
        query = """
        SELECT timestamp, symbol, action, price, strategy, confidence
        FROM trading_signals
        WHERE strategy_source LIKE 'tradingview%'
        AND timestamp >= %s
        ORDER BY timestamp DESC
        """

        start_date = datetime.now() - timedelta(days=days_back)
        return pd.read_sql(query, self.db.connection, params=[start_date])

    def get_webhook_stats(self):
        """Get webhook performance statistics"""
        query = """
        SELECT
            DATE(timestamp) as date,
            COUNT(*) as total_signals,
            SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as errors
        FROM webhook_logs
        WHERE source = 'tradingview'
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
        LIMIT 30
        """

        return pd.read_sql(query, self.db.connection)

def main():
    st.set_page_config(page_title="TradingView Integration Monitor", layout="wide")

    st.title("🔗 TradingView Integration Monitor")
    st.markdown("Real-time monitoring of TradingView signals and webhook performance")

    monitor = TradingViewMonitor()

    # Sidebar controls
    st.sidebar.header("Filters")
    days_back = st.sidebar.slider("Days to show", 1, 30, 7)

    # Main content
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Recent TradingView Signals")
        signals_df = monitor.get_tradingview_signals(days_back)

        if not signals_df.empty:
            st.dataframe(signals_df, use_container_width=True)

            # Signal distribution
            fig = go.Figure(data=[
                go.Bar(x=signals_df['symbol'].value_counts().index,
                      y=signals_df['symbol'].value_counts().values)
            ])
            fig.update_layout(title="Signals by Symbol")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No TradingView signals found for the selected period")

    with col2:
        st.subheader("🔄 Webhook Performance")
        webhook_stats = monitor.get_webhook_stats()

        if not webhook_stats.empty:
            # Performance metrics
            total_signals = webhook_stats['total_signals'].sum()
            success_rate = (webhook_stats['successful'].sum() / total_signals * 100) if total_signals > 0 else 0

            col2a, col2b, col2c = st.columns(3)
            col2a.metric("Total Signals", total_signals)
            col2b.metric("Success Rate", f"{success_rate:.1f}%")
            col2c.metric("Avg Daily", int(webhook_stats['total_signals'].mean()))

            # Performance chart
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=webhook_stats['date'], y=webhook_stats['successful'],
                                   mode='lines+markers', name='Successful'))
            fig.add_trace(go.Scatter(x=webhook_stats['date'], y=webhook_stats['rejected'],
                                   mode='lines+markers', name='Rejected'))
            fig.add_trace(go.Scatter(x=webhook_stats['date'], y=webhook_stats['errors'],
                                   mode='lines+markers', name='Errors'))
            fig.update_layout(title="Daily Webhook Performance")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No webhook statistics available")

    # Real-time status
    st.subheader("🟢 Live Status")
    col3, col4, col5 = st.columns(3)

    with col3:
        st.metric("Webhook Server", "🟢 Online")
    with col4:
        st.metric("TradingView Connection", "🟢 Active")
    with col5:
        st.metric("Signal Processing", "🟢 Running")

if __name__ == "__main__":
    main()
EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Start Monitoring Dashboard:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Install streamlit and plotly
pip install streamlit plotly

# Start the monitoring dashboard
streamlit run tradingview_monitor.py --server.port 8501

# Access dashboard at: http://localhost:8501</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 6: Production Deployment -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center mb-4">
                <div class="step-number mr-4">6</div>
                <h2 class="text-xl font-bold">Production Deployment</h2>
            </div>

            <div class="space-y-6">
                <div>
                    <h3 class="font-semibold mb-3">Create Production Docker Setup:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Create Dockerfile for TradingView integration
cat > Dockerfile.tradingview << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application files
COPY integrations/tradingview/ .
COPY agents/ /app/agents/
COPY database/ /app/database/

# Expose webhook port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/webhook/health || exit 1

# Start webhook server
CMD ["python", "webhook_server.py"]
EOF

# Create docker-compose for full integration
cat > docker-compose.tradingview.yml << 'EOF'
version: '3.8'

services:
  tradingview-webhook:
    build:
      context: .
      dockerfile: Dockerfile.tradingview
    ports:
      - "8080:8080"
    environment:
      - TRADINGVIEW_WEBHOOK_SECRET=${TRADINGVIEW_WEBHOOK_SECRET}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - astro-network

  tradingview-monitor:
    build:
      context: .
      dockerfile: Dockerfile.tradingview
    ports:
      - "8501:8501"
    command: ["streamlit", "run", "tradingview_monitor.py", "--server.port=8501", "--server.address=0.0.0.0"]
    environment:
      - DATABASE_URL=${DATABASE_URL}
    restart: unless-stopped
    networks:
      - astro-network

networks:
  astro-network:
    external: true
EOF</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Deploy with SSL/HTTPS:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Install and configure Nginx for SSL termination
sudo apt install nginx certbot python3-certbot-nginx

# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/tradingview-webhook << 'EOF'
server {
    listen 80;
    server_name your-domain.com;

    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    location / {
        return 301 https://$server_name$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location /webhook/tradingview {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /monitor {
        proxy_pass http://localhost:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF

# Enable site and get SSL certificate
sudo ln -s /etc/nginx/sites-available/tradingview-webhook /etc/nginx/sites-enabled/
sudo certbot --nginx -d your-domain.com
sudo systemctl reload nginx</code></pre>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-3">Start Production Services:</h3>
                    <div class="code-block">
                        <pre class="text-green-400"><code># Set environment variables
export TRADINGVIEW_WEBHOOK_SECRET="astro_tradingview_2024_secure"
export DATABASE_URL="postgresql://trading_user:hejhej@localhost/mathematical_trading"

# Start services with docker-compose
docker-compose -f docker-compose.tradingview.yml up -d

# Verify services are running
docker-compose -f docker-compose.tradingview.yml ps

# Check logs
docker-compose -f docker-compose.tradingview.yml logs -f tradingview-webhook</code></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing & Validation -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i data-lucide="check-circle" class="w-6 h-6 mr-2 text-green-600"></i>
                Testing & Validation
            </h2>

            <div class="space-y-4">
                <div>
                    <h3 class="font-semibold mb-2">✅ Integration Checklist:</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Webhook server running on port 8080
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Pine Script strategy deployed
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> TradingView alerts configured
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> SSL certificate installed
                            </label>
                        </div>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Signal adapter functioning
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Risk management integrated
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Monitoring dashboard accessible
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2"> Test trades executed successfully
                            </label>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-2">🔍 Performance Validation:</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <ul class="space-y-1 text-sm text-gray-600">
                            <li>• <strong>Signal Latency:</strong> &lt; 2 seconds from TradingView to execution</li>
                            <li>• <strong>Success Rate:</strong> &gt; 95% webhook delivery</li>
                            <li>• <strong>Risk Integration:</strong> All signals pass through AstroA risk management</li>
                            <li>• <strong>Data Sync:</strong> Real-time synchronization with portfolio manager</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support & Troubleshooting -->
        <div class="bg-blue-50 rounded-lg p-6">
            <h2 class="text-xl font-bold mb-4 flex items-center text-blue-800">
                <i data-lucide="help-circle" class="w-6 h-6 mr-2"></i>
                Support & Troubleshooting
            </h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold mb-2 text-blue-800">Common Issues:</h3>
                    <ul class="space-y-1 text-blue-700 text-sm">
                        <li>• Webhook timeouts → Check server capacity</li>
                        <li>• Signal validation errors → Verify JSON format</li>
                        <li>• SSL certificate issues → Update certbot</li>
                        <li>• TradingView subscription needed for webhooks</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-2 text-blue-800">Log Locations:</h3>
                    <ul class="space-y-1 text-blue-700 text-sm">
                        <li>• Webhook logs: <code>/app/logs/webhook.log</code></li>
                        <li>• Signal processing: <code>/app/logs/signals.log</code></li>
                        <li>• Nginx access: <code>/var/log/nginx/access.log</code></li>
                        <li>• Docker logs: <code>docker-compose logs</code></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();
    </script>
</body>
</html>