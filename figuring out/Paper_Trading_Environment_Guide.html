<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paper Trading Environment Guide - AstroA Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .paper-bg {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        }
        .step-card {
            border-left: 4px solid #10b981;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #10b981;
        }
        .env-indicator {
            position: absolute;
            top: 5px;
            right: 60px;
            background: rgba(16, 185, 129, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #10b981;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .copy-button:hover {
            background: rgba(16, 185, 129, 0.3);
        }
        .warning-box {
            background: linear-gradient(45deg, #fef3c7, #fde68a);
            border: 2px solid #f59e0b;
            color: #92400e;
        }
        .success-box {
            background: linear-gradient(45deg, #d1fae5, #a7f3d0);
            border: 2px solid #059669;
            color: #065f46;
        }
        .info-box {
            background: linear-gradient(45deg, #dbeafe, #bfdbfe);
            border: 2px solid #2563eb;
            color: #1e40af;
        }
        .code-file {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            margin: 16px 0;
        }
        .file-header {
            background: #1e293b;
            padding: 8px 16px;
            border-bottom: 1px solid #334155;
            font-family: 'Ubuntu Mono', monospace;
            color: #94a3b8;
            font-size: 14px;
        }
        .file-content {
            padding: 16px;
            color: #e2e8f0;
            font-family: 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .nav-tabs {
            display: flex;
            background: #1e293b;
            border-radius: 8px 8px 0 0;
        }
        .nav-tab {
            padding: 12px 24px;
            cursor: pointer;
            background: #334155;
            color: #94a3b8;
            border-right: 1px solid #475569;
            transition: all 0.3s ease;
        }
        .nav-tab:first-child {
            border-radius: 8px 0 0 0;
        }
        .nav-tab:last-child {
            border-right: none;
            border-radius: 0 8px 0 0;
        }
        .nav-tab.active {
            background: #10b981;
            color: white;
        }
        .nav-tab:hover {
            background: #475569;
        }
        .tab-content {
            display: none;
            background: #0f172a;
            padding: 24px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #334155;
            border-top: none;
        }
        .tab-content.active {
            display: block;
        }
        .provider-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .provider-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .provider-card:hover {
            border-color: #10b981;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
        }
        .provider-card.recommended {
            border-color: #10b981;
            background: linear-gradient(45deg, #f0fdf4, #dcfce7);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="paper-bg text-white py-8">
        <div class="container mx-auto px-6">
            <div class="flex items-center space-x-4">
                <i data-lucide="play-circle" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-4xl font-bold">Paper Trading Environment Guide</h1>
                    <p class="text-xl opacity-90">Validate Your AstroA Strategies with Real Market Data</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8">
        <!-- Navigation -->
        <div class="mb-8">
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showTab('overview')">
                    <i data-lucide="info" class="w-4 h-4 inline mr-2"></i>Overview
                </div>
                <div class="nav-tab" onclick="showTab('providers')">
                    <i data-lucide="server" class="w-4 h-4 inline mr-2"></i>Data Providers
                </div>
                <div class="nav-tab" onclick="showTab('setup')">
                    <i data-lucide="settings" class="w-4 h-4 inline mr-2"></i>Setup & Configuration
                </div>
                <div class="nav-tab" onclick="showTab('integration')">
                    <i data-lucide="git-merge" class="w-4 h-4 inline mr-2"></i>AstroA Integration
                </div>
                <div class="nav-tab" onclick="showTab('deployment')">
                    <i data-lucide="rocket" class="w-4 h-4 inline mr-2"></i>Deployment
                </div>
                <div class="nav-tab" onclick="showTab('monitoring')">
                    <i data-lucide="activity" class="w-4 h-4 inline mr-2"></i>Monitoring
                </div>
            </div>

            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🎯 Paper Trading Overview</h2>

                <div class="success-box p-6 rounded-lg mb-6">
                    <div class="flex items-center mb-3">
                        <i data-lucide="target" class="w-6 h-6 mr-3"></i>
                        <h3 class="text-xl font-bold">📊 Why Paper Trading is Critical</h3>
                    </div>
                    <ul class="space-y-2">
                        <li>🔬 Test strategies with real market data without financial risk</li>
                        <li>📈 Validate AstroA system performance over 60+ days</li>
                        <li>⚖️ Fine-tune risk management parameters</li>
                        <li>🧠 Build confidence before live trading</li>
                        <li>📊 Identify strategy weaknesses in different market conditions</li>
                    </ul>
                </div>

                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="shield" class="w-6 h-6 inline mr-2 text-green-600"></i>
                            Paper Trading Benefits
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>🚫 Zero financial risk</li>
                            <li>📊 Real market conditions</li>
                            <li>⚡ Real-time execution simulation</li>
                            <li>📈 Performance tracking</li>
                            <li>🔧 Strategy optimization</li>
                            <li>🎯 Confidence building</li>
                        </ul>
                    </div>

                    <div class="step-card bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i data-lucide="target" class="w-6 h-6 inline mr-2 text-blue-600"></i>
                            Success Criteria
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>✅ 60+ days consistent profitability</li>
                            <li>📉 Maximum drawdown < 10%</li>
                            <li>📊 Sharpe ratio > 1.0</li>
                            <li>🎯 Win rate > 50%</li>
                            <li>⚖️ Risk metrics stable</li>
                            <li>🔄 Strategy robustness proven</li>
                        </ul>
                    </div>
                </div>

                <div class="info-box p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="calendar" class="w-6 h-6 inline mr-2"></i>
                        Paper Trading Timeline
                    </h3>
                    <div class="grid md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">1</div>
                            <p class="font-semibold">Week 1</p>
                            <p class="text-sm">Setup & Configuration</p>
                        </div>
                        <div class="text-center">
                            <div class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">2</div>
                            <p class="font-semibold">Weeks 2-9</p>
                            <p class="text-sm">Active Paper Trading</p>
                        </div>
                        <div class="text-center">
                            <div class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">3</div>
                            <p class="font-semibold">Week 10</p>
                            <p class="text-sm">Performance Analysis</p>
                        </div>
                        <div class="text-center">
                            <div class="bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mx-auto mb-2">4</div>
                            <p class="font-semibold">Week 11+</p>
                            <p class="text-sm">Live Trading Prep</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Providers Tab -->
            <div id="providers" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🌐 Data Provider Options</h2>

                <div class="provider-grid">
                    <!-- Alpaca -->
                    <div class="provider-card recommended">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">A</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Alpaca Markets</h3>
                                <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">🏆 RECOMMENDED</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Free paper trading API</li>
                                    <li>• Real-time market data</li>
                                    <li>• Easy Python integration</li>
                                    <li>• Reliable infrastructure</li>
                                    <li>• US stocks & crypto support</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• US-focused (good for testing)</li>
                                    <li>• Limited international assets</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-green-700">💰 Cost: FREE for paper trading</p>
                                <p class="text-sm text-gray-600">🔗 alpaca.markets</p>
                            </div>
                        </div>
                    </div>

                    <!-- IEX Cloud -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">I</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">IEX Cloud</h3>
                                <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">RELIABLE</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• High-quality US market data</li>
                                    <li>• Good free tier</li>
                                    <li>• Historical data access</li>
                                    <li>• RESTful API</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• US stocks only</li>
                                    <li>• No crypto data</li>
                                    <li>• Rate limits on free tier</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-blue-700">💰 Cost: Free tier available</p>
                                <p class="text-sm text-gray-600">🔗 iexcloud.io</p>
                            </div>
                        </div>
                    </div>

                    <!-- Polygon.io -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">P</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Polygon.io</h3>
                                <span class="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded">PREMIUM</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• High-quality real-time data</li>
                                    <li>• Stocks, options, forex, crypto</li>
                                    <li>• Low latency</li>
                                    <li>• Great for serious trading</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Paid service ($99+/month)</li>
                                    <li>• Overkill for paper trading</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-purple-700">💰 Cost: $99+/month</p>
                                <p class="text-sm text-gray-600">🔗 polygon.io</p>
                            </div>
                        </div>
                    </div>

                    <!-- TD Ameritrade -->
                    <div class="provider-card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">T</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">TD Ameritrade</h3>
                                <span class="text-sm bg-red-100 text-red-800 px-2 py-1 rounded">LEGACY</span>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div>
                                <p class="font-semibold text-gray-700">✅ Pros:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Free paper trading</li>
                                    <li>• Real brokerage data</li>
                                    <li>• Good for US markets</li>
                                </ul>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-700">⚠️ Cons:</p>
                                <ul class="text-sm text-gray-600 ml-4">
                                    <li>• Being acquired by Schwab</li>
                                    <li>• API access uncertain</li>
                                    <li>• Complex setup process</li>
                                </ul>
                            </div>
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <p class="text-sm font-medium text-red-700">💰 Cost: FREE</p>
                                <p class="text-sm text-gray-600">🔗 developer.tdameritrade.com</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="warning-box p-6 rounded-lg mt-8">
                    <h3 class="text-xl font-bold mb-3">
                        <i data-lucide="lightbulb" class="w-6 h-6 inline mr-2"></i>
                        🎯 Recommendation for AstroA
                    </h3>
                    <p class="mb-3">
                        <strong>Start with Alpaca Markets</strong> for paper trading. It provides:
                    </p>
                    <ul class="space-y-1">
                        <li>✅ Free paper trading environment</li>
                        <li>✅ Real-time data that matches your crypto focus</li>
                        <li>✅ Simple Python integration</li>
                        <li>✅ Path to live trading when ready</li>
                    </ul>
                </div>
            </div>

            <!-- Setup Tab -->
            <div id="setup" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">⚙️ Setup & Configuration</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 1: Alpaca Account Setup</h3>
                    <ol class="space-y-3 text-gray-600">
                        <li>1. Visit <a href="https://alpaca.markets" class="text-blue-600 underline">alpaca.markets</a></li>
                        <li>2. Create a free paper trading account</li>
                        <li>3. Navigate to Dashboard → API Keys</li>
                        <li>4. Generate API keys for paper trading</li>
                        <li>5. Note your API Key and Secret Key</li>
                    </ol>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 2: Install Dependencies</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Navigate to AstroA directory
cd /home/<USER>/axmadcodes/AstroA

# Activate virtual environment
source venv/bin/activate

# Install paper trading dependencies
pip install alpaca-trade-api
pip install alpaca-py
pip install yfinance
pip install pandas-ta
pip install schedule

# Verify installations
python -c "import alpaca_trade_api; print('Alpaca API installed successfully')"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 3: Environment Configuration</h3>

                    <div class="code-file">
                        <div class="file-header">.env (Add to existing)</div>
                        <div class="file-content"># Paper Trading Configuration
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Paper Trading Settings
PAPER_TRADING_ENABLED=true
PAPER_TRADING_INITIAL_CASH=100000
PAPER_TRADING_MAX_POSITIONS=10
PAPER_TRADING_RISK_LIMIT=0.02

# Data Feed Settings
REAL_TIME_DATA_ENABLED=true
DATA_FEED_PROVIDER=alpaca
MARKET_DATA_DELAY=0</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 4: Create Paper Trading Configuration</h3>

                    <div class="code-file">
                        <div class="file-header">config/paper_trading_config.py</div>
                        <div class="file-content">"""
Paper Trading Configuration for AstroA System
"""
import os
from typing import Dict, List
from dataclasses import dataclass

@dataclass
class PaperTradingConfig:
    """Configuration for paper trading environment"""

    # Alpaca API Configuration
    api_key: str = os.getenv('ALPACA_API_KEY', '')
    secret_key: str = os.getenv('ALPACA_SECRET_KEY', '')
    base_url: str = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')

    # Trading Parameters
    initial_cash: float = float(os.getenv('PAPER_TRADING_INITIAL_CASH', 100000))
    max_positions: int = int(os.getenv('PAPER_TRADING_MAX_POSITIONS', 10))
    risk_limit: float = float(os.getenv('PAPER_TRADING_RISK_LIMIT', 0.02))

    # Symbols to trade (matching AstroA crypto focus)
    tradable_symbols: List[str] = [
        'BTCUSD',   # Bitcoin
        'ETHUSD',   # Ethereum
        'ADAUSD',   # Cardano
        'SOLUSD',   # Solana
        'DOTUSD',   # Polkadot
        'LINKUSD',  # Chainlink
        'AVAXUSD',  # Avalanche
        'MATICUSD'  # Polygon
    ]

    # Strategy Configuration
    strategies_enabled: List[str] = [
        'mean_reversion',
        'momentum'
    ]

    # Risk Management
    max_position_size: float = 0.15  # 15% of portfolio per position
    stop_loss_percentage: float = 0.02  # 2% stop loss
    take_profit_percentage: float = 0.04  # 4% take profit
    daily_loss_limit: float = 0.05  # 5% daily loss limit

    # Data Feed Configuration
    real_time_enabled: bool = os.getenv('REAL_TIME_DATA_ENABLED', 'true').lower() == 'true'
    data_update_interval: int = 60  # seconds

    # Logging
    log_trades: bool = True
    log_signals: bool = True
    log_performance: bool = True

    def validate(self) -> bool:
        """Validate configuration"""
        required_fields = [self.api_key, self.secret_key]

        if not all(required_fields):
            raise ValueError("Missing required Alpaca API credentials")

        if self.initial_cash <= 0:
            raise ValueError("Initial cash must be positive")

        if not (0 < self.max_position_size <= 1):
            raise ValueError("Max position size must be between 0 and 1")

        return True

# Global configuration instance
paper_config = PaperTradingConfig()</div>
                    </div>
                </div>
            </div>

            <!-- Integration Tab -->
            <div id="integration" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🔗 AstroA Integration</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Paper Trading Engine</h3>

                    <div class="code-file">
                        <div class="file-header">agents/paper_trading/paper_trading_engine.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
AstroA Paper Trading Engine
Integrates with live market data for realistic strategy validation
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import TimeFrame
import pandas as pd
import json

from config.paper_trading_config import paper_config
from agents.trading_strategy.trading_strategy_agent import TradingStrategyAgent
from shared.types.strategy_types import TradingSignal, Position, Order

class PaperTradingEngine:
    """
    Paper trading engine that connects AstroA strategies to real market data
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Validate configuration
        paper_config.validate()

        # Initialize Alpaca API
        self.api = tradeapi.REST(
            paper_config.api_key,
            paper_config.secret_key,
            paper_config.base_url,
            api_version='v2'
        )

        # Initialize AstroA trading strategy agent
        self.strategy_agent = TradingStrategyAgent()

        # Paper trading state
        self.is_running = False
        self.positions = {}
        self.portfolio_value = paper_config.initial_cash
        self.trade_history = []
        self.performance_metrics = {}

        self.logger.info("Paper Trading Engine initialized")

    async def start_paper_trading(self):
        """Start the paper trading session"""
        self.logger.info("Starting paper trading session...")

        try:
            # Verify account status
            account = self.api.get_account()
            self.logger.info(f"Account status: {account.status}")
            self.logger.info(f"Portfolio value: ${float(account.portfolio_value):,.2f}")

            # Start trading loop
            self.is_running = True
            await self._trading_loop()

        except Exception as e:
            self.logger.error(f"Failed to start paper trading: {str(e)}")
            raise

    async def stop_paper_trading(self):
        """Stop the paper trading session"""
        self.logger.info("Stopping paper trading session...")
        self.is_running = False

        # Close all positions
        await self._close_all_positions()

        # Generate final report
        self._generate_session_report()

    async def _trading_loop(self):
        """Main trading loop"""
        while self.is_running:
            try:
                # Get latest market data
                market_data = await self._get_market_data()

                # Process data through AstroA strategy agent
                strategy_result = await self.strategy_agent.execute(
                    market_data=market_data,
                    portfolio_state=self._get_portfolio_state()
                )

                if strategy_result.status == 'success':
                    # Execute trading signals
                    signals = strategy_result.data.get('signals', [])
                    await self._process_trading_signals(signals)

                # Update performance metrics
                self._update_performance_metrics()

                # Wait for next iteration
                await asyncio.sleep(paper_config.data_update_interval)

            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                await asyncio.sleep(5)  # Short pause before retry

    async def _get_market_data(self) -> Dict:
        """Get real-time market data from Alpaca"""
        market_data = {}

        try:
            for symbol in paper_config.tradable_symbols:
                # Get latest bars
                bars = self.api.get_bars(
                    symbol,
                    TimeFrame.Hour,
                    limit=100,
                    adjustment='raw'
                ).df

                # Get latest quote
                latest_quote = self.api.get_latest_quote(symbol)

                market_data[symbol] = {
                    'ohlcv': bars,
                    'current_price': latest_quote.bidprice,
                    'timestamp': datetime.now()
                }

        except Exception as e:
            self.logger.error(f"Error getting market data: {str(e)}")

        return market_data

    async def _process_trading_signals(self, signals: List[TradingSignal]):
        """Process and execute trading signals"""
        for signal in signals:
            try:
                if signal.signal_type in ['BUY', 'STRONG_BUY']:
                    await self._execute_buy_signal(signal)
                elif signal.signal_type in ['SELL', 'STRONG_SELL']:
                    await self._execute_sell_signal(signal)

            except Exception as e:
                self.logger.error(f"Error processing signal {signal.symbol}: {str(e)}")

    async def _execute_buy_signal(self, signal: TradingSignal):
        """Execute a buy signal"""
        symbol = signal.symbol

        # Check if we can buy (risk limits, position limits, etc.)
        if not self._can_open_position(symbol):
            self.logger.info(f"Cannot open position for {symbol} - risk limits")
            return

        # Calculate position size
        position_size = self._calculate_position_size(signal)

        # Place order
        order = self.api.submit_order(
            symbol=symbol,
            qty=position_size,
            side='buy',
            type='market',
            time_in_force='gtc'
        )

        self.logger.info(f"Submitted buy order for {symbol}: {position_size} shares")

        # Record trade
        self._record_trade(signal, order, 'BUY')

    async def _execute_sell_signal(self, signal: TradingSignal):
        """Execute a sell signal"""
        symbol = signal.symbol

        # Check if we have position to sell
        positions = self.api.list_positions()
        position = next((p for p in positions if p.symbol == symbol), None)

        if not position:
            self.logger.info(f"No position to sell for {symbol}")
            return

        # Place sell order
        order = self.api.submit_order(
            symbol=symbol,
            qty=abs(int(position.qty)),
            side='sell',
            type='market',
            time_in_force='gtc'
        )

        self.logger.info(f"Submitted sell order for {symbol}: {position.qty} shares")

        # Record trade
        self._record_trade(signal, order, 'SELL')

    def _can_open_position(self, symbol: str) -> bool:
        """Check if we can open a new position"""
        # Check maximum positions limit
        current_positions = len(self.api.list_positions())
        if current_positions >= paper_config.max_positions:
            return False

        # Check daily loss limit
        account = self.api.get_account()
        daily_pnl = float(account.todays_pnl)
        if daily_pnl < -paper_config.daily_loss_limit * paper_config.initial_cash:
            return False

        return True

    def _calculate_position_size(self, signal: TradingSignal) -> int:
        """Calculate position size based on risk management"""
        account = self.api.get_account()
        portfolio_value = float(account.portfolio_value)

        # Base position size
        max_position_value = portfolio_value * paper_config.max_position_size

        # Adjust based on signal confidence
        confidence_multiplier = signal.confidence
        adjusted_position_value = max_position_value * confidence_multiplier

        # Get current price
        quote = self.api.get_latest_quote(signal.symbol)
        current_price = quote.bidprice

        # Calculate shares
        shares = int(adjusted_position_value / current_price)

        return max(1, shares)  # At least 1 share

    def _record_trade(self, signal: TradingSignal, order, action: str):
        """Record trade in database and local history"""
        trade_record = {
            'timestamp': datetime.now(),
            'symbol': signal.symbol,
            'action': action,
            'signal_type': signal.signal_type,
            'confidence': signal.confidence,
            'order_id': order.id,
            'strategy': signal.strategy_name
        }

        self.trade_history.append(trade_record)

        # Also log to AstroA database
        # This would connect to your existing database logging

    def _get_portfolio_state(self) -> Dict:
        """Get current portfolio state"""
        account = self.api.get_account()
        positions = self.api.list_positions()

        return {
            'total_value': float(account.portfolio_value),
            'cash': float(account.cash),
            'positions': [
                {
                    'symbol': p.symbol,
                    'qty': int(p.qty),
                    'market_value': float(p.market_value),
                    'pnl': float(p.unrealized_pnl)
                }
                for p in positions
            ]
        }

    def _update_performance_metrics(self):
        """Update performance tracking"""
        account = self.api.get_account()

        self.performance_metrics = {
            'timestamp': datetime.now(),
            'portfolio_value': float(account.portfolio_value),
            'total_pnl': float(account.portfolio_value) - paper_config.initial_cash,
            'total_pnl_pct': (float(account.portfolio_value) - paper_config.initial_cash) / paper_config.initial_cash * 100,
            'todays_pnl': float(account.todays_pnl),
            'positions_count': len(self.api.list_positions()),
            'trades_count': len(self.trade_history)
        }

    async def _close_all_positions(self):
        """Close all open positions"""
        self.api.close_all_positions()
        self.logger.info("All positions closed")

    def _generate_session_report(self):
        """Generate final session report"""
        report = {
            'session_summary': self.performance_metrics,
            'trade_history': self.trade_history,
            'final_portfolio_value': self.performance_metrics.get('portfolio_value', 0),
            'total_return': self.performance_metrics.get('total_pnl_pct', 0),
            'total_trades': len(self.trade_history)
        }

        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"data/paper_trading_report_{timestamp}.json"

        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        self.logger.info(f"Session report saved to {report_file}")

# Usage
async def main():
    engine = PaperTradingEngine()
    await engine.start_paper_trading()

if __name__ == "__main__":
    asyncio.run(main())</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Paper Trading Launcher Script</h3>

                    <div class="code-file">
                        <div class="file-header">run_paper_trading.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
AstroA Paper Trading Launcher
Entry point for starting paper trading sessions
"""

import asyncio
import argparse
import logging
import signal
import sys
from datetime import datetime

from agents.paper_trading.paper_trading_engine import PaperTradingEngine
from config.settings import Config

class PaperTradingLauncher:
    """Launcher for paper trading sessions"""

    def __init__(self):
        self.engine = None
        self.setup_logging()

    def setup_logging(self):
        """Setup logging for paper trading"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # Create logs directory
        Config.LOGS_DIR.mkdir(exist_ok=True)

        # Setup file handler
        log_file = Config.LOGS_DIR / f"paper_trading_{datetime.now().strftime('%Y%m%d')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

    async def start_session(self, duration_hours: int = None):
        """Start a paper trading session"""
        print("🌟 Starting AstroA Paper Trading Session")
        print("=" * 50)

        self.engine = PaperTradingEngine()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        try:
            if duration_hours:
                print(f"📅 Session duration: {duration_hours} hours")
                # Start session with timeout
                await asyncio.wait_for(
                    self.engine.start_paper_trading(),
                    timeout=duration_hours * 3600
                )
            else:
                print("📅 Session duration: Unlimited (Ctrl+C to stop)")
                await self.engine.start_paper_trading()

        except asyncio.TimeoutError:
            print(f"⏰ Session completed after {duration_hours} hours")
        except KeyboardInterrupt:
            print("\n🛑 Session stopped by user")
        except Exception as e:
            print(f"❌ Session failed: {str(e)}")
            raise
        finally:
            if self.engine:
                await self.engine.stop_paper_trading()

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        if self.engine:
            asyncio.create_task(self.engine.stop_paper_trading())

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='AstroA Paper Trading System')
    parser.add_argument(
        '--duration',
        type=int,
        help='Session duration in hours (default: unlimited)'
    )
    parser.add_argument(
        '--strategies',
        nargs='+',
        default=['mean_reversion', 'momentum'],
        help='Strategies to run (default: mean_reversion momentum)'
    )
    parser.add_argument(
        '--symbols',
        nargs='+',
        help='Override default trading symbols'
    )

    args = parser.parse_args()

    # Create launcher
    launcher = PaperTradingLauncher()

    # Start session
    try:
        asyncio.run(launcher.start_session(args.duration))
    except KeyboardInterrupt:
        print("🛑 Paper trading session terminated")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()</div>
                    </div>
                </div>
            </div>

            <!-- Deployment Tab -->
            <div id="deployment" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">🚀 Deployment & Execution</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 1: Validate Setup</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Test Alpaca connection
python -c "
import alpaca_trade_api as tradeapi
from config.paper_trading_config import paper_config

api = tradeapi.REST(
    paper_config.api_key,
    paper_config.secret_key,
    paper_config.base_url
)

account = api.get_account()
print(f'Account Status: {account.status}')
print(f'Portfolio Value: ${float(account.portfolio_value):,.2f}')
print('✅ Alpaca connection successful!')
"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 2: Start Paper Trading Session</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Navigate to AstroA directory
cd /home/<USER>/axmadcodes/AstroA

# Activate virtual environment
source venv/bin/activate

# Start 8-hour paper trading session
python run_paper_trading.py --duration 8

# Or start unlimited session (stop with Ctrl+C)
python run_paper_trading.py</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 3: Monitor Session (New Terminal)</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Monitor logs in real-time
tail -f logs/paper_trading_$(date +%Y%m%d).log

# Check portfolio status
python -c "
import alpaca_trade_api as tradeapi
from config.paper_trading_config import paper_config

api = tradeapi.REST(paper_config.api_key, paper_config.secret_key, paper_config.base_url)
account = api.get_account()
positions = api.list_positions()

print(f'Portfolio Value: ${float(account.portfolio_value):,.2f}')
print(f'P&L Today: ${float(account.todays_pnl):,.2f}')
print(f'Open Positions: {len(positions)}')

for pos in positions:
    print(f'  {pos.symbol}: {pos.qty} shares, P&L: ${float(pos.unrealized_pnl):,.2f}')
"</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Step 4: Emergency Controls</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Stop paper trading immediately
# Press Ctrl+C in the main terminal

# Or force close all positions
python -c "
import alpaca_trade_api as tradeapi
from config.paper_trading_config import paper_config

api = tradeapi.REST(paper_config.api_key, paper_config.secret_key, paper_config.base_url)
api.close_all_positions()
print('🛑 All positions closed')
"</div>
                </div>
            </div>

            <!-- Monitoring Tab -->
            <div id="monitoring" class="tab-content">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 Monitoring & Analysis</h2>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Real-Time Performance Dashboard</h3>

                    <div class="code-file">
                        <div class="file-header">create_paper_trading_dashboard.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
Real-time Paper Trading Dashboard for AstroA
"""

import streamlit as st
import alpaca_trade_api as tradeapi
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time

from config.paper_trading_config import paper_config

class PaperTradingDashboard:
    """Real-time dashboard for paper trading monitoring"""

    def __init__(self):
        self.api = tradeapi.REST(
            paper_config.api_key,
            paper_config.secret_key,
            paper_config.base_url
        )

    def run(self):
        """Run the Streamlit dashboard"""
        st.set_page_config(
            page_title="AstroA Paper Trading Dashboard",
            page_icon="🌟",
            layout="wide"
        )

        st.title("🌟 AstroA Paper Trading Dashboard")
        st.markdown("Real-time monitoring of your paper trading performance")

        # Auto-refresh every 30 seconds
        if st.checkbox("Auto-refresh (30s)", value=True):
            time.sleep(30)
            st.rerun()

        # Main metrics
        self._display_main_metrics()

        # Charts section
        col1, col2 = st.columns(2)

        with col1:
            self._display_portfolio_chart()

        with col2:
            self._display_positions_chart()

        # Detailed tables
        self._display_positions_table()
        self._display_recent_orders()

    def _display_main_metrics(self):
        """Display main performance metrics"""
        account = self.api.get_account()
        positions = self.api.list_positions()

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            portfolio_value = float(account.portfolio_value)
            st.metric(
                "Portfolio Value",
                f"${portfolio_value:,.2f}",
                delta=f"${float(account.todays_pnl):,.2f}"
            )

        with col2:
            total_pnl = portfolio_value - paper_config.initial_cash
            total_pnl_pct = (total_pnl / paper_config.initial_cash) * 100
            st.metric(
                "Total Return",
                f"{total_pnl_pct:.2f}%",
                delta=f"${total_pnl:,.2f}"
            )

        with col3:
            st.metric(
                "Open Positions",
                len(positions),
                delta=None
            )

        with col4:
            cash = float(account.cash)
            cash_pct = (cash / portfolio_value) * 100
            st.metric(
                "Available Cash",
                f"{cash_pct:.1f}%",
                delta=f"${cash:,.2f}"
            )

    def _display_portfolio_chart(self):
        """Display portfolio value over time"""
        st.subheader("📈 Portfolio Performance")

        # Get portfolio history (mock data for demo)
        # In real implementation, you'd store this data
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')

        # Mock portfolio values (replace with real data)
        portfolio_values = [paper_config.initial_cash + i * 1000 for i in range(len(dates))]

        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=portfolio_values,
            mode='lines',
            name='Portfolio Value',
            line=dict(color='#10b981', width=3)
        ))

        fig.update_layout(
            title="Portfolio Value Over Time",
            xaxis_title="Date",
            yaxis_title="Value ($)",
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

    def _display_positions_chart(self):
        """Display current positions breakdown"""
        st.subheader("📊 Position Allocation")

        positions = self.api.list_positions()

        if positions:
            symbols = [pos.symbol for pos in positions]
            values = [float(pos.market_value) for pos in positions]

            fig = px.pie(
                values=values,
                names=symbols,
                title="Portfolio Allocation by Position"
            )

            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No open positions")

    def _display_positions_table(self):
        """Display detailed positions table"""
        st.subheader("📋 Current Positions")

        positions = self.api.list_positions()

        if positions:
            data = []
            for pos in positions:
                data.append({
                    'Symbol': pos.symbol,
                    'Quantity': int(pos.qty),
                    'Entry Price': f"${float(pos.avg_entry_price):.2f}",
                    'Current Price': f"${float(pos.current_price):.2f}",
                    'Market Value': f"${float(pos.market_value):,.2f}",
                    'P&L': f"${float(pos.unrealized_pnl):,.2f}",
                    'P&L %': f"{(float(pos.unrealized_plpc) * 100):.2f}%"
                })

            df = pd.DataFrame(data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("No open positions")

    def _display_recent_orders(self):
        """Display recent orders"""
        st.subheader("📝 Recent Orders")

        orders = self.api.list_orders(status='all', limit=10)

        if orders:
            data = []
            for order in orders:
                data.append({
                    'Time': order.submitted_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'Symbol': order.symbol,
                    'Side': order.side.upper(),
                    'Quantity': order.qty,
                    'Type': order.order_type.upper(),
                    'Status': order.status.upper(),
                    'Filled': f"{order.filled_qty}/{order.qty}"
                })

            df = pd.DataFrame(data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("No recent orders")

if __name__ == "__main__":
    dashboard = PaperTradingDashboard()
    dashboard.run()</div>
                    </div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Performance Analysis Commands</h3>
                    <div class="command-block">
                        <div class="env-indicator">TERMINAL</div>
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
# Install dashboard dependencies
pip install streamlit plotly

# Start real-time dashboard
streamlit run create_paper_trading_dashboard.py

# Access dashboard at: http://localhost:8501</div>
                </div>

                <div class="step-card bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Success Criteria Validation</h3>

                    <div class="code-file">
                        <div class="file-header">validate_paper_trading_results.py</div>
                        <div class="file-content">#!/usr/bin/env python3
"""
Validate Paper Trading Results Against Success Criteria
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

def validate_paper_trading_results(report_file: str):
    """Validate results against AstroA success criteria"""

    # Load paper trading report
    with open(report_file, 'r') as f:
        report = json.load(f)

    print("🌟 AstroA Paper Trading Results Validation")
    print("=" * 50)

    # Extract metrics
    total_return_pct = report['session_summary']['total_pnl_pct']
    max_drawdown = calculate_max_drawdown(report['trade_history'])
    sharpe_ratio = calculate_sharpe_ratio(report['trade_history'])
    win_rate = calculate_win_rate(report['trade_history'])

    # Validation criteria
    criteria = {
        'Profitable (>0%)': total_return_pct > 0,
        'Max Drawdown (<10%)': max_drawdown < 10,
        'Sharpe Ratio (>1.0)': sharpe_ratio > 1.0,
        'Win Rate (>50%)': win_rate > 50,
        'Duration (>60 days)': len(report['trade_history']) > 0  # Simplified check
    }

    print(f"📊 Performance Metrics:")
    print(f"   Total Return: {total_return_pct:.2f}%")
    print(f"   Max Drawdown: {max_drawdown:.2f}%")
    print(f"   Sharpe Ratio: {sharpe_ratio:.2f}")
    print(f"   Win Rate: {win_rate:.2f}%")
    print(f"   Total Trades: {len(report['trade_history'])}")

    print(f"\n🎯 Success Criteria:")
    passed = 0
    for criterion, result in criteria.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {criterion}: {status}")
        if result:
            passed += 1

    print(f"\n📈 Overall Assessment:")
    if passed >= 4:
        print("🎉 READY FOR LIVE TRADING!")
        print("   Your strategies have proven themselves in paper trading.")
    elif passed >= 3:
        print("⚠️  NEEDS OPTIMIZATION")
        print("   Consider adjusting strategy parameters before live trading.")
    else:
        print("🛑 NOT READY")
        print("   Continue paper trading and strategy development.")

    return passed >= 4

def calculate_max_drawdown(trade_history):
    """Calculate maximum drawdown from trade history"""
    if not trade_history:
        return 0

    # This is simplified - in real implementation, use portfolio values
    returns = [trade.get('pnl_pct', 0) for trade in trade_history]
    cumulative = np.cumsum(returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = running_max - cumulative

    return np.max(drawdown) if len(drawdown) > 0 else 0

def calculate_sharpe_ratio(trade_history):
    """Calculate Sharpe ratio from trade history"""
    if not trade_history:
        return 0

    returns = [trade.get('pnl_pct', 0) for trade in trade_history]
    if len(returns) < 2:
        return 0

    return np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0

def calculate_win_rate(trade_history):
    """Calculate win rate from trade history"""
    if not trade_history:
        return 0

    winning_trades = sum(1 for trade in trade_history if trade.get('pnl', 0) > 0)
    total_trades = len(trade_history)

    return (winning_trades / total_trades) * 100 if total_trades > 0 else 0

if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        print("Usage: python validate_paper_trading_results.py <report_file.json>")
        sys.exit(1)

    report_file = sys.argv[1]
    validate_paper_trading_results(report_file)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="mt-12 bg-gray-800 text-white py-8">
            <div class="container mx-auto px-6 text-center">
                <p class="text-lg">🌟 AstroA Paper Trading Guide</p>
                <p class="mt-2 opacity-75">Validate your strategies with real market conditions</p>
                <div class="mt-4 flex justify-center space-x-6">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="shield" class="w-5 h-5"></i>
                        <span>Risk-Free Testing</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="trending-up" class="w-5 h-5"></i>
                        <span>Real Market Data</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i data-lucide="target" class="w-5 h-5"></i>
                        <span>Strategy Validation</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Copy to clipboard functionality
        function copyToClipboard(button) {
            const codeBlock = button.parentNode;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(function() {
                button.textContent = 'Copied!';
                setTimeout(function() {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        // Add click event listeners to all copy buttons
        document.addEventListener('DOMContentLoaded', function() {
            const copyButtons = document.querySelectorAll('.copy-button');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    copyToClipboard(this);
                });
            });
        });
    </script>
</body>
</html>