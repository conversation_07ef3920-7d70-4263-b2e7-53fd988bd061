"""Main Risk Controller - Coordinates all risk management components"""
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from .position_limits import PositionLimitManager
from .portfolio_risk import PortfolioRiskManager
from .pnl_monitor import RealTimePnLMonitor
from .circuit_breakers import CircuitBreakerManager

class RiskController:
    """Main risk management controller"""

    def __init__(self):
        self.position_limits = PositionLimitManager()
        self.portfolio_risk = PortfolioRiskManager()
        self.pnl_monitor = RealTimePnLMonitor()
        self.circuit_breakers = CircuitBreakerManager()

        self.is_trading_enabled = True
        self.risk_override_active = False

        self.logger = logging.getLogger("RiskController")

    async def initialize(self):
        """Initialize risk management system"""
        try:
            # Setup default limits and circuit breakers
            await self._setup_default_risk_controls()

            # Start monitoring tasks
            asyncio.create_task(self.circuit_breakers.monitor_breakers())

            # Setup callbacks
            self.pnl_monitor.add_alert_callback(self._handle_pnl_alert)
            self.circuit_breakers.add_trigger_callback(self._handle_circuit_breaker)

            self.logger.info("Risk management system initialized")

        except Exception as e:
            self.logger.error(f"Error initializing risk controller: {e}")

    async def validate_order(self, symbol: str, quantity: float, price: float) -> Dict[str, Any]:
        """Comprehensive order validation"""
        try:
            # Check if trading is enabled
            if not self.is_trading_enabled:
                return {
                    'approved': False,
                    'reason': 'Trading disabled',
                    'details': {}
                }

            # Check circuit breakers
            if not self.circuit_breakers.is_trading_allowed():
                return {
                    'approved': False,
                    'reason': 'Circuit breaker active',
                    'details': self.circuit_breakers.get_breaker_status()
                }

            # Check position limits
            position_check = self.position_limits.check_order_compliance(symbol, quantity, price)
            if not position_check['compliant']:
                return {
                    'approved': False,
                    'reason': 'Position limit violation',
                    'details': position_check
                }

            # Check portfolio risk
            # Would implement portfolio-level checks here

            return {
                'approved': True,
                'reason': 'Order approved',
                'details': {
                    'position_check': position_check,
                    'warnings': position_check.get('warnings', [])
                }
            }

        except Exception as e:
            self.logger.error(f"Error validating order: {e}")
            return {
                'approved': False,
                'reason': 'Validation error',
                'details': {'error': str(e)}
            }

    def update_position(self, symbol: str, new_position: float, pnl_change: float):
        """Update position and P&L"""
        try:
            self.position_limits.update_position(symbol, new_position)
            self.pnl_monitor.update_pnl(symbol, pnl_change)

        except Exception as e:
            self.logger.error(f"Error updating position: {e}")

    def check_market_conditions(self, market_data: Dict):
        """Check market conditions for risk triggers"""
        self.circuit_breakers.check_market_conditions(market_data)

    def emergency_stop(self, reason: str = "manual"):
        """Emergency stop all trading"""
        self.is_trading_enabled = False
        self.circuit_breakers.emergency_halt(reason)
        self.logger.critical(f"EMERGENCY STOP ACTIVATED: {reason}")

    def enable_trading(self):
        """Re-enable trading"""
        if not self.circuit_breakers.is_trading_allowed():
            self.logger.warning("Cannot enable trading - circuit breakers active")
            return False

        self.is_trading_enabled = True
        self.logger.info("Trading enabled")
        return True

    def get_risk_status(self) -> Dict[str, Any]:
        """Get comprehensive risk status"""
        return {
            'trading_enabled': self.is_trading_enabled,
            'circuit_breakers': self.circuit_breakers.get_breaker_status(),
            'position_limits': self.position_limits.get_limit_utilization(),
            'pnl_summary': self.pnl_monitor.get_pnl_summary(),
            'risk_override_active': self.risk_override_active,
            'timestamp': datetime.now().isoformat()
        }

    def _handle_pnl_alert(self, alert):
        """Handle P&L alerts"""
        self.logger.warning(f"P&L Alert received: {alert.message}")

        # Take action based on alert severity
        if alert.alert_type == "daily_loss_limit_exceeded":
            self.emergency_stop("Daily loss limit exceeded")

    def _handle_circuit_breaker(self, breaker, trigger_value):
        """Handle circuit breaker triggers"""
        self.logger.critical(f"Circuit breaker triggered: {breaker.breaker_id}")

        # Disable trading when circuit breaker activates
        self.is_trading_enabled = False

    async def _setup_default_risk_controls(self):
        """Setup default risk controls"""
        from .position_limits import PositionLimit, LimitType
        from .circuit_breakers import CircuitBreaker

        # Default position limits
        default_limits = [
            PositionLimit("symbol_limit_default", LimitType.SYMBOL_POSITION, "default", 1000, 1000),
            PositionLimit("sector_limit_tech", LimitType.SECTOR_EXPOSURE, "technology", 5000, 5000),
        ]

        for limit in default_limits:
            self.position_limits.add_limit(limit)

        # Default circuit breakers
        default_breakers = [
            CircuitBreaker("volatility_spike", "volatility_spike", 0.05, 30),
            CircuitBreaker("price_gap", "price_gap", 0.10, 15),
            CircuitBreaker("volume_spike", "volume_spike", 5.0, 10),
        ]

        for breaker in default_breakers:
            self.circuit_breakers.add_circuit_breaker(breaker)