"""
Position Limit Management System
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import logging

class LimitType(Enum):
    SYMBOL_POSITION = "symbol_position"
    SECTOR_EXPOSURE = "sector_exposure"
    CONCENTRATION = "concentration"
    LEVERAGE = "leverage"
    NOTIONAL = "notional"

@dataclass
class PositionLimit:
    limit_id: str
    limit_type: LimitType
    entity: str  # symbol, sector, etc.
    max_long: float
    max_short: float
    max_notional: Optional[float] = None
    enabled: bool = True
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class PositionLimitManager:
    """Manages position limits and validates trades"""

    def __init__(self):
        self.limits: Dict[str, PositionLimit] = {}
        self.current_positions: Dict[str, float] = {}
        self.sector_mappings: Dict[str, str] = {}
        self.logger = logging.getLogger("PositionLimitManager")

    def add_limit(self, limit: PositionLimit):
        """Add position limit"""
        self.limits[limit.limit_id] = limit
        self.logger.info(f"Added limit: {limit.limit_id}")

    def check_order_compliance(self, symbol: str, quantity: float, price: float) -> Dict[str, Any]:
        """Check if order complies with position limits"""
        try:
            violations = []
            warnings = []

            # Check symbol limits
            symbol_violation = self._check_symbol_limit(symbol, quantity)
            if symbol_violation:
                violations.append(symbol_violation)

            # Check sector limits
            sector_violation = self._check_sector_limit(symbol, quantity)
            if sector_violation:
                violations.append(sector_violation)

            # Check concentration limits
            concentration_warning = self._check_concentration_limit(symbol, quantity, price)
            if concentration_warning:
                warnings.append(concentration_warning)

            return {
                'compliant': len(violations) == 0,
                'violations': violations,
                'warnings': warnings
            }

        except Exception as e:
            self.logger.error(f"Error checking compliance: {e}")
            return {'compliant': False, 'violations': [str(e)], 'warnings': []}

    def _check_symbol_limit(self, symbol: str, quantity: float) -> Optional[str]:
        """Check symbol-specific position limits"""
        for limit in self.limits.values():
            if (limit.limit_type == LimitType.SYMBOL_POSITION and
                limit.entity == symbol and limit.enabled):

                current_pos = self.current_positions.get(symbol, 0)
                new_pos = current_pos + quantity

                if quantity > 0 and new_pos > limit.max_long:
                    return f"Symbol {symbol} long position would exceed limit: {new_pos} > {limit.max_long}"
                elif quantity < 0 and new_pos < -limit.max_short:
                    return f"Symbol {symbol} short position would exceed limit: {new_pos} < {-limit.max_short}"

        return None

    def _check_sector_limit(self, symbol: str, quantity: float) -> Optional[str]:
        """Check sector exposure limits"""
        sector = self.sector_mappings.get(symbol)
        if not sector:
            return None

        for limit in self.limits.values():
            if (limit.limit_type == LimitType.SECTOR_EXPOSURE and
                limit.entity == sector and limit.enabled):

                # Calculate current sector exposure
                sector_exposure = sum(
                    self.current_positions.get(sym, 0)
                    for sym, sec in self.sector_mappings.items()
                    if sec == sector
                )

                new_exposure = sector_exposure + quantity

                if quantity > 0 and new_exposure > limit.max_long:
                    return f"Sector {sector} long exposure would exceed limit: {new_exposure} > {limit.max_long}"
                elif quantity < 0 and new_exposure < -limit.max_short:
                    return f"Sector {sector} short exposure would exceed limit: {new_exposure} < {-limit.max_short}"

        return None

    def _check_concentration_limit(self, symbol: str, quantity: float, price: float) -> Optional[str]:
        """Check concentration limits"""
        # Implementation for concentration checks
        return None

    def update_position(self, symbol: str, new_position: float):
        """Update current position for symbol"""
        self.current_positions[symbol] = new_position

    def get_limit_utilization(self) -> Dict[str, Dict[str, float]]:
        """Get current limit utilization"""
        utilization = {}

        for limit_id, limit in self.limits.items():
            if limit.limit_type == LimitType.SYMBOL_POSITION:
                current_pos = self.current_positions.get(limit.entity, 0)

                if current_pos >= 0:
                    util_pct = (current_pos / limit.max_long * 100) if limit.max_long > 0 else 0
                else:
                    util_pct = (abs(current_pos) / limit.max_short * 100) if limit.max_short > 0 else 0

                utilization[limit_id] = {
                    'current_position': current_pos,
                    'utilization_pct': util_pct,
                    'limit_type': limit.limit_type.value
                }

        return utilization