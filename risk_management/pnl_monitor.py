"""Real-Time P&L Monitoring"""
import asyncio
from typing import Dict, List, Callable
from dataclasses import dataclass
from datetime import datetime
import logging

@dataclass
class PnLAlert:
    alert_id: str
    timestamp: datetime
    alert_type: str
    threshold: float
    current_value: float
    message: str

class RealTimePnLMonitor:
    def __init__(self):
        self.current_pnl = 0.0
        self.daily_pnl = 0.0
        self.thresholds = {'daily_loss_limit': -5000, 'total_loss_limit': -10000}
        self.alert_callbacks: List[Callable] = []
        self.logger = logging.getLogger("RealTimePnLMonitor")

    def update_pnl(self, symbol: str, pnl_change: float):
        """Update P&L and check thresholds"""
        self.current_pnl += pnl_change
        self.daily_pnl += pnl_change
        self._check_thresholds()

    def _check_thresholds(self):
        """Check P&L against risk thresholds"""
        if self.daily_pnl < self.thresholds['daily_loss_limit']:
            alert = PnLAlert(
                alert_id=f"daily_loss_{datetime.now().timestamp()}",
                timestamp=datetime.now(),
                alert_type="daily_loss_limit_exceeded",
                threshold=self.thresholds['daily_loss_limit'],
                current_value=self.daily_pnl,
                message=f"Daily loss limit exceeded: ${self.daily_pnl}"
            )
            self._trigger_alert(alert)

    def _trigger_alert(self, alert: PnLAlert):
        """Trigger P&L alert"""
        self.logger.warning(f"P&L Alert: {alert.message}")
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")

    def add_alert_callback(self, callback: Callable):
        """Add P&L alert callback"""
        self.alert_callbacks.append(callback)

    def get_pnl_summary(self) -> Dict[str, float]:
        """Get current P&L summary"""
        return {
            'current_pnl': self.current_pnl,
            'daily_pnl': self.daily_pnl,
            'pnl_percentage': (self.current_pnl / 100000) * 100  # Assuming 100k base
        }