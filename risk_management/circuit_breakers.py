"""Circuit Breakers for Extreme Market Conditions"""
import asyncio
from typing import Dict, List, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import logging

class CircuitBreakerState(Enum):
    CLOSED = "closed"    # Normal operation
    OPEN = "open"        # Trading halted
    HALF_OPEN = "half_open"  # Limited trading

@dataclass
class CircuitBreaker:
    breaker_id: str
    trigger_condition: str
    threshold: float
    duration_minutes: int
    state: CircuitBreakerState = CircuitBreakerState.CLOSED
    triggered_at: datetime = None
    reset_at: datetime = None

class CircuitBreakerManager:
    def __init__(self):
        self.breakers: Dict[str, CircuitBreaker] = {}
        self.trigger_callbacks: List[Callable] = []
        self.market_data_buffer = []
        self.logger = logging.getLogger("CircuitBreakerManager")

    def add_circuit_breaker(self, breaker: CircuitBreaker):
        """Add circuit breaker"""
        self.breakers[breaker.breaker_id] = breaker
        self.logger.info(f"Added circuit breaker: {breaker.breaker_id}")

    def check_market_conditions(self, market_data: Dict):
        """Check market conditions against circuit breaker triggers"""
        try:
            # Check volatility spike
            if 'volatility' in market_data:
                volatility = market_data['volatility']
                if volatility > 0.05:  # 5% volatility spike
                    self._trigger_breaker('volatility_spike', volatility)

            # Check price gaps
            if 'price_change_pct' in market_data:
                price_change = market_data['price_change_pct']
                if abs(price_change) > 0.10:  # 10% price gap
                    self._trigger_breaker('price_gap', price_change)

            # Check volume anomalies
            if 'volume_ratio' in market_data:
                volume_ratio = market_data['volume_ratio']
                if volume_ratio > 5.0:  # 5x normal volume
                    self._trigger_breaker('volume_spike', volume_ratio)

        except Exception as e:
            self.logger.error(f"Error checking market conditions: {e}")

    def _trigger_breaker(self, breaker_type: str, trigger_value: float):
        """Trigger circuit breaker"""
        for breaker_id, breaker in self.breakers.items():
            if (breaker.trigger_condition == breaker_type and
                breaker.state == CircuitBreakerState.CLOSED and
                abs(trigger_value) > breaker.threshold):

                breaker.state = CircuitBreakerState.OPEN
                breaker.triggered_at = datetime.now()
                breaker.reset_at = datetime.now() + timedelta(minutes=breaker.duration_minutes)

                self.logger.critical(f"CIRCUIT BREAKER TRIGGERED: {breaker_id} - {breaker_type} = {trigger_value}")

                # Notify callbacks
                for callback in self.trigger_callbacks:
                    try:
                        callback(breaker, trigger_value)
                    except Exception as e:
                        self.logger.error(f"Error in circuit breaker callback: {e}")

    def is_trading_allowed(self) -> bool:
        """Check if trading is allowed (no active circuit breakers)"""
        active_breakers = [
            b for b in self.breakers.values()
            if b.state == CircuitBreakerState.OPEN
        ]
        return len(active_breakers) == 0

    async def monitor_breakers(self):
        """Monitor and reset circuit breakers"""
        while True:
            try:
                current_time = datetime.now()

                for breaker in self.breakers.values():
                    if (breaker.state == CircuitBreakerState.OPEN and
                        breaker.reset_at and current_time >= breaker.reset_at):

                        breaker.state = CircuitBreakerState.CLOSED
                        breaker.triggered_at = None
                        breaker.reset_at = None

                        self.logger.info(f"Circuit breaker reset: {breaker.breaker_id}")

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Error monitoring breakers: {e}")
                await asyncio.sleep(60)

    def add_trigger_callback(self, callback: Callable):
        """Add circuit breaker trigger callback"""
        self.trigger_callbacks.append(callback)

    def get_breaker_status(self) -> Dict[str, Any]:
        """Get current circuit breaker status"""
        return {
            breaker_id: {
                'state': breaker.state.value,
                'triggered_at': breaker.triggered_at.isoformat() if breaker.triggered_at else None,
                'reset_at': breaker.reset_at.isoformat() if breaker.reset_at else None,
                'threshold': breaker.threshold
            }
            for breaker_id, breaker in self.breakers.items()
        }

    def emergency_halt(self, reason: str = "manual"):
        """Emergency halt all trading"""
        emergency_breaker = CircuitBreaker(
            breaker_id=f"emergency_halt_{datetime.now().timestamp()}",
            trigger_condition="emergency",
            threshold=0.0,
            duration_minutes=60,  # 1 hour halt
            state=CircuitBreakerState.OPEN,
            triggered_at=datetime.now(),
            reset_at=datetime.now() + timedelta(hours=1)
        )

        self.breakers[emergency_breaker.breaker_id] = emergency_breaker
        self.logger.critical(f"EMERGENCY HALT TRIGGERED: {reason}")

        # Notify all callbacks
        for callback in self.trigger_callbacks:
            try:
                callback(emergency_breaker, 0.0)
            except Exception as e:
                self.logger.error(f"Error in emergency halt callback: {e}")