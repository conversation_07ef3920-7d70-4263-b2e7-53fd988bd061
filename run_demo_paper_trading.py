#!/usr/bin/env python3
"""
AstroA Demo Paper Trading
Runs paper trading simulation using existing database data (no external APIs required)
"""

import asyncio
import argparse
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agents.paper_trading.paper_trading_engine import PaperTradingEngine
from config.settings import Config

class DemoPaperTradingLauncher:
    """Demo launcher for paper trading sessions using database data"""

    def __init__(self):
        self.engine = None
        self.setup_logging()

    def setup_logging(self):
        """Setup logging for demo paper trading"""
        log_file = f"logs/demo_paper_trading_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        if self.engine:
            asyncio.create_task(self.engine.stop_paper_trading())

    async def start_demo_session(self, duration_minutes: int = 10):
        """Start a demo paper trading session"""
        print("🌟 Starting AstroA DEMO Paper Trading Session")
        print("=" * 50)
        print("📊 Mode: Database Simulation (No external APIs required)")
        print("💰 Initial Cash: $100,000.00")
        print("🎯 Symbols: BTC, ETH, ADA, SOL, DOT")
        print("⚡ Update Interval: 30s")
        print(f"⏰ Duration: {duration_minutes} minutes")
        print("=" * 50)

        # Override configuration for demo mode
        from config.paper_trading_config import paper_config
        paper_config.api_key = "demo_key"
        paper_config.secret_key = "demo_secret"
        paper_config.data_update_interval = 30  # 30 seconds for demo
        
        self.engine = PaperTradingEngine()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        try:
            print("🚀 Starting demo session...")
            # Start session with timeout
            await asyncio.wait_for(
                self.engine.start_paper_trading(),
                timeout=duration_minutes * 60
            )

        except asyncio.TimeoutError:
            print(f"⏰ Demo session completed after {duration_minutes} minutes")
            await self.engine.stop_paper_trading()
        except KeyboardInterrupt:
            print("\n🛑 Demo session interrupted by user")
            await self.engine.stop_paper_trading()
        except Exception as e:
            print(f"❌ Demo session error: {str(e)}")
            if self.engine:
                await self.engine.stop_paper_trading()
            raise

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='AstroA Demo Paper Trading System')
    parser.add_argument(
        '--duration',
        type=int,
        default=10,
        help='Demo duration in minutes (default: 10)'
    )
    parser.add_argument(
        '--check-data',
        action='store_true',
        help='Check if database has market data'
    )

    args = parser.parse_args()

    # Check database data if requested
    if args.check_data:
        try:
            import psycopg2
            from config.settings import Config
            
            conn = psycopg2.connect(Config.DATABASE_URL)
            cursor = conn.cursor()
            
            # Check for market data
            cursor.execute("SELECT COUNT(*) FROM market_data")
            count = cursor.fetchone()[0]
            
            if count > 0:
                print(f"✅ Database has {count:,} market data records")
                
                # Show recent data
                cursor.execute("""
                    SELECT symbol, COUNT(*) as records, MAX(timestamp) as latest
                    FROM market_data 
                    GROUP BY symbol 
                    ORDER BY records DESC 
                    LIMIT 10
                """)
                
                print("\n📊 Recent market data:")
                for row in cursor.fetchall():
                    print(f"  {row[0]}: {row[1]:,} records (latest: {row[2]})")
                    
            else:
                print("❌ No market data found in database")
                print("💡 Run data collection first: python test_data_collection.py")
                
            cursor.close()
            conn.close()
            return
            
        except Exception as e:
            print(f"❌ Database check failed: {e}")
            return

    # Create launcher
    launcher = DemoPaperTradingLauncher()

    # Start demo session
    try:
        asyncio.run(launcher.start_demo_session(args.duration))
    except KeyboardInterrupt:
        print("🛑 Demo paper trading session terminated")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
