#!/usr/bin/env python3
"""
Verification script for complete Trading Strategy Agent implementation
"""
import os
import sys
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def verify_implementation():
    """Verify all components are properly implemented"""
    print("🔍 AstroA Trading Strategy Agent - Implementation Verification")
    print("=" * 60)

    # Check file structure
    required_files = [
        "shared/types/strategy_types.py",
        "shared/utils/base_strategy.py",
        "agents/trading_strategy/risk_management/risk_manager.py",
        "agents/trading_strategy/strategies/mean_reversion_strategy.py",
        "agents/trading_strategy/strategies/momentum_strategy.py",
        "agents/trading_strategy/trading_strategy_agent.py",
        "setup_trading_tables.sql",
        "test_trading_strategy_agent.py"
    ]

    print("\n📁 File Structure Verification:")
    all_files_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            all_files_exist = False

    # Check database tables
    print("\n🗄️  Database Tables Verification:")
    try:
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            database=os.getenv('DB_NAME', 'mathematical_trading'),
            user=os.getenv('DB_USER', 'trading_user'),
            password=os.getenv('DB_PASSWORD', 'hejhej')
        )
        cursor = conn.cursor()

        required_tables = [
            'trades', 'position_closures', 'portfolio_snapshots',
            'strategy_performance', 'risk_metrics', 'trading_signals'
        ]

        cursor.execute("""
            SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name IN %s
        """, (tuple(required_tables),))

        existing_tables = [row[0] for row in cursor.fetchall()]

        for table in required_tables:
            if table in existing_tables:
                print(f"✅ {table}")
            else:
                print(f"❌ {table}")

        # Check market data availability
        cursor.execute("SELECT COUNT(DISTINCT symbol) FROM market_data WHERE timestamp > NOW() - INTERVAL '7 days'")
        symbol_count = cursor.fetchone()[0]
        print(f"\n📊 Market Data: {symbol_count} symbols available for trading")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

    # Check imports
    print("\n🐍 Python Import Verification:")
    try:
        from shared.types.strategy_types import StrategyType, SignalType, RiskLevel
        print("✅ Strategy types")

        from shared.utils.base_strategy import BaseStrategy
        print("✅ Base strategy")

        from agents.trading_strategy.risk_management.risk_manager import RiskManager
        print("✅ Risk manager")

        from agents.trading_strategy.strategies.mean_reversion_strategy import MeanReversionStrategy
        print("✅ Mean reversion strategy")

        from agents.trading_strategy.strategies.momentum_strategy import MomentumStrategy
        print("✅ Momentum strategy")

        from agents.trading_strategy.trading_strategy_agent import TradingStrategyAgent
        print("✅ Trading strategy agent")

    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

    print("\n🎯 Implementation Status:")
    if all_files_exist:
        print("✅ All required files present")
        print("✅ Database schema configured")
        print("✅ Python modules importable")
        print("✅ Market data available")

        print("\n🚀 System Ready!")
        print("📖 See TRADING_STRATEGY_IMPLEMENTATION_REPORT.md for next steps")
        print("📊 Run 'python test_trading_strategy_agent.py' for full testing")

        return True
    else:
        print("❌ Some components missing")
        return False

if __name__ == "__main__":
    success = verify_implementation()
    sys.exit(0 if success else 1)