{"backup_directory": "backups", "retention_days": 30, "backup_schedule": {"database": "daily", "code": "daily", "logs": "weekly", "models": "weekly"}, "recovery_plans": [{"priority": 1, "component": "database", "recovery_time_objective": 15, "recovery_point_objective": 60, "procedure": "restore_database_from_backup", "dependencies": []}, {"priority": 2, "component": "application", "recovery_time_objective": 30, "recovery_point_objective": 60, "procedure": "restore_application_code", "dependencies": ["database"]}, {"priority": 3, "component": "models", "recovery_time_objective": 60, "recovery_point_objective": 1440, "procedure": "restore_ml_models", "dependencies": ["database", "application"]}], "notification": {"email_enabled": false, "webhook_url": null, "alert_channels": []}}