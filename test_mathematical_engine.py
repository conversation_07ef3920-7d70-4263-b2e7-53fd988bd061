#!/usr/bin/env python3
"""
Comprehensive test script for Mathematical Engine components
"""
import sys
import os
import asyncio
import pandas as pd
import numpy as np
import psycopg2
from datetime import datetime, timedelta
import uuid
import json

sys.path.append(os.getcwd())

from agents.mathematical_engine.mathematical_engine_agent import MathematicalEngineAgent
from agents.mathematical_engine.analyzers.statistical.statistical_analyzer import StatisticalAnalyzer
from agents.mathematical_engine.analyzers.correlation.correlation_analyzer import CorrelationAnalyzer
from agents.mathematical_engine.analyzers.pattern.pattern_analyzer import PatternAnalyzer
from agents.mathematical_engine.analyzers.risk.risk_analyzer import RiskAnalyzer
from shared.mathematical.formulas import IntegratedMathematicalAnalysis
from shared.mathematical.utils import validate_data_quality

def generate_test_data(n_periods: int = 1000, n_assets: int = 3) -> pd.DataFrame:
    """Generate synthetic market data for testing"""
    np.random.seed(42)  # For reproducible results

    dates = pd.date_range(start='2023-01-01', periods=n_periods, freq='h')

    # Generate correlated price series
    base_corr = np.array([
        [1.0, 0.7, 0.3],
        [0.7, 1.0, 0.5],
        [0.3, 0.5, 1.0]
    ])

    # Adjust correlation matrix for number of assets
    if n_assets <= 3:
        correlation_matrix = base_corr[:n_assets, :n_assets]
    else:
        # For more assets, create a larger correlation matrix
        correlation_matrix = np.eye(n_assets)
        for i in range(n_assets):
            for j in range(n_assets):
                if i != j:
                    correlation_matrix[i, j] = max(0.1, 0.8 - 0.1 * abs(i - j))

    # Generate random returns with correlation
    returns = np.random.multivariate_normal(
        mean=[0.0001] * n_assets,
        cov=correlation_matrix * 0.02,
        size=n_periods
    )

    # Convert to price levels
    prices = {}
    volumes = {}

    for i in range(n_assets):
        symbol = f"TEST{i+1}"

        # Create OHLCV data
        base_price = 100.0
        price_series = base_price * np.exp(np.cumsum(returns[:, i]))

        # Add some noise for OHLC
        high_prices = price_series * (1 + np.abs(np.random.normal(0, 0.01, n_periods)))
        low_prices = price_series * (1 - np.abs(np.random.normal(0, 0.01, n_periods)))
        open_prices = np.roll(price_series, 1)
        open_prices[0] = base_price

        # Generate volume data
        volume_series = np.random.lognormal(mean=10, sigma=1, size=n_periods)

        prices[symbol] = pd.DataFrame({
            'timestamp': dates,
            'open_price': open_prices,
            'high_price': high_prices,
            'low_price': low_prices,
            'close_price': price_series,
            'volume': volume_series
        }).set_index('timestamp')

    return prices

async def test_statistical_analyzer():
    """Test the statistical analyzer"""
    print("🧮 Testing Statistical Analyzer...")

    analyzer = StatisticalAnalyzer()
    test_data = generate_test_data(500, 1)['TEST1']

    try:
        # Test comprehensive analysis
        results = analyzer.perform_comprehensive_analysis(test_data, 'TEST1')

        # Verify results structure
        assert 'symbol' in results
        assert 'basic_stats' in results
        assert 'technical_indicators' in results
        assert 'moving_averages' in results
        assert 'bollinger_bands' in results

        print(f"✅ Statistical analysis completed for {results['basic_stats']['count']} data points")
        print(f"✅ Current price: ${results['basic_stats']['current_price']:.2f}")
        print(f"✅ Volatility: {results['return_stats']['annualized_volatility']:.2%}")

        # Test individual components
        close_prices = test_data['close_price']

        # Moving averages
        ma_results = analyzer.calculate_moving_averages(close_prices)
        assert len(ma_results.columns) >= 6  # SMA, EMA, WMA for each period
        print(f"✅ Moving averages calculated: {len(ma_results.columns)} indicators")

        # Bollinger Bands
        bb_results = analyzer.calculate_bollinger_bands(close_prices)
        assert 'bb_upper' in bb_results.columns
        assert 'bb_lower' in bb_results.columns
        print(f"✅ Bollinger Bands calculated")

        # Technical indicators
        tech_indicators = analyzer.calculate_technical_indicators(test_data)
        assert 'rsi' in tech_indicators.columns
        assert 'macd' in tech_indicators.columns
        print(f"✅ Technical indicators calculated: {len(tech_indicators.columns)} indicators")

        return True

    except Exception as e:
        print(f"❌ Statistical analyzer test failed: {e}")
        return False

async def test_correlation_analyzer():
    """Test the correlation analyzer"""
    print("\n🔗 Testing Correlation Analyzer...")

    analyzer = CorrelationAnalyzer()
    test_data = generate_test_data(500, 3)

    try:
        # Create returns DataFrame
        returns_data = {}
        for symbol, data in test_data.items():
            returns_data[symbol] = data['close_price'].pct_change().dropna()

        returns_df = pd.DataFrame(returns_data).dropna()

        # Test comprehensive correlation analysis
        results = analyzer.comprehensive_correlation_analysis(returns_df)

        assert 'static_correlation' in results
        assert 'rolling_correlations' in results
        assert 'cluster_analysis' in results

        # Check correlation matrix
        corr_matrix = results['static_correlation']['correlation_matrix']
        assert corr_matrix.shape == (3, 3)

        print(f"✅ Correlation analysis completed for {len(returns_df.columns)} assets")
        print(f"✅ Average correlation: {corr_matrix.values[np.triu_indices_from(corr_matrix, k=1)].mean():.3f}")

        # Test specific correlation calculations
        corr_result = analyzer.calculate_pearson_correlation(returns_df['TEST1'], returns_df['TEST2'])
        assert 'correlation' in corr_result
        print(f"✅ Pearson correlation TEST1-TEST2: {corr_result['correlation']:.3f}")

        # Test cross-correlation
        cross_corr = analyzer.calculate_cross_correlation(returns_df['TEST1'], returns_df['TEST2'])
        assert 'best_lag' in cross_corr
        print(f"✅ Cross-correlation analysis completed, best lag: {cross_corr['best_lag']}")

        return True

    except Exception as e:
        print(f"❌ Correlation analyzer test failed: {e}")
        return False

async def test_pattern_analyzer():
    """Test the pattern analyzer"""
    print("\n🔍 Testing Pattern Analyzer...")

    analyzer = PatternAnalyzer()
    test_data = generate_test_data(300, 1)['TEST1']

    try:
        # Test comprehensive pattern analysis
        results = analyzer.comprehensive_pattern_analysis(test_data)

        assert 'candlestick_patterns' in results
        assert 'chart_patterns' in results
        assert 'trend_patterns' in results
        assert 'momentum_patterns' in results

        print(f"✅ Pattern analysis completed for {results['analysis_info']['total_periods']} periods")

        # Test candlestick patterns
        candlestick_patterns = results['candlestick_patterns']
        pattern_count = candlestick_patterns.sum().sum()
        print(f"✅ Candlestick patterns detected: {pattern_count} total patterns")

        # Test chart patterns
        chart_patterns = results['chart_patterns']
        if 'peaks' in chart_patterns and 'troughs' in chart_patterns:
            print(f"✅ Chart analysis: {len(chart_patterns['peaks'])} peaks, {len(chart_patterns['troughs'])} troughs")

        # Test trend analysis
        trend_patterns = results['trend_patterns']
        overall_trend = trend_patterns['overall_trend']
        print(f"✅ Trend analysis: {overall_trend['direction']} with strength {overall_trend['strength']:.4f}")

        return True

    except Exception as e:
        print(f"❌ Pattern analyzer test failed: {e}")
        return False

async def test_risk_analyzer():
    """Test the risk analyzer"""
    print("\n🛡️ Testing Risk Analyzer...")

    analyzer = RiskAnalyzer()
    test_data = generate_test_data(500, 3)

    try:
        # Create returns DataFrame
        returns_data = {}
        volume_data = {}

        for symbol, data in test_data.items():
            returns_data[symbol] = data['close_price'].pct_change().dropna()
            volume_data[symbol] = data['volume']

        returns_df = pd.DataFrame(returns_data).dropna()
        volume_df = pd.DataFrame(volume_data).dropna()

        # Test comprehensive risk analysis
        results = analyzer.comprehensive_risk_analysis(returns_df, volumes_df=volume_df)

        assert 'portfolio_risk' in results
        assert 'correlation_risk' in results
        assert 'individual_var' in results

        # Check portfolio risk metrics
        portfolio_risk = results['portfolio_risk']
        assert 'portfolio_volatility' in portfolio_risk
        assert 'sharpe_ratio' in portfolio_risk
        assert 'max_drawdown' in portfolio_risk

        print(f"✅ Risk analysis completed for {len(returns_df.columns)} assets")
        print(f"✅ Portfolio volatility: {portfolio_risk['portfolio_volatility']:.2%}")
        print(f"✅ Sharpe ratio: {portfolio_risk['sharpe_ratio']:.3f}")
        print(f"✅ Max drawdown: {portfolio_risk['max_drawdown']:.2%}")

        # Test VaR calculations
        var_results = analyzer.calculate_value_at_risk(returns_df['TEST1'])
        print(f"✅ VaR (95%): {var_results['95%']['historical_var']:.4f}")

        # Test portfolio risk metrics
        equal_weights = np.ones(len(returns_df.columns)) / len(returns_df.columns)
        portfolio_metrics = analyzer.calculate_portfolio_risk_metrics(returns_df, equal_weights)
        print(f"✅ Portfolio risk metrics calculated")

        return True

    except Exception as e:
        print(f"❌ Risk analyzer test failed: {e}")
        return False

async def test_integrated_analysis():
    """Test the integrated mathematical analysis"""
    print("\n🎯 Testing Integrated Mathematical Analysis...")

    integrated_analyzer = IntegratedMathematicalAnalysis()
    test_data = generate_test_data(400, 2)['TEST1']

    try:
        # Test comprehensive market analysis
        results = integrated_analyzer.comprehensive_market_analysis(test_data)

        assert 'calculus' in results
        assert 'indicators' in results

        print(f"✅ Integrated analysis completed")
        print(f"✅ Calculus analysis: momentum and acceleration calculated")
        print(f"✅ Advanced indicators: momentum oscillator and trend strength")

        return True

    except Exception as e:
        print(f"❌ Integrated analysis test failed: {e}")
        return False

async def test_database_integration():
    """Test database integration"""
    print("\n📊 Testing Database Integration...")

    try:
        # Connect to database
        conn = psycopg2.connect(
            host='localhost',
            database='mathematical_trading',
            user='trading_user',
            password='hejhej'
        )

        cursor = conn.cursor()

        # Check if we have market data
        cursor.execute("SELECT COUNT(*) FROM market_data")
        data_count = cursor.fetchone()[0]

        if data_count < 100:
            print("⚠️ Insufficient market data in database for full testing")
            print(f"   Current data points: {data_count}")
            print("   Recommendation: Run data collection agent first")
            return True

        # Test fetching real market data
        cursor.execute("""
            SELECT symbol, timestamp, close_price, volume
            FROM market_data
            ORDER BY timestamp DESC
            LIMIT 10
        """)

        sample_data = cursor.fetchall()
        print(f"✅ Database connection successful")
        print(f"✅ Market data available: {data_count} records")
        print(f"✅ Latest data: {sample_data[0][1]} for {sample_data[0][0]}")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        print("   Make sure PostgreSQL is running and database is set up")
        return False

async def test_mathematical_engine_agent():
    """Test the main Mathematical Engine Agent"""
    print("\n🤖 Testing Mathematical Engine Agent...")

    try:
        # Create agent instance
        agent = MathematicalEngineAgent()

        # Test initialization
        await agent.initialize()
        print("✅ Agent initialization successful")

        # Test fetching market data
        market_data = await agent.fetch_market_data(limit=100)

        if market_data:
            print(f"✅ Market data fetched: {len(market_data)} symbols")

            # Test analysis quality assessment
            for symbol, data in list(market_data.items())[:1]:  # Test first symbol only
                quality = agent._assess_analysis_quality(data)
                print(f"✅ Data quality for {symbol}: {quality:.2f}")
                break
        else:
            print("⚠️ No market data available for agent testing")

        # Test cleanup
        await agent.cleanup()
        print("✅ Agent cleanup successful")

        return True

    except Exception as e:
        print(f"❌ Mathematical Engine Agent test failed: {e}")
        return False

async def test_mathematical_formulas():
    """Test mathematical formulas implementation"""
    print("\n📐 Testing Mathematical Formulas...")

    try:
        from shared.mathematical.formulas import CalculusFormulas, ProbabilityFormulas, MatrixFormulas

        # Test data
        test_prices = pd.Series([100, 101, 103, 102, 105, 107, 106, 108, 110, 109])

        # Test calculus formulas
        calculus = CalculusFormulas()
        momentum = calculus.derivative_price_momentum(test_prices)
        acceleration = calculus.second_derivative_acceleration(test_prices)

        print("✅ Calculus formulas: derivatives and momentum calculated")

        # Test probability formulas
        probability = ProbabilityFormulas()
        price_prob = probability.normal_distribution_price_probability(test_prices, 115.0)

        print(f"✅ Probability analysis: {price_prob['probability']:.3f} chance of reaching target")

        # Test matrix formulas
        matrix = MatrixFormulas()
        test_returns = pd.DataFrame({
            'Asset1': np.random.normal(0.001, 0.02, 100),
            'Asset2': np.random.normal(0.0008, 0.025, 100),
            'Asset3': np.random.normal(0.0012, 0.03, 100)
        })

        corr_analysis = matrix.correlation_matrix_analysis(test_returns)

        print(f"✅ Matrix formulas: correlation analysis completed")
        print(f"   Determinant: {corr_analysis['determinant']:.4f}")

        return True

    except Exception as e:
        print(f"❌ Mathematical formulas test failed: {e}")
        return False

async def run_performance_benchmark():
    """Run performance benchmark for mathematical operations"""
    print("\n⚡ Running Performance Benchmark...")

    try:
        import time

        # Generate larger dataset
        large_data = generate_test_data(2000, 5)

        # Benchmark statistical analysis
        analyzer = StatisticalAnalyzer()
        start_time = time.time()

        for symbol, data in large_data.items():
            results = analyzer.perform_comprehensive_analysis(data, symbol)

        stat_time = time.time() - start_time
        print(f"✅ Statistical analysis benchmark: {stat_time:.2f}s for 5 assets")

        # Benchmark correlation analysis
        corr_analyzer = CorrelationAnalyzer()
        returns_data = {}
        for symbol, data in large_data.items():
            returns_data[symbol] = data['close_price'].pct_change().dropna()

        returns_df = pd.DataFrame(returns_data).dropna()

        start_time = time.time()
        corr_results = corr_analyzer.comprehensive_correlation_analysis(returns_df)
        corr_time = time.time() - start_time

        print(f"✅ Correlation analysis benchmark: {corr_time:.2f}s for 5x5 correlation matrix")

        # Overall performance assessment
        total_time = stat_time + corr_time
        if total_time < 10:
            print(f"✅ Performance: Excellent ({total_time:.2f}s total)")
        elif total_time < 30:
            print(f"✅ Performance: Good ({total_time:.2f}s total)")
        else:
            print(f"⚠️ Performance: Consider optimization ({total_time:.2f}s total)")

        return True

    except Exception as e:
        print(f"❌ Performance benchmark failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Mathematical Engine - Comprehensive Test Suite")
    print("=" * 60)

    tests = [
        ("Mathematical Formulas", test_mathematical_formulas),
        ("Statistical Analyzer", test_statistical_analyzer),
        ("Correlation Analyzer", test_correlation_analyzer),
        ("Pattern Analyzer", test_pattern_analyzer),
        ("Risk Analyzer", test_risk_analyzer),
        ("Integrated Analysis", test_integrated_analysis),
        ("Database Integration", test_database_integration),
        ("Mathematical Engine Agent", test_mathematical_engine_agent),
        ("Performance Benchmark", run_performance_benchmark),
    ]

    results = []
    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")

    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Mathematical Engine is ready!")
        print("\nNext steps:")
        print("1. Mathematical Engine Agent is fully operational")
        print("2. All mathematical formulas and analyzers working correctly")
        print("3. Ready for integration with Trading Strategy Agent")
        print("4. Database integration confirmed")
        return 0
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))