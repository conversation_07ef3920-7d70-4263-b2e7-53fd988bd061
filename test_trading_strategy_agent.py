#!/usr/bin/env python3
"""
Test script for Trading Strategy Agent
"""
import asyncio
import sys
import logging
from datetime import datetime
import os
from dotenv import load_dotenv

# Add project root to path
sys.path.append('.')

from agents.trading_strategy.trading_strategy_agent import TradingStrategyAgent

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('trading_strategy_test.log')
    ]
)

async def test_trading_strategy_agent():
    """Test the Trading Strategy Agent"""
    logger = logging.getLogger(__name__)

    logger.info("=== Trading Strategy Agent Test ===")

    try:
        # Initialize the agent
        logger.info("1. Initializing Trading Strategy Agent...")
        agent = TradingStrategyAgent()

        # Test initialization
        logger.info("2. Testing agent initialization...")
        await agent.initialize()

        # Test strategy loading
        logger.info("3. Testing strategy initialization...")
        logger.info(f"Loaded {len(agent.strategies)} strategies:")
        for strategy_id, strategy in agent.strategies.items():
            logger.info(f"  - {strategy_id}: {strategy.strategy_type.value} ({len(strategy.symbols)} symbols)")

        # Test portfolio status
        logger.info("4. Testing portfolio status...")
        portfolio = agent.get_portfolio_summary()
        logger.info(f"Portfolio Value: ${portfolio.total_value:,.2f}")
        logger.info(f"Cash: ${portfolio.cash:,.2f}")
        logger.info(f"Positions: {portfolio.positions_count}")

        # Test single execution cycle
        logger.info("5. Testing single execution cycle...")
        await agent.execute_main_logic()

        # Test risk assessment
        logger.info("6. Testing risk assessment...")
        await agent._assess_portfolio_risk()

        # Test market data retrieval
        logger.info("7. Testing market data retrieval...")
        market_data = await agent._get_recent_market_data()
        logger.info(f"Retrieved {len(market_data)} market data records")

        if not market_data.empty:
            symbols = market_data['symbol'].unique()
            logger.info(f"Available symbols: {list(symbols)}")

        # Test strategy signal generation (if we have data)
        if not market_data.empty:
            logger.info("8. Testing strategy signal generation...")
            analysis_results = await agent._get_analysis_results()

            for strategy_id, strategy in agent.strategies.items():
                if strategy.is_active:
                    signals = await strategy.generate_signals(market_data, analysis_results)
                    logger.info(f"Strategy {strategy_id} generated {len(signals)} signals")

                    for signal in signals[:3]:  # Show first 3 signals
                        logger.info(f"  Signal: {signal.signal_type.value} {signal.symbol} at ${signal.price:.2f} (confidence: {signal.confidence:.2f})")

        # Test cleanup
        logger.info("9. Testing cleanup...")
        await agent.cleanup()

        logger.info("=== All tests completed successfully! ===")
        return True

    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        return False

async def test_strategy_components():
    """Test individual strategy components"""
    logger = logging.getLogger(__name__)

    logger.info("=== Testing Strategy Components ===")

    try:
        # Test strategy types import
        from shared.types.strategy_types import StrategyType, SignalType, RiskLevel
        logger.info("✓ Strategy types imported successfully")

        # Test risk manager
        from agents.trading_strategy.risk_management.risk_manager import RiskManager
        risk_manager = RiskManager()
        logger.info("✓ Risk manager initialized successfully")

        # Test strategies
        from agents.trading_strategy.strategies.mean_reversion_strategy import MeanReversionStrategy
        from agents.trading_strategy.strategies.momentum_strategy import MomentumStrategy

        mean_rev = MeanReversionStrategy("test_mr", ["AAPL", "MSFT"])
        momentum = MomentumStrategy("test_mom", ["TSLA", "GOOGL"])

        logger.info("✓ Strategies initialized successfully")
        logger.info(f"  Mean Reversion: {len(mean_rev.symbols)} symbols")
        logger.info(f"  Momentum: {len(momentum.symbols)} symbols")

        return True

    except Exception as e:
        logger.error(f"Component test failed: {e}", exc_info=True)
        return False

async def main():
    """Main test function"""
    logger = logging.getLogger(__name__)

    logger.info("Starting Trading Strategy Agent Tests...")

    # Test components first
    component_test_passed = await test_strategy_components()

    if component_test_passed:
        logger.info("Component tests passed, proceeding with full agent test...")
        agent_test_passed = await test_trading_strategy_agent()

        if agent_test_passed:
            logger.info("🎉 All tests passed successfully!")
            return 0
        else:
            logger.error("❌ Agent tests failed")
            return 1
    else:
        logger.error("❌ Component tests failed")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())