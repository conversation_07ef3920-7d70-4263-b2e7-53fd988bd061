<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux Installation Guide - Mathematical Trading System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .linux-bg {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        }
        .step-card {
            border-left: 4px solid #22c55e;
            transition: all 0.3s ease;
        }
        .step-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .command-block {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Ubuntu Mono', 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 16px 0;
            position: relative;
            overflow-x: auto;
            border: 1px solid #22c55e;
        }
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid #22c55e;
            color: #22c55e;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            font-family: 'Ubuntu', sans-serif;
        }
        .copy-button:hover {
            background: rgba(34, 197, 94, 0.3);
        }
        .step-number {
            width: 50px;
            height: 50px;
            background: #22c55e;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 4px 10px rgba(34, 197, 94, 0.3);
        }
        .ubuntu-card {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
        }
        .mint-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .verification-checklist {
            background: #f0fdf4;
            border: 2px solid #22c55e;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .checklist-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        .checklist-item:hover {
            background: #dcfce7;
        }
        .warning-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .success-box {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .info-box {
            background: #dbeafe;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        .distro-selector {
            display: flex;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .distro-tab {
            flex: 1;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            text-align: center;
        }
        .distro-tab.ubuntu {
            background: #e97008;
            color: white;
        }
        .distro-tab.mint {
            background: #87d068;
            color: white;
        }
        .distro-tab.active {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .distro-content {
            display: none;
        }
        .distro-content.active {
            display: block;
        }
        .tool-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #22c55e;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
        .requirement-badge {
            display: inline-block;
            background: #dcfce7;
            color: #166534;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            margin: 3px;
        }
        .terminal-prompt {
            color: #22c55e;
            font-weight: bold;
        }
        .package-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .package-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.2s;
        }
        .package-item:hover {
            border-color: #22c55e;
            box-shadow: 0 4px 10px rgba(34, 197, 94, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="linux-bg text-white p-4 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <i data-lucide="terminal" class="w-8 h-8"></i>
                <div>
                    <h1 class="text-xl font-bold">Linux Installation Guide</h1>
                    <p class="text-sm opacity-90">Ubuntu & Linux Mint Optimized</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <i data-lucide="monitor" class="w-5 h-5"></i>
                    <span class="text-sm">Linux Ready</span>
                </div>
                <a href="#quick-start" class="bg-white/20 px-4 py-2 rounded hover:bg-white/30 transition">Quick Start</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="linux-bg text-white py-20">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h1 class="text-5xl font-bold mb-6">Linux Installation Guide</h1>
            <p class="text-xl mb-8">Complete setup for Ubuntu 20.04+ and Linux Mint 20+</p>
            <div class="flex justify-center space-x-8">
                <div class="ubuntu-card rounded-lg p-6">
                    <i data-lucide="circle" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Ubuntu</h3>
                    <p class="text-sm">20.04 LTS, 22.04 LTS, 23.04+</p>
                </div>
                <div class="mint-card rounded-lg p-6">
                    <i data-lucide="leaf" class="w-12 h-12 mx-auto mb-3"></i>
                    <h3 class="text-lg font-bold">Linux Mint</h3>
                    <p class="text-sm">20.x, 21.x (Cinnamon/MATE/Xfce)</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick System Check -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">System Requirements Check</h2>

            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h3 class="text-xl font-bold mb-6">🔍 Check Your System First</h3>

                <div class="command-block">
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Check your system information
lsb_release -a                    # Check Ubuntu/Mint version
uname -a                         # Check kernel version
free -h                          # Check available RAM
df -h                           # Check available disk space
nproc                           # Check number of CPU cores
python3 --version               # Check if Python 3 is installed
                </div>

                <div class="grid md:grid-cols-3 gap-6 mt-6">
                    <div class="tool-card">
                        <h4 class="font-bold text-green-600 mb-3">✅ Minimum Requirements</h4>
                        <ul class="text-sm space-y-2">
                            <li>• Ubuntu 20.04+ or Linux Mint 20+</li>
                            <li>• 4GB RAM (8GB recommended)</li>
                            <li>• 10GB free disk space</li>
                            <li>• 2 CPU cores (4+ recommended)</li>
                            <li>• Internet connection</li>
                        </ul>
                    </div>
                    <div class="tool-card">
                        <h4 class="font-bold text-blue-600 mb-3">🎯 Recommended Specs</h4>
                        <ul class="text-sm space-y-2">
                            <li>• Ubuntu 22.04 LTS or Mint 21+</li>
                            <li>• 16GB+ RAM</li>
                            <li>• 50GB+ SSD storage</li>
                            <li>• 4+ CPU cores</li>
                            <li>• Dedicated GPU (optional)</li>
                        </ul>
                    </div>
                    <div class="tool-card">
                        <h4 class="font-bold text-purple-600 mb-3">🚀 Optimal Setup</h4>
                        <ul class="text-sm space-y-2">
                            <li>• Latest Ubuntu/Mint version</li>
                            <li>• 32GB+ RAM</li>
                            <li>• NVMe SSD</li>
                            <li>• 8+ CPU cores</li>
                            <li>• NVIDIA GPU for ML</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Distribution Specific Setup -->
    <section id="quick-start" class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Distribution-Specific Setup</h2>

            <!-- Distribution Selector -->
            <div class="distro-selector max-w-2xl mx-auto">
                <div class="distro-tab ubuntu active" onclick="switchDistro('ubuntu')">
                    <i data-lucide="circle" class="w-6 h-6 inline mr-2"></i>
                    Ubuntu Setup
                </div>
                <div class="distro-tab mint" onclick="switchDistro('mint')">
                    <i data-lucide="leaf" class="w-6 h-6 inline mr-2"></i>
                    Linux Mint Setup
                </div>
            </div>

            <!-- Ubuntu Content -->
            <div id="ubuntu-content" class="distro-content active">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="ubuntu-card rounded-lg p-6 mb-6">
                        <h3 class="text-2xl font-bold mb-2">🍊 Ubuntu 20.04+ / 22.04+ / 23.04+</h3>
                        <p>Optimized commands for Ubuntu and Ubuntu-based distributions</p>
                    </div>

                    <h4 class="text-xl font-bold mb-4">Step 1: Update System & Install Prerequisites</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Update package lists and system
sudo apt update && sudo apt upgrade -y

<span class="terminal-prompt">$</span> # Install essential development tools
sudo apt install -y curl wget git build-essential software-properties-common

<span class="terminal-prompt">$</span> # Install additional useful tools
sudo apt install -y tree htop neofetch vim nano
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 2: Install Python 3.11+</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Add deadsnakes PPA for latest Python versions
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update

<span class="terminal-prompt">$</span> # Install Python 3.11 and related packages
sudo apt install -y python3.11 python3.11-pip python3.11-venv python3.11-dev

<span class="terminal-prompt">$</span> # Install additional Python packages
sudo apt install -y python3-pip python3-venv python-is-python3

<span class="terminal-prompt">$</span> # Set Python 3.11 as default python3 (optional)
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1

<span class="terminal-prompt">$</span> # Verify installation
python3 --version
python3.11 --version
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 3: Install Node.js & npm</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Install Node.js 18+ LTS via NodeSource
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

<span class="terminal-prompt">$</span> # Verify installation
node --version
npm --version

<span class="terminal-prompt">$</span> # Update npm to latest version
sudo npm install -g npm@latest
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 4: Install PostgreSQL Database</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Install PostgreSQL and additional packages
sudo apt install -y postgresql postgresql-contrib postgresql-client

<span class="terminal-prompt">$</span> # Start and enable PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

<span class="terminal-prompt">$</span> # Check service status
sudo systemctl status postgresql

<span class="terminal-prompt">$</span> # Switch to postgres user and create database
sudo -u postgres psql -c "CREATE DATABASE mathematical_trading;"
sudo -u postgres psql -c "CREATE USER trading_user WITH PASSWORD 'secure_password_123';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE mathematical_trading TO trading_user;"
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 5: Install Redis Cache Server</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Install Redis server
sudo apt install -y redis-server

<span class="terminal-prompt">$</span> # Start and enable Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server

<span class="terminal-prompt">$</span> # Test Redis connection
redis-cli ping
# Should return: PONG

<span class="terminal-prompt">$</span> # Check Redis status
sudo systemctl status redis-server
                    </div>
                </div>
            </div>

            <!-- Linux Mint Content -->
            <div id="mint-content" class="distro-content">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="mint-card rounded-lg p-6 mb-6">
                        <h3 class="text-2xl font-bold mb-2">🌿 Linux Mint 20+ / 21+</h3>
                        <p>Optimized commands for Linux Mint (Cinnamon, MATE, Xfce editions)</p>
                    </div>

                    <h4 class="text-xl font-bold mb-4">Step 1: Update System & Install Prerequisites</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Update package lists and system
sudo apt update && sudo apt upgrade -y

<span class="terminal-prompt">$</span> # Install essential development tools
sudo apt install -y curl wget git build-essential software-properties-common

<span class="terminal-prompt">$</span> # Install Mint-specific tools
sudo apt install -y mintupgrade mintstick mintreport

<span class="terminal-prompt">$</span> # Install additional useful tools
sudo apt install -y tree htop neofetch vim nano
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 2: Install Python 3.11+ (Mint Specific)</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Add deadsnakes PPA (works on Mint)
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update

<span class="terminal-prompt">$</span> # Install Python 3.11 and related packages
sudo apt install -y python3.11 python3.11-pip python3.11-venv python3.11-dev

<span class="terminal-prompt">$</span> # Install Python development headers (important for Mint)
sudo apt install -y python3-dev python3-setuptools

<span class="terminal-prompt">$</span> # Set up alternatives (Mint-friendly way)
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 2
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 1

<span class="terminal-prompt">$</span> # Choose Python version (optional)
sudo update-alternatives --config python3

<span class="terminal-prompt">$</span> # Verify installation
python3 --version
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 3: Install Node.js & npm (Mint Method)</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Method 1: Via NodeSource (recommended)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

<span class="terminal-prompt">$</span> # Method 2: Via Snap (alternative)
# sudo snap install node --classic

<span class="terminal-prompt">$</span> # Method 3: Via apt (older version but stable)
# sudo apt install -y nodejs npm

<span class="terminal-prompt">$</span> # Verify installation
node --version
npm --version
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 4: Install PostgreSQL (Mint Optimized)</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib postgresql-client

<span class="terminal-prompt">$</span> # Start PostgreSQL (Mint uses systemd)
sudo systemctl start postgresql
sudo systemctl enable postgresql

<span class="terminal-prompt">$</span> # Configure PostgreSQL for development
sudo -u postgres psql << EOF
CREATE DATABASE mathematical_trading;
CREATE USER trading_user WITH PASSWORD 'secure_password_123';
GRANT ALL PRIVILEGES ON DATABASE mathematical_trading TO trading_user;
ALTER USER trading_user CREATEDB;
\q
EOF

<span class="terminal-prompt">$</span> # Test connection
psql -h localhost -U trading_user -d mathematical_trading -c "SELECT version();"
                    </div>

                    <h4 class="text-xl font-bold mb-4 mt-6">Step 5: Install Redis (Mint Compatible)</h4>
                    <div class="command-block">
                        <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Install Redis server
sudo apt install -y redis-server redis-tools

<span class="terminal-prompt">$</span> # Configure Redis for development
sudo nano /etc/redis/redis.conf
# Uncomment and modify these lines:
# maxmemory 1gb
# maxmemory-policy allkeys-lru

<span class="terminal-prompt">$</span> # Start and enable Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

<span class="terminal-prompt">$</span> # Test Redis
redis-cli ping
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Setup Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Project Environment Setup</h2>

            <!-- Step 1: Create Project -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">1</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Create Project Directory & Virtual Environment</h3>

                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create project directory
mkdir -p ~/Projects/mathematical-trading-system
cd ~/Projects/mathematical-trading-system

<span class="terminal-prompt">$</span> # Create virtual environment using Python 3.11
python3.11 -m venv venv

<span class="terminal-prompt">$</span> # Activate virtual environment
source venv/bin/activate

<span class="terminal-prompt">$</span> # Upgrade pip and setuptools
pip install --upgrade pip setuptools wheel

<span class="terminal-prompt">$</span> # Verify virtual environment
which python
python --version
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Terminal prompt shows <code>(venv)</code></span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Python version is 3.11+</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>pip is latest version (23.0+)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Install Python Packages -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">2</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Install Python Dependencies</h3>

                        <div class="warning-box">
                            <strong>⚠️ Important:</strong> Make sure your virtual environment is activated! You should see <code>(venv)</code> in your terminal prompt.
                        </div>

                        <h4 class="font-semibold mb-3">Core Data Science & Mathematical Libraries</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Install core data manipulation libraries
pip install pandas numpy scipy matplotlib plotly seaborn

<span class="terminal-prompt">(venv) $</span> # Install mathematical computation libraries
pip install sympy statsmodels

<span class="terminal-prompt">(venv) $</span> # Install machine learning frameworks
pip install scikit-learn lightgbm xgboost

<span class="terminal-prompt">(venv) $</span> # Install time series and financial libraries
pip install prophet pyportfolioopt cvxpy ta-lib
                        </div>

                        <div class="info-box">
                            <strong>📝 Note:</strong> TA-Lib might require additional system dependencies. If it fails, install with:
                            <br><code>sudo apt install -y libta-lib-dev</code> then retry the pip install.
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">Cryptocurrency & Financial Data APIs</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Install cryptocurrency exchange APIs
pip install ccxt

<span class="terminal-prompt">(venv) $</span> # Install traditional financial data sources
pip install yfinance alpha_vantage quandl

<span class="terminal-prompt">(venv) $</span> # Install news and social media APIs
pip install newsapi-python tweepy praw

<span class="terminal-prompt">(venv) $</span> # Install web scraping tools
pip install beautifulsoup4 requests lxml selenium

<span class="terminal-prompt">(venv) $</span> # Install HTTP client tools
pip install httpx aiohttp
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">AI & Machine Learning Agents</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Install LLM and AI frameworks
pip install langchain openai anthropic

<span class="terminal-prompt">(venv) $</span> # Install multi-agent systems
pip install autogen-agentchat

<span class="terminal-prompt">(venv) $</span> # Install natural language processing
pip install transformers torch spacy nltk textblob

<span class="terminal-prompt">(venv) $</span> # Download spaCy language model
python -m spacy download en_core_web_sm

<span class="terminal-prompt">(venv) $</span> # Install additional AI tools
pip install sentence-transformers chromadb
                        </div>

                        <h4 class="font-semibold mb-3 mt-6">Web Framework & Database Libraries</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Install web framework
pip install fastapi uvicorn

<span class="terminal-prompt">(venv) $</span> # Install database libraries
pip install sqlalchemy psycopg2-binary alembic

<span class="terminal-prompt">(venv) $</span> # Install caching and task queue
pip install redis celery

<span class="terminal-prompt">(venv) $</span> # Install template and utility libraries
pip install jinja2 python-dotenv pydantic

<span class="terminal-prompt">(venv) $</span> # Install development and testing tools
pip install pytest black isort flake8 mypy pre-commit jupyter
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All packages installed without errors</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Test imports: <code>python -c "import pandas, numpy, ccxt, langchain"</code></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Database Setup -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">3</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Database Schema & Configuration</h3>

                        <h4 class="font-semibold mb-3">Create Database Tables</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create database schema file
cat > database_schema.sql << 'EOF'
-- Mathematical Trading System Database Schema

-- Market data table for OHLCV data
CREATE TABLE IF NOT EXISTS market_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(20,8),
    high_price DECIMAL(20,8),
    low_price DECIMAL(20,8),
    close_price DECIMAL(20,8),
    volume DECIMAL(20,8),
    exchange VARCHAR(50),
    timeframe VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp, exchange, timeframe)
);

-- News data table
CREATE TABLE IF NOT EXISTS news_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20),
    title TEXT,
    content TEXT,
    url TEXT UNIQUE,
    source VARCHAR(100),
    published_at TIMESTAMP,
    sentiment_score DECIMAL(5,4),
    relevance_score DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Features table for engineered features
CREATE TABLE IF NOT EXISTS features (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    timeframe VARCHAR(10),
    feature_name VARCHAR(100),
    feature_value DECIMAL(20,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp, timeframe, feature_name)
);

-- Predictions table
CREATE TABLE IF NOT EXISTS predictions (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    prediction_timestamp TIMESTAMP NOT NULL,
    target_timestamp TIMESTAMP NOT NULL,
    model_name VARCHAR(100),
    predicted_price DECIMAL(20,8),
    predicted_direction VARCHAR(10),
    confidence_score DECIMAL(5,4),
    actual_price DECIMAL(20,8),
    accuracy_score DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Analysis results table
CREATE TABLE IF NOT EXISTS analysis_results (
    id SERIAL PRIMARY KEY,
    run_id UUID NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    selected_symbols JSON,
    mathematical_scores JSON,
    correlation_matrix JSON,
    risk_metrics JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_market_data_exchange_timeframe ON market_data(exchange, timeframe);
CREATE INDEX IF NOT EXISTS idx_news_data_symbol_published ON news_data(symbol, published_at);
CREATE INDEX IF NOT EXISTS idx_features_symbol_timestamp ON features(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_predictions_symbol_target ON predictions(symbol, target_timestamp);
CREATE INDEX IF NOT EXISTS idx_analysis_results_run_id ON analysis_results(run_id);

-- Grant permissions to trading user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trading_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trading_user;
EOF

<span class="terminal-prompt">$</span> # Execute the schema
psql -h localhost -U trading_user -d mathematical_trading -f database_schema.sql
                        </div>

                        <h4 class="font-semibold mb-3 mt-4">Test Database Connection</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Test PostgreSQL connection
python -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='localhost',
        database='mathematical_trading',
        user='trading_user',
        password='secure_password_123'
    )
    cursor = conn.cursor()
    cursor.execute('SELECT version();')
    version = cursor.fetchone()
    print(f'✅ PostgreSQL connected: {version[0][:50]}...')
    cursor.execute('SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = \'public\';')
    table_count = cursor.fetchone()
    print(f'✅ Tables created: {table_count[0]} tables in database')
    conn.close()
except Exception as e:
    print(f'❌ Database connection failed: {e}')
"

<span class="terminal-prompt">$</span> # Test Redis connection
python -c "
import redis
try:
    r = redis.Redis(host='localhost', port=6379, db=0)
    r.ping()
    print('✅ Redis connection successful')
    r.set('test_key', 'test_value')
    value = r.get('test_key')
    print(f'✅ Redis read/write test: {value.decode()}')
    r.delete('test_key')
except Exception as e:
    print(f'❌ Redis connection failed: {e}')
"
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Database schema created successfully</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>PostgreSQL connection test passes</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Redis connection test passes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Project Structure -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">4</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Create Project Structure</h3>

                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create complete directory structure
mkdir -p {agents,data/{raw,processed,features,models},database/{models,migrations},mathematical_engines,ml_models,api/{routes,middleware},frontend/{src,public},reports/{templates,static,output},config,tests/{test_agents,test_mathematical_engines,test_ml_models},scripts,docker,docs,logs}

<span class="terminal-prompt">$</span> # Create Python package __init__.py files
touch agents/__init__.py
touch mathematical_engines/__init__.py
touch ml_models/__init__.py
touch api/__init__.py
touch database/__init__.py
touch database/models/__init__.py

<span class="terminal-prompt">$</span> # Create main configuration and entry files
touch config/settings.py
touch config/__init__.py
touch main.py
touch README.md

<span class="terminal-prompt">$</span> # Create environment and git files
touch .env
touch .env.example
touch .gitignore
touch requirements.txt

<span class="terminal-prompt">$</span> # Create development scripts
touch scripts/dev_setup.sh
touch scripts/start_services.sh
touch scripts/run_tests.sh

<span class="terminal-prompt">$</span> # Make scripts executable
chmod +x scripts/*.sh

<span class="terminal-prompt">$</span> # Display project structure
tree -I 'venv|__pycache__|*.pyc|.git' -L 3
                        </div>

                        <h4 class="font-semibold mb-3 mt-4">Create Configuration Files</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create .env file template
cat > .env << 'EOF'
# Database Configuration
DATABASE_URL=postgresql://trading_user:secure_password_123@localhost:5432/mathematical_trading
REDIS_URL=redis://localhost:6379/0

# Exchange API Keys (Get these from respective exchanges)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_key_here

# News API Keys
NEWS_API_KEY=your_newsapi_key_here

# Financial Data APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
QUANDL_API_KEY=your_quandl_key_here

# AI API Keys (Optional)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# Application Settings
SECRET_KEY=your_secret_key_here_minimum_32_characters
DEBUG=True
LOG_LEVEL=INFO
ENVIRONMENT=development

# Trading Configuration
MAX_POSITION_SIZE=0.1
MAX_PORTFOLIO_VOLATILITY=0.2
MAX_DRAWDOWN_LIMIT=0.15
UPDATE_INTERVAL_SECONDS=60
EOF

<span class="terminal-prompt">$</span> # Create .env.example (for git repository)
cp .env .env.example
sed -i 's/=.*/=your_key_here/g' .env.example

<span class="terminal-prompt">$</span> # Create .gitignore
cat > .gitignore << 'EOF'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/

# Environment variables
.env

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs/
*.log

# Data files
data/raw/*
data/processed/*
!data/raw/.gitkeep
!data/processed/.gitkeep

# Model files
ml_models/*.pkl
ml_models/*.joblib

# Reports
reports/output/*
!reports/output/.gitkeep

# OS
.DS_Store
Thumbs.db

# Jupyter
.ipynb_checkpoints/

# Testing
.pytest_cache/
.coverage
htmlcov/

# Node.js (for frontend)
node_modules/
npm-debug.log*
build/
dist/
EOF

<span class="terminal-prompt">$</span> # Create placeholder files to keep empty directories in git
touch data/raw/.gitkeep
touch data/processed/.gitkeep
touch reports/output/.gitkeep
touch logs/.gitkeep
                        </div>

                        <h4 class="font-semibold mb-3 mt-4">Generate requirements.txt</h4>
                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">(venv) $</span> # Generate requirements from current environment
pip freeze > requirements.txt

<span class="terminal-prompt">$</span> # View the requirements file
head -20 requirements.txt

<span class="terminal-prompt">$</span> # Count total packages installed
wc -l requirements.txt
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All directories created successfully</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>.env file created with all variables</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>requirements.txt contains 50+ packages</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: Frontend Setup -->
            <div class="step-card bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex items-start">
                    <div class="step-number">5</div>
                    <div class="flex-1">
                        <h3 class="text-xl font-bold mb-4">Frontend React Dashboard Setup</h3>

                        <div class="command-block">
                            <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Navigate to frontend directory
cd frontend

<span class="terminal-prompt">$</span> # Create React app with Vite (faster than create-react-app)
npm create vite@latest . -- --template react --yes

<span class="terminal-prompt">$</span> # Install additional frontend dependencies
npm install axios recharts lucide-react @headlessui/react
npm install tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install socket.io-client date-fns

<span class="terminal-prompt">$</span> # Install development dependencies
npm install -D @types/node postcss autoprefixer

<span class="terminal-prompt">$</span> # Initialize Tailwind CSS
npx tailwindcss init -p

<span class="terminal-prompt">$</span> # Test frontend installation
npm run dev &
sleep 3
curl -s http://localhost:5173 | grep -q "Vite" && echo "✅ Frontend working" || echo "❌ Frontend failed"
pkill -f "npm run dev"

<span class="terminal-prompt">$</span> # Return to project root
cd ..
                        </div>

                        <div class="verification-checklist">
                            <h4 class="font-semibold mb-3">✓ Verification Steps:</h4>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>React app created successfully</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All npm packages installed</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Frontend starts and responds on port 5173</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testing & Verification -->
    <section class="py-16 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Final Testing & Verification</h2>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h3 class="text-xl font-bold mb-6">🧪 Complete System Test</h3>

                <div class="command-block">
                    <button class="copy-button" onclick="copyToClipboard(this)">Copy</button>
<span class="terminal-prompt">$</span> # Create comprehensive test script
cat > test_installation.py << 'EOF'
#!/usr/bin/env python3
"""
Comprehensive installation test for Mathematical Trading System
"""
import sys
import subprocess
import importlib

def test_python_version():
    """Test Python version"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    if version >= (3, 11):
        print("✅ Python version is 3.11+")
        return True
    else:
        print("❌ Python version must be 3.11+")
        return False

def test_package_imports():
    """Test critical package imports"""
    packages = {
        'pandas': 'Data manipulation',
        'numpy': 'Numerical computing',
        'scipy': 'Scientific computing',
        'matplotlib': 'Plotting',
        'plotly': 'Interactive plots',
        'sympy': 'Symbolic mathematics',
        'sklearn': 'Machine learning',
        'lightgbm': 'Gradient boosting',
        'ccxt': 'Crypto exchange APIs',
        'yfinance': 'Financial data',
        'langchain': 'LLM framework',
        'fastapi': 'Web framework',
        'sqlalchemy': 'Database ORM',
        'redis': 'Cache client',
        'psycopg2': 'PostgreSQL client'
    }

    failed_imports = []
    print("\n📦 Testing package imports:")

    for package, description in packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {package:<15} - {description}")
        except ImportError as e:
            print(f"❌ {package:<15} - Failed: {e}")
            failed_imports.append(package)

    return failed_imports

def test_database_connection():
    """Test database connectivity"""
    print("\n🗄️  Testing database connections:")

    # Test PostgreSQL
    try:
        import psycopg2
        conn = psycopg2.connect(
            host='localhost',
            database='mathematical_trading',
            user='trading_user',
            password='secure_password_123'
        )
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
        table_count = cursor.fetchone()[0]
        print(f"✅ PostgreSQL: Connected, {table_count} tables")
        conn.close()
        pg_ok = True
    except Exception as e:
        print(f"❌ PostgreSQL: {e}")
        pg_ok = False

    # Test Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        info = r.info()
        print(f"✅ Redis: Connected, version {info['redis_version']}")
        redis_ok = True
    except Exception as e:
        print(f"❌ Redis: {e}")
        redis_ok = False

    return pg_ok and redis_ok

def test_api_functionality():
    """Test API functionality"""
    print("\n🌐 Testing API functionality:")

    # Test CCXT
    try:
        import ccxt
        exchange = ccxt.binance()
        markets = exchange.load_markets()
        print(f"✅ CCXT: {len(markets)} markets loaded from Binance")
        ccxt_ok = True
    except Exception as e:
        print(f"❌ CCXT: {e}")
        ccxt_ok = False

    # Test yfinance
    try:
        import yfinance as yf
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        if 'regularMarketPrice' in info:
            print(f"✅ yfinance: AAPL price ${info['regularMarketPrice']}")
            yf_ok = True
        else:
            print("⚠️ yfinance: Data retrieved but no price info")
            yf_ok = True
    except Exception as e:
        print(f"❌ yfinance: {e}")
        yf_ok = False

    return ccxt_ok and yf_ok

def test_mathematical_libraries():
    """Test mathematical computation libraries"""
    print("\n🧮 Testing mathematical libraries:")

    try:
        import numpy as np
        import pandas as pd
        from scipy import stats
        import sympy as sp

        # Test NumPy
        arr = np.random.normal(0, 1, 1000)
        mean = np.mean(arr)
        print(f"✅ NumPy: Random array mean = {mean:.3f}")

        # Test Pandas
        df = pd.DataFrame({'x': arr, 'y': arr * 2})
        corr = df.corr().iloc[0, 1]
        print(f"✅ Pandas: Correlation = {corr:.3f}")

        # Test SciPy
        stat, p_value = stats.normaltest(arr)
        print(f"✅ SciPy: Normality test p-value = {p_value:.3f}")

        # Test SymPy
        x = sp.Symbol('x')
        derivative = sp.diff(x**2 + 2*x + 1, x)
        print(f"✅ SymPy: d/dx(x²+2x+1) = {derivative}")

        return True
    except Exception as e:
        print(f"❌ Mathematical libraries: {e}")
        return False

def test_system_resources():
    """Test system resources"""
    print("\n💻 System resource check:")

    import psutil

    # CPU cores
    cpu_count = psutil.cpu_count()
    print(f"CPU cores: {cpu_count}")

    # Memory
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    print(f"Total RAM: {memory_gb:.1f} GB")
    print(f"Available RAM: {memory.available / (1024**3):.1f} GB")

    # Disk space
    disk = psutil.disk_usage('/')
    disk_free_gb = disk.free / (1024**3)
    print(f"Free disk space: {disk_free_gb:.1f} GB")

    # Check minimums
    sufficient = (cpu_count >= 2 and memory_gb >= 4 and disk_free_gb >= 10)
    if sufficient:
        print("✅ System resources sufficient")
    else:
        print("⚠️ System resources may be insufficient")

    return sufficient

def main():
    """Run all tests"""
    print("🚀 Mathematical Trading System - Installation Test")
    print("=" * 60)

    tests = [
        ("Python Version", test_python_version),
        ("Package Imports", lambda: len(test_package_imports()) == 0),
        ("Database Connections", test_database_connection),
        ("API Functionality", test_api_functionality),
        ("Mathematical Libraries", test_mathematical_libraries),
        ("System Resources", test_system_resources),
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Your installation is ready!")
        print("Next steps:")
        print("1. Configure your API keys in .env file")
        print("2. Start developing your agents")
        print("3. Run the development manual examples")
        return 0
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
EOF

<span class="terminal-prompt">$</span> # Install psutil for system monitoring
pip install psutil

<span class="terminal-prompt">$</span> # Make test script executable and run it
chmod +x test_installation.py
python test_installation.py
                </div>

                <div class="success-box mt-6">
                    <h4 class="font-bold text-green-800 mb-2">🎯 Success Criteria</h4>
                    <p class="text-green-700">If all tests pass, your Mathematical Trading System is ready for development!</p>
                </div>

                <div class="grid md:grid-cols-2 gap-8 mt-8">
                    <div class="verification-checklist">
                        <h4 class="font-semibold mb-3">✅ Final Checklist</h4>
                        <div class="space-y-2">
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Python 3.11+ installed and working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>All Python packages imported successfully</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>PostgreSQL database connected and tables created</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Redis cache server working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>CCXT and yfinance APIs functional</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Mathematical libraries working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>React frontend created and working</span>
                            </div>
                            <div class="checklist-item">
                                <input type="checkbox" class="mr-3">
                                <span>Project structure created</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="font-semibold mb-4">🚀 Next Steps</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                                <strong>1. Configure API Keys</strong>
                                <p class="text-sm">Edit .env file with your actual API keys from Binance, NewsAPI, etc.</p>
                            </div>
                            <div class="p-3 bg-green-50 border border-green-200 rounded">
                                <strong>2. Start Development</strong>
                                <p class="text-sm">Begin with the Data Collection Agent using the development manual</p>
                            </div>
                            <div class="p-3 bg-purple-50 border border-purple-200 rounded">
                                <strong>3. Initialize Git</strong>
                                <p class="text-sm">Set up version control and make your first commit</p>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h5 class="font-semibold mb-2">Quick Commands:</h5>
                            <div class="command-block text-sm">
<span class="terminal-prompt">$</span> # Initialize Git repository
git init
git add .
git commit -m "Initial project setup"

<span class="terminal-prompt">$</span> # Start development environment
source venv/bin/activate
python test_installation.py

<span class="terminal-prompt">$</span> # Start frontend (in another terminal)
cd frontend && npm run dev
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="linux-bg text-white py-8">
        <div class="max-w-7xl mx-auto text-center px-4">
            <h3 class="text-xl font-bold mb-4">🐧 Linux Installation Complete!</h3>
            <p class="mb-6">Your Mathematical Trading System is ready on Ubuntu/Linux Mint. Time to start coding!</p>
            <div class="flex justify-center space-x-4">
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="play" class="w-4 h-4 inline mr-2"></i>
                    Start Development
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="book-open" class="w-4 h-4 inline mr-2"></i>
                    View Manual
                </button>
                <button class="bg-white/20 px-6 py-2 rounded hover:bg-white/30 transition">
                    <i data-lucide="terminal" class="w-4 h-4 inline mr-2"></i>
                    Open Terminal
                </button>
            </div>
            <p class="text-sm mt-6 opacity-75">© 2025 Mathematical Trading System - Linux Installation Guide</p>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Distribution switching
        function switchDistro(distro) {
            // Hide all distro content
            document.querySelectorAll('.distro-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.distro-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected distro content
            document.getElementById(distro + '-content').classList.add('active');

            // Add active class to selected tab
            const activeTab = document.querySelector(`.distro-tab.${distro}`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
        }

        // Copy to clipboard function
        function copyToClipboard(button) {
            const codeBlock = button.parentElement;
            const code = codeBlock.textContent.replace('Copy', '').trim();

            navigator.clipboard.writeText(code).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = 'rgba(34, 197, 94, 0.4)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(34, 197, 94, 0.2)';
                }, 2000);
            });
        }

        // Auto-check checkboxes and track progress
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateProgress();
            });
        });

        function updateProgress() {
            const totalCheckboxes = document.querySelectorAll('input[type="checkbox"]').length;
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked').length;
            const progress = (checkedBoxes / totalCheckboxes) * 100;

            console.log(`Installation progress: ${progress.toFixed(1)}% (${checkedBoxes}/${totalCheckboxes})`);

            // Update page title with progress
            document.title = `Linux Installation Guide - ${progress.toFixed(0)}% Complete`;
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Highlight command blocks on hover
        document.querySelectorAll('.command-block').forEach(block => {
            block.addEventListener('mouseenter', function() {
                this.style.borderColor = '#16a34a';
                this.style.boxShadow = '0 4px 15px rgba(34, 197, 94, 0.2)';
            });

            block.addEventListener('mouseleave', function() {
                this.style.borderColor = '#22c55e';
                this.style.boxShadow = 'none';
            });
        });

        // Initialize progress tracking
        updateProgress();

        // Add success animation for completed sections
        function animateSuccess(element) {
            element.style.animation = 'pulse 0.5s ease-in-out';
            setTimeout(() => {
                element.style.animation = '';
            }, 500);
        }

        // Terminal-style typing effect for important commands
        function typeCommand(element, text, speed = 50) {
            element.textContent = '';
            let i = 0;
            const timer = setInterval(() => {
                element.textContent += text.charAt(i);
                i++;
                if (i >= text.length) {
                    clearInterval(timer);
                }
            }, speed);
        }

        // Add some visual flair
        document.addEventListener('DOMContentLoaded', function() {
            // Animate step numbers
            document.querySelectorAll('.step-number').forEach((stepNum, index) => {
                setTimeout(() => {
                    stepNum.style.animation = 'fadeInUp 0.6s ease-out';
                }, index * 200);
            });
        });
    </script>
</body>
</html>