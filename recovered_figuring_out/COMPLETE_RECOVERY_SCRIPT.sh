#!/bin/bash

# Complete File Recovery Script for "figuring out" Directory
# This script recreates the deleted "figuring out" directory and restores all recovered files

echo "=========================================="
echo "FIGURING OUT DIRECTORY RECOVERY SCRIPT"
echo "=========================================="
echo "Date: $(date)"
echo "Recovery Location: /home/<USER>/axmadcodes/figuring-out"
echo ""

# Create the original directory structure
TARGET_DIR="/home/<USER>/axmadcodes/figuring-out"
SOURCE_DIR="/home/<USER>/axmadcodes/AstroA/recovered_figuring_out"

echo "Step 1: Creating target directory structure..."
mkdir -p "$TARGET_DIR"

echo "Step 2: Copying recovered files..."

# Files successfully recovered from Claude logs
RECOVERED_FILES=(
    "Development_Manual_Kanban.html"
    "Project_Management_Interface.html"
    "Installation_Setup_Guide.html"
    "Linux_Installation_Guide.html"
    "Mathematical_Trading_Archeology_Action_Plan.html"
)

RECOVERED_COUNT=0
for file in "${RECOVERED_FILES[@]}"; do
    if [ -f "$SOURCE_DIR/$file" ]; then
        cp "$SOURCE_DIR/$file" "$TARGET_DIR/"
        echo "  ✓ Recovered: $file"
        RECOVERED_COUNT=$((RECOVERED_COUNT + 1))
    else
        echo "  ✗ Missing: $file"
    fi
done

echo ""
echo "Step 3: Creating placeholder files for missing content..."

# Files that were referenced but not found in logs (likely already existed)
MISSING_FILES=(
    "AI_Trading_System_Action_Plan.html"
    "readme1.md"
    "crypto_agents_part1.md"
    "promptreadmeprocess.md"
)

for file in "${MISSING_FILES[@]}"; do
    if [ ! -f "$TARGET_DIR/$file" ]; then
        echo "# FILE NOT RECOVERED FROM LOGS" > "$TARGET_DIR/$file"
        echo "" >> "$TARGET_DIR/$file"
        echo "This file was referenced in the Claude logs but the actual content" >> "$TARGET_DIR/$file"
        echo "was not found in the Write tool calls. It may have already existed" >> "$TARGET_DIR/$file"
        echo "before the logged sessions or was created in a different session." >> "$TARGET_DIR/$file"
        echo "" >> "$TARGET_DIR/$file"
        echo "Original file: $file" >> "$TARGET_DIR/$file"
        echo "Recovery date: $(date)" >> "$TARGET_DIR/$file"
        echo "  ⚠ Created placeholder: $file"
    fi
done

echo ""
echo "Step 4: Creating recovery documentation..."

# Create a comprehensive recovery report
cat > "$TARGET_DIR/RECOVERY_STATUS.md" << 'EOF'
# File Recovery Status Report

## Summary
This directory has been recovered from Claude project logs after accidental deletion.

## Successfully Recovered Files (5/9)
These files were fully recovered from Write tool calls in the Claude logs:

1. **Development_Manual_Kanban.html** (77,672 chars)
   - Complete development manual with Kanban structure
   - Source: Log 5c61aafa-9ec7-4cea-9e97-9d48d68bd359.jsonl, Line 24

2. **Project_Management_Interface.html** (42,709 chars)
   - Project management interface design
   - Source: Log 5c61aafa-9ec7-4cea-9e97-9d48d68bd359.jsonl, Line 28

3. **Installation_Setup_Guide.html** (71,052 chars)
   - Complete installation and setup guide
   - Source: Log 5c61aafa-9ec7-4cea-9e97-9d48d68bd359.jsonl, Line 35

4. **Linux_Installation_Guide.html** (60,272 chars)
   - Linux-specific installation guide
   - Source: Log 5c61aafa-9ec7-4cea-9e97-9d48d68bd359.jsonl, Line 40

5. **Mathematical_Trading_Archeology_Action_Plan.html** (65,592 chars)
   - Mathematical trading system action plan
   - Source: Log 98295940-875b-4e60-89b5-8121f451ffc2.jsonl, Line 27

## Files Not Recovered (4/9)
These files were referenced in the logs but content was not found in Write tool calls:

1. **AI_Trading_System_Action_Plan.html**
   - Status: Referenced but content not in logs
   - Likely: Already existed before logged sessions

2. **readme1.md**
   - Status: Referenced in Read tool calls but content not captured
   - Likely: Already existed, was being read not created

3. **crypto_agents_part1.md**
   - Status: Referenced but not created in these sessions
   - Likely: Pre-existing file

4. **promptreadmeprocess.md**
   - Status: Referenced but not found in Write tool calls
   - Likely: Pre-existing file

## Recovery Details
- **Recovery Date**: 2025-09-27
- **Log Files Analyzed**: 2
- **Total Write Tool Calls Found**: 5
- **Recovery Success Rate**: 55.6% (5/9 target files)
- **Data Volume Recovered**: 317,297 characters

## Notes
- All recovered files are complete and intact HTML documents
- Missing files likely existed before the logged Claude sessions
- This represents significant milestone work for the Mathematical Trading System project
- All recovered files contain detailed technical documentation and implementation guides

## Next Steps
1. Review recovered HTML files for completeness
2. Attempt to recreate missing markdown files from memory or other sources
3. Continue development using recovered documentation as foundation
EOF

echo ""
echo "=========================================="
echo "RECOVERY COMPLETE"
echo "=========================================="
echo "Files recovered: $RECOVERED_COUNT/5 found in logs"
echo "Total target files: 9 (4 not found in logs)"
echo "Recovery directory: $TARGET_DIR"
echo ""
echo "Key recovered files:"
for file in "${RECOVERED_FILES[@]}"; do
    if [ -f "$TARGET_DIR/$file" ]; then
        SIZE=$(stat -c%s "$TARGET_DIR/$file")
        echo "  - $file ($(numfmt --to=iec $SIZE))"
    fi
done
echo ""
echo "See RECOVERY_STATUS.md for detailed information."
echo "Review recovered files and continue development work."