#!/usr/bin/env python3
"""
AstroA DeepSeek API Connectivity Test
Tests the DeepSeek AI integration and agent framework
"""
import asyncio
import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import Config
from config.deepseek_client import DeepSeekClient, ChatMessage
from agents.base_agent import AgentOrchestrator
from agents.data_agent import DataAgent

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('AstroA.Test')

async def test_deepseek_basic():
    """Test basic DeepSeek API connectivity"""
    print("\n🚀 Testing DeepSeek API Basic Connectivity...")

    try:
        client = DeepSeekClient()

        messages = [
            ChatMessage("system", "You are Astro<PERSON>, a cryptocurrency analysis AI assistant."),
            Chat<PERSON><PERSON><PERSON>("user", "Hello! Can you confirm you're working? Just respond with 'AstroA online'.")
        ]

        response = await client.chat_completion(messages)
        content = response['choices'][0]['message']['content']

        print(f"✅ DeepSeek Response: {content}")
        return True

    except Exception as e:
        print(f"❌ DeepSeek API Test Failed: {str(e)}")
        return False

async def test_deepseek_market_analysis():
    """Test DeepSeek market analysis capabilities"""
    print("\n📊 Testing DeepSeek Market Analysis...")

    try:
        client = DeepSeekClient()

        # Sample market data
        sample_data = {
            "BTC_USDT": {
                "price": 45000,
                "change_24h": 0.025,
                "volume": 1500000000
            },
            "ETH_USDT": {
                "price": 3200,
                "change_24h": 0.035,
                "volume": 800000000
            }
        }

        analysis = await client.analyze_market_data(
            data=sample_data,
            context="Testing market analysis capabilities"
        )

        print(f"✅ Market Analysis Result:")
        print(f"   {analysis[:200]}...")
        return True

    except Exception as e:
        print(f"❌ Market Analysis Test Failed: {str(e)}")
        return False

async def test_agent_framework():
    """Test the agent framework with DeepSeek integration"""
    print("\n🤖 Testing Agent Framework...")

    try:
        # Create orchestrator and register data agent
        orchestrator = AgentOrchestrator()
        data_agent = DataAgent()
        orchestrator.register_agent(data_agent)

        # Test agent status
        status = orchestrator.get_agent_status()
        print(f"✅ Agent Status: {status}")

        # Note: We're not running the full data agent execution here
        # as it requires API keys and network access
        print("✅ Agent Framework Initialized Successfully")
        return True

    except Exception as e:
        print(f"❌ Agent Framework Test Failed: {str(e)}")
        return False

async def test_configuration():
    """Test configuration setup"""
    print("\n⚙️  Testing Configuration...")

    try:
        # Check if DeepSeek API key is configured
        if not Config.DEEPSEEK_API_KEY or Config.DEEPSEEK_API_KEY == 'your_deepseek_api_key_here':
            print("⚠️  Warning: DeepSeek API key not configured in .env file")
            print("   Please set DEEPSEEK_API_KEY in your .env file")
            return False

        # Validate configuration
        Config.validate_config()
        print("✅ Configuration Valid")

        # Test directory creation
        for directory in [Config.DATA_DIR, Config.REPORTS_DIR, Config.LOGS_DIR]:
            if directory.exists():
                print(f"✅ Directory exists: {directory}")
            else:
                print(f"❌ Directory missing: {directory}")
                return False

        return True

    except Exception as e:
        print(f"❌ Configuration Test Failed: {str(e)}")
        return False

async def test_trading_signal_generation():
    """Test trading signal generation"""
    print("\n📈 Testing Trading Signal Generation...")

    try:
        client = DeepSeekClient()

        sample_indicators = {
            "rsi": 65,
            "macd_signal": "bullish",
            "volume_trend": "increasing",
            "price_trend": "upward",
            "support_level": 44000,
            "resistance_level": 46000
        }

        signal = await client.generate_trading_signal("BTC/USDT", sample_indicators)

        print(f"✅ Trading Signal Generated:")
        print(f"   Signal: {signal.get('signal', 'N/A')}")
        print(f"   Confidence: {signal.get('confidence', 'N/A')}")
        print(f"   Reasoning: {signal.get('reasoning', 'N/A')}")

        return True

    except Exception as e:
        print(f"❌ Trading Signal Test Failed: {str(e)}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🌟 AstroA DeepSeek Integration Test Suite")
    print("=" * 50)

    tests = [
        ("Configuration", test_configuration),
        ("DeepSeek Basic API", test_deepseek_basic),
        ("Market Analysis", test_deepseek_market_analysis),
        ("Trading Signals", test_trading_signal_generation),
        ("Agent Framework", test_agent_framework),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} Test Exception: {str(e)}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("🏁 Test Results Summary:")

    passed = 0
    total = len(results)

    for test_name, passed_test in results:
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if passed_test:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! AstroA is ready for DeepSeek AI integration.")
    else:
        print("⚠️  Some tests failed. Please check the configuration and API keys.")

    return passed == total

def main():
    """Main test execution"""
    try:
        # Run async tests
        result = asyncio.run(run_all_tests())

        if result:
            print("\n🚀 AstroA is configured correctly for DeepSeek AI!")
            sys.exit(0)
        else:
            print("\n❌ Please fix the issues above before proceeding.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()