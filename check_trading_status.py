#!/usr/bin/env python3
"""
Quick Paper Trading Status Check
"""

import sys
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import alpaca_trade_api as tradeapi
    from config.paper_trading_config import paper_config
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def main():
    print("🌟 AstroA Paper Trading - Quick Status")
    print("=" * 50)
    
    try:
        # Initialize API
        api = tradeapi.REST(
            paper_config.api_key,
            paper_config.secret_key,
            paper_config.base_url,
            api_version='v2'
        )
        
        # Get account info
        account = api.get_account()
        
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 Account Status: {account.status}")
        print(f"💰 Portfolio Value: ${float(account.portfolio_value):,.2f}")
        print(f"💵 Available Cash: ${float(account.cash):,.2f}")
        
        # Calculate P&L
        initial_value = 100000.0
        total_pnl = float(account.portfolio_value) - initial_value
        pnl_pct = (total_pnl / initial_value) * 100
        
        pnl_color = "🟢" if total_pnl >= 0 else "🔴"
        print(f"{pnl_color} Total P&L: ${total_pnl:,.2f} ({pnl_pct:+.2f}%)")
        
        # Get positions
        positions = api.list_positions()
        print(f"📈 Open Positions: {len(positions)}")
        
        for pos in positions:
            pnl = float(pos.unrealized_pl)
            pnl_pct = float(pos.unrealized_plpc) * 100
            pnl_color = "🟢" if pnl >= 0 else "🔴"
            print(f"  {pos.symbol}: {pos.qty} shares")
            print(f"    {pnl_color} ${pnl:+,.2f} ({pnl_pct:+.2f}%)")
        
        # Get recent orders
        orders = api.list_orders(status='all', limit=3)
        print(f"📝 Recent Orders: {len(orders)}")
        
        for order in orders:
            status_icon = "✅" if order.status == 'filled' else "⏳" if order.status == 'pending_new' else "❌"
            print(f"  {status_icon} {order.side.upper()} {order.qty} {order.symbol} - {order.status}")
        
        print("\n✅ Status check complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
