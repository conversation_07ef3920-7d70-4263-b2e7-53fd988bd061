"""
Simple monitoring system without Docker dependencies.
Provides metrics collection, storage, and basic alerting.
"""

import sqlite3
import json
import time
import logging
import smtplib
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class Metric:
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = {}

@dataclass
class Alert:
    name: str
    condition: str
    threshold: float
    message: str
    email_recipients: List[str] = None
    enabled: bool = True

    def __post_init__(self):
        if self.email_recipients is None:
            self.email_recipients = []

class MetricsCollector:
    """Lightweight metrics collection system"""

    def __init__(self, db_path: str = "monitoring/metrics.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_db()

    def _init_db(self):
        """Initialize SQLite database for metrics storage"""
        Path(self.db_path).parent.mkdir(exist_ok=True)

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp REAL NOT NULL,
                tags TEXT,
                INDEX(name, timestamp)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alert_name TEXT NOT NULL,
                triggered_at REAL NOT NULL,
                resolved_at REAL,
                message TEXT,
                INDEX(alert_name, triggered_at)
            )
        ''')

        conn.commit()
        conn.close()

    def record_metric(self, metric: Metric):
        """Store a metric in the database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO metrics (name, value, timestamp, tags)
            VALUES (?, ?, ?, ?)
        ''', (
            metric.name,
            metric.value,
            metric.timestamp,
            json.dumps(metric.tags)
        ))

        conn.commit()
        conn.close()

    def get_metrics(self, name: str, start_time: float, end_time: float) -> List[Metric]:
        """Retrieve metrics for a given name and time range"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, value, timestamp, tags
            FROM metrics
            WHERE name = ? AND timestamp BETWEEN ? AND ?
            ORDER BY timestamp DESC
        ''', (name, start_time, end_time))

        metrics = []
        for row in cursor.fetchall():
            name, value, timestamp, tags_json = row
            tags = json.loads(tags_json) if tags_json else {}
            metrics.append(Metric(name, value, timestamp, tags))

        conn.close()
        return metrics

    def get_latest_metric(self, name: str) -> Optional[Metric]:
        """Get the most recent metric value"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, value, timestamp, tags
            FROM metrics
            WHERE name = ?
            ORDER BY timestamp DESC
            LIMIT 1
        ''', (name,))

        row = cursor.fetchone()
        conn.close()

        if row:
            name, value, timestamp, tags_json = row
            tags = json.loads(tags_json) if tags_json else {}
            return Metric(name, value, timestamp, tags)
        return None

class SystemMonitor:
    """Monitors system resources and trading application metrics"""

    def __init__(self, collector: MetricsCollector):
        self.collector = collector
        self.logger = logging.getLogger(__name__)
        self.running = False

    def start_monitoring(self, interval: int = 60):
        """Start monitoring system metrics"""
        self.running = True

        def monitor_loop():
            while self.running:
                try:
                    self._collect_system_metrics()
                    time.sleep(interval)
                except Exception as e:
                    self.logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(interval)

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        self.logger.info("System monitoring started")

    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False
        self.logger.info("System monitoring stopped")

    def _collect_system_metrics(self):
        """Collect system-level metrics"""
        timestamp = time.time()

        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.collector.record_metric(Metric("system.cpu.usage", cpu_percent, timestamp))

        # Memory usage
        memory = psutil.virtual_memory()
        self.collector.record_metric(Metric("system.memory.usage", memory.percent, timestamp))
        self.collector.record_metric(Metric("system.memory.available", memory.available, timestamp))

        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        self.collector.record_metric(Metric("system.disk.usage", disk_percent, timestamp))

        # Network I/O
        network = psutil.net_io_counters()
        self.collector.record_metric(Metric("system.network.bytes_sent", network.bytes_sent, timestamp))
        self.collector.record_metric(Metric("system.network.bytes_recv", network.bytes_recv, timestamp))

    def record_trading_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Record application-specific trading metrics"""
        metric = Metric(name, value, time.time(), tags or {})
        self.collector.record_metric(metric)

class AlertManager:
    """Simple alerting system"""

    def __init__(self, collector: MetricsCollector, smtp_config: Dict[str, str] = None):
        self.collector = collector
        self.alerts: List[Alert] = []
        self.active_alerts: Dict[str, float] = {}  # alert_name -> triggered_time
        self.smtp_config = smtp_config or {}
        self.logger = logging.getLogger(__name__)

    def add_alert(self, alert: Alert):
        """Add an alert rule"""
        self.alerts.append(alert)
        self.logger.info(f"Added alert rule: {alert.name}")

    def check_alerts(self):
        """Check all alert conditions"""
        for alert in self.alerts:
            if not alert.enabled:
                continue

            try:
                self._evaluate_alert(alert)
            except Exception as e:
                self.logger.error(f"Error evaluating alert {alert.name}: {e}")

    def _evaluate_alert(self, alert: Alert):
        """Evaluate a single alert condition"""
        # Get latest metric value
        metric_name = alert.name.replace("alert.", "")
        latest_metric = self.collector.get_latest_metric(metric_name)

        if not latest_metric:
            return

        # Check if alert should trigger
        should_trigger = self._check_condition(latest_metric.value, alert.condition, alert.threshold)

        if should_trigger and alert.name not in self.active_alerts:
            # Trigger alert
            self._trigger_alert(alert, latest_metric.value)
        elif not should_trigger and alert.name in self.active_alerts:
            # Resolve alert
            self._resolve_alert(alert)

    def _check_condition(self, value: float, condition: str, threshold: float) -> bool:
        """Check if alert condition is met"""
        if condition == "greater_than":
            return value > threshold
        elif condition == "less_than":
            return value < threshold
        elif condition == "equals":
            return abs(value - threshold) < 0.001
        else:
            return False

    def _trigger_alert(self, alert: Alert, current_value: float):
        """Trigger an alert"""
        self.active_alerts[alert.name] = time.time()

        message = alert.message.format(
            name=alert.name,
            value=current_value,
            threshold=alert.threshold,
            timestamp=datetime.now().isoformat()
        )

        self.logger.warning(f"ALERT TRIGGERED: {alert.name} - {message}")

        # Store alert in database
        conn = sqlite3.connect(self.collector.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO alerts (alert_name, triggered_at, message)
            VALUES (?, ?, ?)
        ''', (alert.name, time.time(), message))
        conn.commit()
        conn.close()

        # Send email if configured
        if alert.email_recipients and self.smtp_config:
            self._send_email_alert(alert, message)

    def _resolve_alert(self, alert: Alert):
        """Resolve an active alert"""
        if alert.name in self.active_alerts:
            triggered_time = self.active_alerts.pop(alert.name)

            self.logger.info(f"ALERT RESOLVED: {alert.name}")

            # Update alert in database
            conn = sqlite3.connect(self.collector.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE alerts
                SET resolved_at = ?
                WHERE alert_name = ? AND triggered_at = ? AND resolved_at IS NULL
            ''', (time.time(), alert.name, triggered_time))
            conn.commit()
            conn.close()

    def _send_email_alert(self, alert: Alert, message: str):
        """Send email notification"""
        try:
            msg = MimeMultipart()
            msg['From'] = self.smtp_config.get('from_email', '')
            msg['To'] = ', '.join(alert.email_recipients)
            msg['Subject'] = f"Trading Alert: {alert.name}"

            body = f"Trading System Alert\n\n{message}"
            msg.attach(MimeText(body, 'plain'))

            server = smtplib.SMTP(self.smtp_config.get('host', 'localhost'),
                                 self.smtp_config.get('port', 587))
            if self.smtp_config.get('use_tls', True):
                server.starttls()
            if self.smtp_config.get('username'):
                server.login(self.smtp_config['username'], self.smtp_config['password'])

            server.send_message(msg)
            server.quit()

            self.logger.info(f"Email alert sent for {alert.name}")
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")

class MonitoringDashboard:
    """Simple web dashboard for viewing metrics"""

    def __init__(self, collector: MetricsCollector):
        self.collector = collector

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status"""
        now = time.time()
        one_hour_ago = now - 3600

        status = {}

        # Get latest system metrics
        for metric_name in ["system.cpu.usage", "system.memory.usage", "system.disk.usage"]:
            latest = self.collector.get_latest_metric(metric_name)
            if latest:
                status[metric_name] = {
                    "current": latest.value,
                    "timestamp": latest.timestamp,
                    "status": "ok" if latest.value < 80 else "warning" if latest.value < 95 else "critical"
                }

        return status

    def get_trading_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """Get trading performance metrics"""
        now = time.time()
        start_time = now - (hours * 3600)

        metrics = {}

        # Common trading metrics to check
        trading_metric_names = [
            "trading.pnl.daily",
            "trading.trades.count",
            "trading.orders.executed",
            "trading.orders.failed",
            "trading.latency.avg"
        ]

        for metric_name in trading_metric_names:
            recent_metrics = self.collector.get_metrics(metric_name, start_time, now)
            if recent_metrics:
                latest = recent_metrics[0]
                metrics[metric_name] = {
                    "current": latest.value,
                    "history": [{"value": m.value, "timestamp": m.timestamp} for m in recent_metrics[-100:]]
                }

        return metrics

# Factory function to create a complete monitoring setup
def create_monitoring_system(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create a complete monitoring system"""
    config = config or {}

    # Create components
    collector = MetricsCollector(config.get("db_path", "monitoring/metrics.db"))
    monitor = SystemMonitor(collector)
    alert_manager = AlertManager(collector, config.get("smtp_config"))
    dashboard = MonitoringDashboard(collector)

    # Setup default alerts
    default_alerts = [
        Alert(
            name="alert.system.cpu.usage",
            condition="greater_than",
            threshold=90.0,
            message="High CPU usage detected: {value:.1f}% (threshold: {threshold}%)",
            email_recipients=config.get("alert_emails", [])
        ),
        Alert(
            name="alert.system.memory.usage",
            condition="greater_than",
            threshold=85.0,
            message="High memory usage detected: {value:.1f}% (threshold: {threshold}%)",
            email_recipients=config.get("alert_emails", [])
        ),
        Alert(
            name="alert.system.disk.usage",
            condition="greater_than",
            threshold=90.0,
            message="High disk usage detected: {value:.1f}% (threshold: {threshold}%)",
            email_recipients=config.get("alert_emails", [])
        )
    ]

    for alert in default_alerts:
        alert_manager.add_alert(alert)

    return {
        "collector": collector,
        "monitor": monitor,
        "alert_manager": alert_manager,
        "dashboard": dashboard
    }