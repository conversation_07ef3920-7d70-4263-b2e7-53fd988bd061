#!/usr/bin/env python3
"""
Monitoring daemon for the trading system.
Runs monitoring, alerting, and provides a simple web interface.
"""

import sys
import time
import signal
import logging
import argparse
import threading
from pathlib import Path
from typing import Dict, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from monitoring.simple_metrics import create_monitoring_system
from monitoring.web_interface import create_web_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('monitoring/monitor.log'),
        logging.StreamHandler()
    ]
)

class MonitoringDaemon:
    """Main monitoring daemon"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.monitoring_system = None
        self.web_app = None

    def start(self):
        """Start the monitoring daemon"""
        self.logger.info("Starting monitoring daemon...")

        # Create monitoring system
        self.monitoring_system = create_monitoring_system(self.config)

        # Start system monitoring
        self.monitoring_system["monitor"].start_monitoring(
            interval=self.config.get("monitoring_interval", 60)
        )

        # Start alert checking loop
        self._start_alert_loop()

        # Start web interface if enabled
        if self.config.get("enable_web_interface", True):
            self._start_web_interface()

        self.running = True
        self.logger.info("Monitoring daemon started successfully")

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def stop(self):
        """Stop the monitoring daemon"""
        self.logger.info("Stopping monitoring daemon...")
        self.running = False

        if self.monitoring_system:
            self.monitoring_system["monitor"].stop_monitoring()

        self.logger.info("Monitoring daemon stopped")

    def _start_alert_loop(self):
        """Start the alert checking loop"""
        def alert_loop():
            while self.running:
                try:
                    self.monitoring_system["alert_manager"].check_alerts()
                    time.sleep(self.config.get("alert_check_interval", 30))
                except Exception as e:
                    self.logger.error(f"Error in alert loop: {e}")
                    time.sleep(30)

        alert_thread = threading.Thread(target=alert_loop, daemon=True)
        alert_thread.start()
        self.logger.info("Alert checking loop started")

    def _start_web_interface(self):
        """Start the web interface"""
        try:
            from monitoring.web_interface import create_web_app

            self.web_app = create_web_app(self.monitoring_system)

            def run_web():
                self.web_app.run(
                    host=self.config.get("web_host", "127.0.0.1"),
                    port=self.config.get("web_port", 8080),
                    debug=False
                )

            web_thread = threading.Thread(target=run_web, daemon=True)
            web_thread.start()

            self.logger.info(f"Web interface started on http://{self.config.get('web_host', '127.0.0.1')}:{self.config.get('web_port', 8080)}")
        except ImportError:
            self.logger.warning("Flask not available, web interface disabled")
        except Exception as e:
            self.logger.error(f"Failed to start web interface: {e}")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)

    def run_forever(self):
        """Run the daemon until interrupted"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()

def load_config() -> Dict[str, Any]:
    """Load monitoring configuration"""
    # Default configuration
    config = {
        "db_path": "monitoring/metrics.db",
        "monitoring_interval": 60,  # seconds
        "alert_check_interval": 30,  # seconds
        "enable_web_interface": True,
        "web_host": "127.0.0.1",
        "web_port": 8080,
        "alert_emails": [],
        "smtp_config": {
            "host": "localhost",
            "port": 587,
            "use_tls": True,
            "username": None,
            "password": None,
            "from_email": "trading-alerts@localhost"
        }
    }

    # Try to load from config file
    config_file = Path("monitoring/config.json")
    if config_file.exists():
        import json
        try:
            with open(config_file) as f:
                file_config = json.load(f)
                config.update(file_config)
            logging.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            logging.warning(f"Failed to load config from {config_file}: {e}")

    # Override with environment variables
    import os
    env_mappings = {
        "MONITOR_DB_PATH": "db_path",
        "MONITOR_INTERVAL": ("monitoring_interval", int),
        "ALERT_INTERVAL": ("alert_check_interval", int),
        "WEB_HOST": "web_host",
        "WEB_PORT": ("web_port", int),
        "ALERT_EMAILS": ("alert_emails", lambda x: x.split(",")),
        "SMTP_HOST": ("smtp_config", "host"),
        "SMTP_PORT": ("smtp_config", "port", int),
        "SMTP_USERNAME": ("smtp_config", "username"),
        "SMTP_PASSWORD": ("smtp_config", "password"),
        "SMTP_FROM": ("smtp_config", "from_email"),
    }

    for env_var, config_path in env_mappings.items():
        value = os.getenv(env_var)
        if value is not None:
            if isinstance(config_path, tuple):
                if len(config_path) == 2:
                    key, converter = config_path
                    config[key] = converter(value)
                elif len(config_path) == 3:
                    section, key, converter = config_path
                    config[section][key] = converter(value)
            elif isinstance(config_path, str):
                config[config_path] = value

    return config

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Trading System Monitoring Daemon")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--daemon", action="store_true", help="Run as daemon")

    args = parser.parse_args()

    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # Load configuration
    config = load_config()

    if args.config:
        import json
        with open(args.config) as f:
            custom_config = json.load(f)
            config.update(custom_config)

    # Create and start daemon
    daemon = MonitoringDaemon(config)

    try:
        daemon.start()
        daemon.run_forever()
    except Exception as e:
        logging.error(f"Daemon failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()