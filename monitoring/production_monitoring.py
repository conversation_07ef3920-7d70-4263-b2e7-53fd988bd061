"""
Production Monitoring Stack for Live Trading
Comprehensive monitoring, logging, metrics collection, and alerting system
"""

import asyncio
import json
import os
import psutil
import time
import logging
import aiohttp
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import prometheus_client
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
import structlog
import traceback
from pathlib import Path

class MonitoringLevel(Enum):
    """Monitoring severity levels"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ComponentStatus(Enum):
    """System component status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    process_count: int
    database_connections: int
    redis_connections: int
    api_response_time: float
    trading_latency: float

@dataclass
class ComponentHealth:
    """Health status of a system component"""
    name: str
    status: ComponentStatus
    last_check: datetime
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Alert:
    """System alert"""
    alert_id: str
    component: str
    severity: MonitoringLevel
    title: str
    message: str
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None

class MetricsCollector:
    """Prometheus metrics collector for trading system"""

    def __init__(self):
        self.registry = CollectorRegistry()

        # Trading metrics
        self.orders_total = Counter(
            'trading_orders_total',
            'Total number of orders',
            ['broker', 'symbol', 'side', 'status'],
            registry=self.registry
        )

        self.trades_total = Counter(
            'trading_trades_total',
            'Total number of trades',
            ['symbol', 'strategy'],
            registry=self.registry
        )

        self.order_latency = Histogram(
            'trading_order_latency_seconds',
            'Order execution latency',
            ['broker'],
            registry=self.registry
        )

        self.pnl_gauge = Gauge(
            'trading_pnl_total',
            'Current P&L',
            ['type'],  # realized, unrealized, total
            registry=self.registry
        )

        self.portfolio_value = Gauge(
            'trading_portfolio_value',
            'Current portfolio value',
            registry=self.registry
        )

        # System metrics
        self.cpu_usage = Gauge(
            'system_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )

        self.memory_usage = Gauge(
            'system_memory_usage_percent',
            'Memory usage percentage',
            registry=self.registry
        )

        self.disk_usage = Gauge(
            'system_disk_usage_percent',
            'Disk usage percentage',
            registry=self.registry
        )

        # Data feed metrics
        self.market_data_latency = Histogram(
            'market_data_latency_seconds',
            'Market data latency',
            ['provider', 'symbol'],
            registry=self.registry
        )

        self.market_data_rate = Gauge(
            'market_data_rate_per_second',
            'Market data messages per second',
            ['provider'],
            registry=self.registry
        )

        # API metrics
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status'],
            registry=self.registry
        )

        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint'],
            registry=self.registry
        )

        # Error metrics
        self.errors_total = Counter(
            'errors_total',
            'Total errors',
            ['component', 'error_type'],
            registry=self.registry
        )

        self.alerts_total = Counter(
            'alerts_total',
            'Total alerts',
            ['component', 'severity'],
            registry=self.registry
        )

class StructuredLogger:
    """Structured logging system for trading application"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.log_level = config.get('log_level', 'INFO')
        self.log_format = config.get('log_format', 'json')
        self.log_file = config.get('log_file', 'logs/trading.log')

        # Ensure log directory exists
        Path(self.log_file).parent.mkdir(parents=True, exist_ok=True)

        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

        # Setup file handler
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(getattr(logging, self.log_level.upper()))

        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            handlers=[file_handler, console_handler],
            format='%(message)s'
        )

        self.logger = structlog.get_logger()

    def log_trade(self, trade_data: Dict[str, Any]):
        """Log trade execution"""
        self.logger.info(
            "Trade executed",
            event_type="trade",
            **trade_data
        )

    def log_order(self, order_data: Dict[str, Any]):
        """Log order placement"""
        self.logger.info(
            "Order placed",
            event_type="order",
            **order_data
        )

    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """Log error with context"""
        self.logger.error(
            "Error occurred",
            event_type="error",
            error_type=type(error).__name__,
            error_message=str(error),
            traceback=traceback.format_exc(),
            **(context or {})
        )

    def log_performance(self, metrics: Dict[str, Any]):
        """Log performance metrics"""
        self.logger.info(
            "Performance metrics",
            event_type="performance",
            **metrics
        )

class HealthChecker:
    """System health monitoring"""

    def __init__(self):
        self.components: Dict[str, ComponentHealth] = {}
        self.health_checks: Dict[str, Callable] = {}
        self.check_interval = 30  # seconds
        self.timeout = 10  # seconds

    def register_component(self, name: str, health_check: Callable):
        """Register a component for health monitoring"""
        self.health_checks[name] = health_check
        self.components[name] = ComponentHealth(
            name=name,
            status=ComponentStatus.UNKNOWN,
            last_check=datetime.now()
        )

    async def check_all_components(self) -> Dict[str, ComponentHealth]:
        """Check health of all registered components"""
        tasks = []
        for name in self.health_checks:
            task = asyncio.create_task(self._check_component(name))
            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)
        return self.components

    async def _check_component(self, name: str):
        """Check health of a single component"""
        try:
            start_time = time.time()
            health_check = self.health_checks[name]

            # Run health check with timeout
            result = await asyncio.wait_for(
                health_check(),
                timeout=self.timeout
            )

            response_time = time.time() - start_time

            # Update component health
            if result is True:
                status = ComponentStatus.HEALTHY
                error_message = None
            elif isinstance(result, dict):
                status = ComponentStatus(result.get('status', 'healthy'))
                error_message = result.get('error')
            else:
                status = ComponentStatus.DEGRADED
                error_message = str(result)

            self.components[name] = ComponentHealth(
                name=name,
                status=status,
                last_check=datetime.now(),
                response_time=response_time,
                error_message=error_message
            )

        except asyncio.TimeoutError:
            self.components[name] = ComponentHealth(
                name=name,
                status=ComponentStatus.UNHEALTHY,
                last_check=datetime.now(),
                error_message="Health check timeout"
            )

        except Exception as e:
            self.components[name] = ComponentHealth(
                name=name,
                status=ComponentStatus.UNHEALTHY,
                last_check=datetime.now(),
                error_message=str(e)
            )

    def get_overall_status(self) -> ComponentStatus:
        """Get overall system health status"""
        if not self.components:
            return ComponentStatus.UNKNOWN

        statuses = [comp.status for comp in self.components.values()]

        if any(status == ComponentStatus.UNHEALTHY for status in statuses):
            return ComponentStatus.UNHEALTHY
        elif any(status == ComponentStatus.DEGRADED for status in statuses):
            return ComponentStatus.DEGRADED
        elif all(status == ComponentStatus.HEALTHY for status in statuses):
            return ComponentStatus.HEALTHY
        else:
            return ComponentStatus.UNKNOWN

class ProductionMonitor:
    """Main production monitoring system"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics_collector = MetricsCollector()
        self.logger = StructuredLogger(config.get('logging', {}))
        self.health_checker = HealthChecker()

        # Alert system
        self.alerts: List[Alert] = []
        self.alert_thresholds = config.get('alert_thresholds', {})
        self.alert_channels = []

        # Monitoring state
        self.is_monitoring = False
        self.last_metrics_collection = datetime.now()
        self.system_start_time = datetime.now()

        # Performance tracking
        self.performance_history: List[SystemMetrics] = []
        self.max_history_length = config.get('max_history_length', 1000)

        self.monitoring_logger = logging.getLogger("ProductionMonitor")

    async def start_monitoring(self):
        """Start the production monitoring system"""
        try:
            self.is_monitoring = True
            self.monitoring_logger.info("Starting production monitoring")

            # Register default health checks
            await self._register_default_health_checks()

            # Start monitoring tasks
            tasks = [
                asyncio.create_task(self._collect_system_metrics()),
                asyncio.create_task(self._monitor_health()),
                asyncio.create_task(self._process_alerts()),
                asyncio.create_task(self._export_metrics()),
                asyncio.create_task(self._cleanup_old_data())
            ]

            await asyncio.gather(*tasks)

        except Exception as e:
            self.monitoring_logger.error(f"Error in production monitoring: {e}")
            self.is_monitoring = False

    async def stop_monitoring(self):
        """Stop monitoring"""
        self.is_monitoring = False
        self.monitoring_logger.info("Production monitoring stopped")

    async def _register_default_health_checks(self):
        """Register default system health checks"""

        async def database_health():
            """Check database connectivity"""
            try:
                # This would check your actual database
                return True
            except Exception as e:
                return {'status': 'unhealthy', 'error': str(e)}

        async def redis_health():
            """Check Redis connectivity"""
            try:
                # This would check your actual Redis instance
                return True
            except Exception as e:
                return {'status': 'unhealthy', 'error': str(e)}

        async def data_feed_health():
            """Check data feed connectivity"""
            try:
                # This would check your data feeds
                return True
            except Exception as e:
                return {'status': 'degraded', 'error': str(e)}

        async def broker_health():
            """Check broker connectivity"""
            try:
                # This would check your brokers
                return True
            except Exception as e:
                return {'status': 'unhealthy', 'error': str(e)}

        # Register health checks
        self.health_checker.register_component('database', database_health)
        self.health_checker.register_component('redis', redis_health)
        self.health_checker.register_component('data_feed', data_feed_health)
        self.health_checker.register_component('broker', broker_health)

    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        while self.is_monitoring:
            try:
                # Collect system metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                network = psutil.net_io_counters()

                # Update Prometheus metrics
                self.metrics_collector.cpu_usage.set(cpu_percent)
                self.metrics_collector.memory_usage.set(memory.percent)
                self.metrics_collector.disk_usage.set(disk.percent)

                # Create metrics snapshot
                metrics = SystemMetrics(
                    timestamp=datetime.now(),
                    cpu_usage=cpu_percent,
                    memory_usage=memory.percent,
                    disk_usage=disk.percent,
                    network_io={
                        'bytes_sent': network.bytes_sent,
                        'bytes_recv': network.bytes_recv
                    },
                    process_count=len(psutil.pids()),
                    database_connections=self._get_database_connections(),
                    redis_connections=self._get_redis_connections(),
                    api_response_time=self._get_api_response_time(),
                    trading_latency=self._get_trading_latency()
                )

                self.performance_history.append(metrics)

                # Trim history
                if len(self.performance_history) > self.max_history_length:
                    self.performance_history = self.performance_history[-self.max_history_length:]

                # Check thresholds and create alerts
                await self._check_metric_thresholds(metrics)

                # Log performance
                self.logger.log_performance({
                    'cpu_usage': cpu_percent,
                    'memory_usage': memory.percent,
                    'disk_usage': disk.percent
                })

                await asyncio.sleep(30)  # Collect every 30 seconds

            except Exception as e:
                self.monitoring_logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(30)

    async def _monitor_health(self):
        """Monitor component health"""
        while self.is_monitoring:
            try:
                # Check all components
                component_health = await self.health_checker.check_all_components()

                # Create alerts for unhealthy components
                for name, health in component_health.items():
                    if health.status == ComponentStatus.UNHEALTHY:
                        await self._create_alert(
                            component=name,
                            severity=MonitoringLevel.CRITICAL,
                            title=f"{name} Component Unhealthy",
                            message=f"Component {name} is unhealthy: {health.error_message}"
                        )
                    elif health.status == ComponentStatus.DEGRADED:
                        await self._create_alert(
                            component=name,
                            severity=MonitoringLevel.WARNING,
                            title=f"{name} Component Degraded",
                            message=f"Component {name} is degraded: {health.error_message}"
                        )

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.monitoring_logger.error(f"Error monitoring health: {e}")
                await asyncio.sleep(30)

    async def _check_metric_thresholds(self, metrics: SystemMetrics):
        """Check metrics against alert thresholds"""
        thresholds = self.alert_thresholds

        # CPU threshold
        cpu_threshold = thresholds.get('cpu_usage', 80)
        if metrics.cpu_usage > cpu_threshold:
            await self._create_alert(
                component='system',
                severity=MonitoringLevel.WARNING,
                title='High CPU Usage',
                message=f'CPU usage is {metrics.cpu_usage:.1f}%, above threshold of {cpu_threshold}%'
            )

        # Memory threshold
        memory_threshold = thresholds.get('memory_usage', 85)
        if metrics.memory_usage > memory_threshold:
            await self._create_alert(
                component='system',
                severity=MonitoringLevel.WARNING,
                title='High Memory Usage',
                message=f'Memory usage is {metrics.memory_usage:.1f}%, above threshold of {memory_threshold}%'
            )

        # Disk threshold
        disk_threshold = thresholds.get('disk_usage', 90)
        if metrics.disk_usage > disk_threshold:
            await self._create_alert(
                component='system',
                severity=MonitoringLevel.ERROR,
                title='High Disk Usage',
                message=f'Disk usage is {metrics.disk_usage:.1f}%, above threshold of {disk_threshold}%'
            )

        # Trading latency threshold
        latency_threshold = thresholds.get('trading_latency', 1.0)  # 1 second
        if metrics.trading_latency > latency_threshold:
            await self._create_alert(
                component='trading',
                severity=MonitoringLevel.WARNING,
                title='High Trading Latency',
                message=f'Trading latency is {metrics.trading_latency:.3f}s, above threshold of {latency_threshold}s'
            )

    async def _create_alert(self, component: str, severity: MonitoringLevel,
                           title: str, message: str):
        """Create a new alert"""
        try:
            alert_id = f"{component}_{severity.value}_{int(time.time())}"

            # Check if similar alert already exists
            existing_alert = None
            for alert in self.alerts:
                if (alert.component == component and
                    alert.title == title and
                    not alert.resolved):
                    existing_alert = alert
                    break

            if existing_alert:
                return  # Don't create duplicate alerts

            alert = Alert(
                alert_id=alert_id,
                component=component,
                severity=severity,
                title=title,
                message=message,
                timestamp=datetime.now()
            )

            self.alerts.append(alert)

            # Update metrics
            self.metrics_collector.alerts_total.labels(
                component=component,
                severity=severity.value
            ).inc()

            # Log alert
            self.logger.logger.log(
                severity.value.upper(),
                "Alert created",
                alert_id=alert_id,
                component=component,
                title=title,
                message=message
            )

            # Send through alert channels
            await self._send_alert_notifications(alert)

        except Exception as e:
            self.monitoring_logger.error(f"Error creating alert: {e}")

    async def _send_alert_notifications(self, alert: Alert):
        """Send alert through configured channels"""
        for channel in self.alert_channels:
            try:
                await channel.send_alert(alert)
            except Exception as e:
                self.monitoring_logger.error(f"Error sending alert notification: {e}")

    async def _process_alerts(self):
        """Process and manage alerts"""
        while self.is_monitoring:
            try:
                current_time = datetime.now()

                # Auto-resolve old alerts
                for alert in self.alerts:
                    if (not alert.resolved and
                        current_time - alert.timestamp > timedelta(hours=1)):
                        alert.resolved = True
                        alert.resolution_time = current_time

                # Clean up old resolved alerts
                cutoff_time = current_time - timedelta(days=7)
                self.alerts = [
                    alert for alert in self.alerts
                    if not alert.resolved or alert.timestamp > cutoff_time
                ]

                await asyncio.sleep(300)  # Process every 5 minutes

            except Exception as e:
                self.monitoring_logger.error(f"Error processing alerts: {e}")
                await asyncio.sleep(300)

    async def _export_metrics(self):
        """Export metrics to Prometheus"""
        while self.is_monitoring:
            try:
                # This would start a Prometheus metrics server
                # For now, we'll just log metrics export
                self.monitoring_logger.debug("Metrics exported to Prometheus")

                await asyncio.sleep(15)  # Export every 15 seconds

            except Exception as e:
                self.monitoring_logger.error(f"Error exporting metrics: {e}")
                await asyncio.sleep(15)

    async def _cleanup_old_data(self):
        """Clean up old monitoring data"""
        while self.is_monitoring:
            try:
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(days=30)

                # Clean up old performance history
                self.performance_history = [
                    metrics for metrics in self.performance_history
                    if metrics.timestamp > cutoff_time
                ]

                await asyncio.sleep(3600)  # Clean up every hour

            except Exception as e:
                self.monitoring_logger.error(f"Error cleaning up old data: {e}")
                await asyncio.sleep(3600)

    def _get_database_connections(self) -> int:
        """Get number of active database connections"""
        # This would query your database for connection count
        return 5  # Placeholder

    def _get_redis_connections(self) -> int:
        """Get number of active Redis connections"""
        # This would query Redis for connection count
        return 2  # Placeholder

    def _get_api_response_time(self) -> float:
        """Get average API response time"""
        # This would calculate from recent API requests
        return 0.05  # 50ms placeholder

    def _get_trading_latency(self) -> float:
        """Get current trading latency"""
        # This would calculate from recent orders
        return 0.1  # 100ms placeholder

    # Public API methods
    def record_order(self, broker: str, symbol: str, side: str, status: str):
        """Record an order for metrics"""
        self.metrics_collector.orders_total.labels(
            broker=broker,
            symbol=symbol,
            side=side,
            status=status
        ).inc()

    def record_trade(self, symbol: str, strategy: str):
        """Record a trade for metrics"""
        self.metrics_collector.trades_total.labels(
            symbol=symbol,
            strategy=strategy
        ).inc()

    def record_order_latency(self, broker: str, latency: float):
        """Record order execution latency"""
        self.metrics_collector.order_latency.labels(broker=broker).observe(latency)

    def update_pnl(self, realized: float, unrealized: float, total: float):
        """Update P&L metrics"""
        self.metrics_collector.pnl_gauge.labels(type='realized').set(realized)
        self.metrics_collector.pnl_gauge.labels(type='unrealized').set(unrealized)
        self.metrics_collector.pnl_gauge.labels(type='total').set(total)

    def update_portfolio_value(self, value: float):
        """Update portfolio value metric"""
        self.metrics_collector.portfolio_value.set(value)

    def record_market_data_latency(self, provider: str, symbol: str, latency: float):
        """Record market data latency"""
        self.metrics_collector.market_data_latency.labels(
            provider=provider,
            symbol=symbol
        ).observe(latency)

    def update_market_data_rate(self, provider: str, rate: float):
        """Update market data rate"""
        self.metrics_collector.market_data_rate.labels(provider=provider).set(rate)

    def record_error(self, component: str, error_type: str):
        """Record an error"""
        self.metrics_collector.errors_total.labels(
            component=component,
            error_type=error_type
        ).inc()

    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        overall_health = self.health_checker.get_overall_status()
        active_alerts = [alert for alert in self.alerts if not alert.resolved]

        uptime = datetime.now() - self.system_start_time

        return {
            'status': overall_health.value,
            'uptime_seconds': uptime.total_seconds(),
            'components': {
                name: {
                    'status': health.status.value,
                    'last_check': health.last_check.isoformat(),
                    'response_time': health.response_time,
                    'error': health.error_message
                }
                for name, health in self.health_checker.components.items()
            },
            'active_alerts': len(active_alerts),
            'critical_alerts': len([a for a in active_alerts if a.severity == MonitoringLevel.CRITICAL]),
            'last_metrics_update': self.last_metrics_collection.isoformat()
        }

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.performance_history:
            return {}

        recent_metrics = self.performance_history[-10:]  # Last 10 readings

        return {
            'current': {
                'cpu_usage': recent_metrics[-1].cpu_usage,
                'memory_usage': recent_metrics[-1].memory_usage,
                'disk_usage': recent_metrics[-1].disk_usage,
                'trading_latency': recent_metrics[-1].trading_latency
            },
            'average': {
                'cpu_usage': sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
                'memory_usage': sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
                'trading_latency': sum(m.trading_latency for m in recent_metrics) / len(recent_metrics)
            },
            'peak': {
                'cpu_usage': max(m.cpu_usage for m in recent_metrics),
                'memory_usage': max(m.memory_usage for m in recent_metrics),
                'trading_latency': max(m.trading_latency for m in recent_metrics)
            }
        }

# Configuration helper
def create_monitoring_config() -> Dict[str, Any]:
    """Create default monitoring configuration"""
    return {
        'logging': {
            'log_level': 'INFO',
            'log_format': 'json',
            'log_file': 'logs/trading.log'
        },
        'alert_thresholds': {
            'cpu_usage': 80,      # percent
            'memory_usage': 85,   # percent
            'disk_usage': 90,     # percent
            'trading_latency': 1.0,  # seconds
            'api_response_time': 2.0  # seconds
        },
        'max_history_length': 1000
    }