"""
Simple web interface for monitoring dashboard.
Provides basic visualization without requiring complex frameworks.
"""

try:
    from flask import Flask, render_template_string, jsonify, request
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

import json
import time
from datetime import datetime, timedelta

# HTML template for the dashboard
DASHBOARD_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Trading System Monitor</title>
    <meta http-equiv="refresh" content="30">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; margin: 10px 0; }
        .metric-name { color: #666; font-size: 0.9em; text-transform: uppercase; }
        .status-ok { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-critical { color: #e74c3c; }
        .alert-section { margin-top: 30px; }
        .alert-item { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .alert-active { background: #f8d7da; border-color: #f5c6cb; }
        .timestamp { font-size: 0.8em; color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Trading System Monitor</h1>
            <p>Last updated: {{ last_updated }}</p>
        </div>

        <div class="metric-grid">
            {% for name, data in system_metrics.items() %}
            <div class="metric-card">
                <div class="metric-name">{{ name.replace('system.', '').replace('.', ' ').title() }}</div>
                <div class="metric-value status-{{ data.status }}">
                    {{ "%.1f"|format(data.current) }}{% if 'usage' in name %}%{% endif %}
                </div>
                <div class="timestamp">{{ data.timestamp_str }}</div>
            </div>
            {% endfor %}
        </div>

        {% if trading_metrics %}
        <div class="alert-section">
            <h2>Trading Metrics</h2>
            <div class="metric-grid">
                {% for name, data in trading_metrics.items() %}
                <div class="metric-card">
                    <div class="metric-name">{{ name.replace('trading.', '').replace('.', ' ').title() }}</div>
                    <div class="metric-value">{{ "%.2f"|format(data.current) }}</div>
                    <div class="timestamp">{{ data.timestamp_str }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% if active_alerts %}
        <div class="alert-section">
            <h2>🚨 Active Alerts</h2>
            {% for alert in active_alerts %}
            <div class="alert-item alert-active">
                <strong>{{ alert.name }}</strong><br>
                {{ alert.message }}<br>
                <div class="timestamp">Triggered: {{ alert.triggered_at }}</div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% if recent_alerts %}
        <div class="alert-section">
            <h2>📋 Recent Alerts</h2>
            <table>
                <thead>
                    <tr>
                        <th>Alert</th>
                        <th>Triggered</th>
                        <th>Resolved</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    {% for alert in recent_alerts %}
                    <tr>
                        <td>{{ alert.name }}</td>
                        <td>{{ alert.triggered_at }}</td>
                        <td>{{ alert.resolved_at or 'Active' }}</td>
                        <td>{{ alert.duration or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
</body>
</html>
"""

def create_web_app(monitoring_system):
    """Create Flask web application for monitoring dashboard"""
    if not FLASK_AVAILABLE:
        raise ImportError("Flask is required for web interface")

    app = Flask(__name__)
    collector = monitoring_system["collector"]
    dashboard = monitoring_system["dashboard"]
    alert_manager = monitoring_system["alert_manager"]

    @app.route("/")
    def index():
        """Main dashboard page"""
        # Get system metrics
        system_metrics = dashboard.get_system_status()

        # Add formatted timestamps
        for metric_data in system_metrics.values():
            if "timestamp" in metric_data:
                metric_data["timestamp_str"] = datetime.fromtimestamp(
                    metric_data["timestamp"]
                ).strftime("%H:%M:%S")

        # Get trading metrics
        trading_metrics = dashboard.get_trading_metrics(hours=24)
        for metric_data in trading_metrics.values():
            if "current" in metric_data and "history" in metric_data:
                # Get timestamp from latest history entry
                if metric_data["history"]:
                    latest_ts = metric_data["history"][0]["timestamp"]
                    metric_data["timestamp_str"] = datetime.fromtimestamp(latest_ts).strftime("%H:%M:%S")

        # Get alerts
        active_alerts = _get_active_alerts(alert_manager)
        recent_alerts = _get_recent_alerts(collector)

        return render_template_string(
            DASHBOARD_TEMPLATE,
            system_metrics=system_metrics,
            trading_metrics=trading_metrics,
            active_alerts=active_alerts,
            recent_alerts=recent_alerts,
            last_updated=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

    @app.route("/api/metrics")
    def api_metrics():
        """API endpoint for metrics data"""
        system_metrics = dashboard.get_system_status()
        trading_metrics = dashboard.get_trading_metrics(hours=int(request.args.get("hours", 24)))

        return jsonify({
            "system": system_metrics,
            "trading": trading_metrics,
            "timestamp": time.time()
        })

    @app.route("/api/alerts")
    def api_alerts():
        """API endpoint for alerts"""
        active_alerts = _get_active_alerts(alert_manager)
        recent_alerts = _get_recent_alerts(collector)

        return jsonify({
            "active": active_alerts,
            "recent": recent_alerts,
            "timestamp": time.time()
        })

    @app.route("/health")
    def health():
        """Health check endpoint"""
        return jsonify({"status": "ok", "timestamp": time.time()})

    return app

def _get_active_alerts(alert_manager):
    """Get currently active alerts"""
    active = []
    for alert_name, triggered_time in alert_manager.active_alerts.items():
        active.append({
            "name": alert_name,
            "triggered_at": datetime.fromtimestamp(triggered_time).strftime("%Y-%m-%d %H:%M:%S"),
            "message": f"Alert {alert_name} is active"
        })
    return active

def _get_recent_alerts(collector):
    """Get recent alert history"""
    import sqlite3

    conn = sqlite3.connect(collector.db_path)
    cursor = conn.cursor()

    # Get alerts from last 24 hours
    yesterday = time.time() - (24 * 3600)

    cursor.execute('''
        SELECT alert_name, triggered_at, resolved_at, message
        FROM alerts
        WHERE triggered_at > ?
        ORDER BY triggered_at DESC
        LIMIT 50
    ''', (yesterday,))

    alerts = []
    for row in cursor.fetchall():
        alert_name, triggered_at, resolved_at, message = row

        triggered_str = datetime.fromtimestamp(triggered_at).strftime("%Y-%m-%d %H:%M:%S")
        resolved_str = None
        duration = None

        if resolved_at:
            resolved_str = datetime.fromtimestamp(resolved_at).strftime("%Y-%m-%d %H:%M:%S")
            duration_seconds = resolved_at - triggered_at
            duration = f"{duration_seconds//60:.0f}m {duration_seconds%60:.0f}s"

        alerts.append({
            "name": alert_name,
            "triggered_at": triggered_str,
            "resolved_at": resolved_str,
            "duration": duration,
            "message": message
        })

    conn.close()
    return alerts

# Standalone monitoring server without Flask
class SimpleHTTPMonitor:
    """Simple HTTP server for monitoring without Flask dependency"""

    def __init__(self, monitoring_system, host="127.0.0.1", port=8080):
        self.monitoring_system = monitoring_system
        self.host = host
        self.port = port

    def start(self):
        """Start simple HTTP server"""
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import json

        class MonitoringHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == "/":
                    self.send_response(200)
                    self.send_header("Content-type", "text/html")
                    self.end_headers()
                    self.wfile.write(self._get_simple_dashboard().encode())
                elif self.path == "/api/metrics":
                    self.send_response(200)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    metrics = self._get_metrics_json()
                    self.wfile.write(json.dumps(metrics).encode())
                elif self.path == "/health":
                    self.send_response(200)
                    self.send_header("Content-type", "application/json")
                    self.end_headers()
                    self.wfile.write(json.dumps({"status": "ok"}).encode())
                else:
                    self.send_response(404)
                    self.end_headers()

            def _get_simple_dashboard(self):
                dashboard = self.server.monitoring_system["dashboard"]
                system_metrics = dashboard.get_system_status()

                html = "<html><head><title>Trading Monitor</title></head><body>"
                html += "<h1>Trading System Monitor</h1>"
                html += f"<p>Last updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"

                for name, data in system_metrics.items():
                    status_color = {"ok": "green", "warning": "orange", "critical": "red"}.get(data.get("status", "ok"), "black")
                    html += f"<div style='margin: 10px; padding: 10px; border: 1px solid {status_color};'>"
                    html += f"<h3>{name}</h3>"
                    html += f"<p style='color: {status_color}; font-size: 24px;'>{data['current']:.1f}</p>"
                    html += "</div>"

                html += "</body></html>"
                return html

            def _get_metrics_json(self):
                dashboard = self.server.monitoring_system["dashboard"]
                return {
                    "system": dashboard.get_system_status(),
                    "trading": dashboard.get_trading_metrics(),
                    "timestamp": time.time()
                }

        server = HTTPServer((self.host, self.port), MonitoringHandler)
        server.monitoring_system = self.monitoring_system

        print(f"Starting simple monitoring server on http://{self.host}:{self.port}")
        server.serve_forever()