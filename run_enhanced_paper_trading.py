#!/usr/bin/env python3
"""
Enhanced AstroA Paper Trading with Full API Integration
- Live Binance crypto data
- DeepSeek AI market analysis  
- Alpha Vantage data fallback
- News sentiment integration
- Alpaca paper trading execution
"""

import asyncio
import argparse
import signal
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agents.paper_trading.paper_trading_engine import PaperTradingEngine
from config.paper_trading_config import paper_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/enhanced_paper_trading_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class EnhancedPaperTradingSession:
    def __init__(self):
        self.engine = None
        self.is_running = False
        self.start_time = None
        self.session_duration = None

    async def start_session(self, duration_hours: float = 8.0):
        """Start enhanced paper trading session"""
        
        print("🚀 Starting Enhanced AstroA Paper Trading Session")
        print("=" * 60)
        print(f"💰 Initial Cash: ${paper_config.initial_cash:,.2f}")
        print(f"📊 Max Positions: {paper_config.max_positions}")
        print(f"🎯 Tradable Symbols: {', '.join(paper_config.tradable_symbols[:5])}...")
        print(f"⚡ Update Interval: {paper_config.data_update_interval}s")
        print(f"🤖 AI Analysis: DeepSeek AI Enabled")
        print(f"📈 Live Data: Binance + Alpha Vantage + Alpaca")
        print("=" * 60)
        
        try:
            # Initialize enhanced trading engine
            self.engine = PaperTradingEngine()
            
            # Set session parameters
            self.start_time = datetime.now()
            self.session_duration = timedelta(hours=duration_hours)
            self.is_running = True
            
            print(f"📅 Session duration: {duration_hours} hours")
            print(f"🕐 Session will end at: {(self.start_time + self.session_duration).strftime('%H:%M:%S')}")
            
            # Start the enhanced trading session
            await self.engine.start_paper_trading()
            
        except KeyboardInterrupt:
            logger.info("Session interrupted by user")
            await self.stop_session()
        except Exception as e:
            logger.error(f"Session error: {e}")
            await self.stop_session()

    async def stop_session(self):
        """Stop the trading session and generate report"""
        if self.engine:
            self.is_running = False
            await self.engine.stop_paper_trading()
            
            # Generate enhanced session report
            await self._generate_enhanced_report()
            
            print("\n🛑 Enhanced paper trading session ended")

    async def _generate_enhanced_report(self):
        """Generate comprehensive session report"""
        try:
            if not self.engine:
                return
            
            end_time = datetime.now()
            session_duration = end_time - self.start_time if self.start_time else timedelta(0)
            
            report = {
                'session_info': {
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'end_time': end_time.isoformat(),
                    'duration_hours': session_duration.total_seconds() / 3600,
                    'engine_type': 'Enhanced Multi-API'
                },
                'performance': {
                    'initial_portfolio': paper_config.initial_cash,
                    'final_portfolio': self.engine.portfolio_value,
                    'total_pnl': self.engine.total_pnl,
                    'daily_pnl': self.engine.daily_pnl,
                    'total_trades': len(self.engine.trade_history),
                    'positions': len(self.engine.positions)
                },
                'api_usage': {
                    'binance_calls': getattr(self.engine, 'binance_calls', 0),
                    'alpha_vantage_calls': getattr(self.engine, 'alpha_vantage_calls', 0),
                    'deepseek_calls': getattr(self.engine, 'deepseek_calls', 0),
                    'alpaca_calls': getattr(self.engine, 'alpaca_calls', 0)
                },
                'ai_analysis': getattr(self.engine, 'ai_market_analysis', {}),
                'trades': self.engine.trade_history,
                'positions': self.engine.positions,
                'performance_metrics': self.engine.performance_metrics
            }
            
            # Save report
            report_file = f"data/enhanced_paper_trading_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            import json
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            # Print summary
            print(f"\n📊 Enhanced Session Summary")
            print(f"⏱️  Duration: {session_duration}")
            print(f"💰 Final Portfolio: ${self.engine.portfolio_value:,.2f}")
            print(f"📈 Total P&L: ${self.engine.total_pnl:+,.2f}")
            print(f"🔄 Total Trades: {len(self.engine.trade_history)}")
            print(f"📋 Open Positions: {len(self.engine.positions)}")
            print(f"💾 Report saved: {report_file}")
            
            if hasattr(self.engine, 'ai_market_analysis') and self.engine.ai_market_analysis:
                print(f"🤖 AI Analysis: Available")
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")

def setup_signal_handlers(session):
    """Setup signal handlers for graceful shutdown"""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(session.stop_session())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

async def main():
    parser = argparse.ArgumentParser(description='Enhanced AstroA Paper Trading with Full API Integration')
    parser.add_argument('--duration', type=float, default=8.0, 
                       help='Trading session duration in hours (default: 8.0)')
    parser.add_argument('--test-apis', action='store_true',
                       help='Test all API connections before starting')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Test API connections if requested
    if args.test_apis:
        print("🔍 Testing API connections...")
        
        # Test Alpaca
        try:
            from test_alpaca_connection import main as test_alpaca
            test_alpaca()
        except Exception as e:
            print(f"❌ Alpaca test failed: {e}")
        
        # Test Binance
        try:
            import requests
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
            if response.status_code == 200:
                print("✅ Binance API: Connected")
            else:
                print("❌ Binance API: Failed")
        except Exception as e:
            print(f"❌ Binance test failed: {e}")
        
        print("API tests completed.\n")
    
    # Create and start enhanced session
    session = EnhancedPaperTradingSession()
    setup_signal_handlers(session)
    
    try:
        await session.start_session(args.duration)
    except Exception as e:
        logger.error(f"Session failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
