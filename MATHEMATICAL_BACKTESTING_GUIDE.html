<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroA Mathematical Backtesting Guide - Test Your Mathematical Ideas</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 10px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .nav h3 {
            margin-bottom: 15px;
            color: #2a5298;
        }

        .nav ul {
            list-style: none;
            columns: 2;
        }

        .nav li {
            margin-bottom: 8px;
        }

        .nav a {
            color: #1e3c72;
            text-decoration: none;
            font-weight: 500;
        }

        .nav a:hover {
            color: #2a5298;
            text-decoration: underline;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fdfdfd;
        }

        .section h2 {
            color: #1e3c72;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #2a5298;
            margin-bottom: 15px;
            margin-top: 25px;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .math-focus {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .math-focus h3 {
            color: white;
            margin-bottom: 15px;
        }

        .api-card {
            background: #f8f9fa;
            border: 2px solid #2a5298;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .api-card h4 {
            color: #1e3c72;
            margin-bottom: 10px;
        }

        .api-link {
            display: inline-block;
            background: #2a5298;
            color: white !important;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px 0;
            font-weight: bold;
        }

        .api-link:hover {
            background: #1e3c72;
        }

        .step {
            background: #f8f9fa;
            border-left: 4px solid #2a5298;
            padding: 20px;
            margin: 20px 0;
        }

        .step h4 {
            color: #1e3c72;
            margin-bottom: 10px;
        }

        .formula-box {
            background: #e8f4fd;
            border: 2px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2a5298;
        }

        .critical {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 AstroA Mathematical Backtesting Guide</h1>
            <p>Test Your Advanced Mathematical Trading Ideas</p>
            <p><em>Calculus, Probability, Matrix Math, AI Analysis - Your Math, Your Rules</em></p>
        </div>

        <div class="math-focus">
            <h3>🎯 What This Guide Is Really About</h3>
            <p><strong>This is NOT about simple strategies.</strong> This is about testing YOUR mathematical ideas:</p>
            <ul style="margin-top: 10px;">
                <li>🧮 <strong>Calculus-Based Analysis:</strong> Derivatives, momentum, acceleration</li>
                <li>📊 <strong>Probability Theory:</strong> Market state probabilities, conditional analysis</li>
                <li>🔢 <strong>Matrix Mathematics:</strong> Portfolio optimization, correlation analysis</li>
                <li>🔍 <strong>Pattern Recognition:</strong> Mathematical pattern detection</li>
                <li>🤖 <strong>AI Integration:</strong> Mathematical result interpretation</li>
            </ul>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#api-setup">🔑 API Setup (REQUIRED)</a></li>
                <li><a href="#math-architecture">🧮 Mathematical Architecture</a></li>
                <li><a href="#testing-math">🧪 Testing Your Math</a></li>
                <li><a href="#custom-formulas">📐 Custom Mathematical Formulas</a></li>
                <li><a href="#backtest-execution">⚡ Backtest Execution</a></li>
                <li><a href="#reading-math-results">📊 Reading Mathematical Results</a></li>
                <li><a href="#advanced-math">🎯 Advanced Mathematical Testing</a></li>
            </ul>
        </div>

        <section id="api-setup" class="section">
            <h2>🔑 API Keys Setup (REQUIRED for Backtesting)</h2>

            <div class="critical">
                <strong>⚠️ CRITICAL:</strong> You MUST have these API keys to backtest your mathematical ideas. The system needs real market data to test your formulas!
            </div>

            <h3>📋 Required API Keys & Where to Get Them</h3>

            <div class="api-card">
                <h4>🤖 DeepSeek AI (REQUIRED - Mathematical Analysis)</h4>
                <p><strong>Purpose:</strong> AI interpretation of your mathematical results</p>
                <p><strong>Cost:</strong> Pay-per-use (very affordable)</p>
                <a href="https://platform.deepseek.com/" class="api-link" target="_blank">Get DeepSeek API Key</a>
                <div class="code-block">
# In your .env file:
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com</div>
            </div>

            <div class="api-card">
                <h4>📰 NewsAPI (REQUIRED - Sentiment Analysis)</h4>
                <p><strong>Purpose:</strong> News sentiment for mathematical probability calculations</p>
                <p><strong>Cost:</strong> Free tier available (500 requests/day)</p>
                <a href="https://newsapi.org/register" class="api-link" target="_blank">Get News API Key</a>
                <div class="code-block">
# In your .env file:
NEWS_API_KEY=your_news_api_key_here</div>
            </div>

            <div class="api-card">
                <h4>📈 Alpha Vantage (REQUIRED - Cross-Asset Data)</h4>
                <p><strong>Purpose:</strong> Stock/forex data for correlation matrix calculations</p>
                <p><strong>Cost:</strong> Free tier available (25 requests/day)</p>
                <a href="https://www.alphavantage.co/support/#api-key" class="api-link" target="_blank">Get Alpha Vantage API Key</a>
                <div class="code-block">
# In your .env file:
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here</div>
            </div>

            <div class="api-card">
                <h4>💱 Exchange APIs (OPTIONAL - More Data)</h4>
                <p><strong>Purpose:</strong> More crypto data for mathematical analysis</p>
                <p><strong>Binance:</strong> Free API access</p>
                <a href="https://www.binance.com/en/binance-api" class="api-link" target="_blank">Get Binance API Key</a>
                <p><strong>Coinbase:</strong> Free API access</p>
                <a href="https://docs.cloud.coinbase.com/sign-in-with-coinbase/docs/api-key-authentication" class="api-link" target="_blank">Get Coinbase API Key</a>
                <div class="code-block">
# In your .env file:
BINANCE_API_KEY=your_binance_key_here
BINANCE_SECRET_KEY=your_binance_secret_here
COINBASE_API_KEY=your_coinbase_key_here
COINBASE_SECRET_KEY=your_coinbase_secret_here</div>
            </div>

            <h3>⚙️ Setting Up Your .env File</h3>
            <div class="step">
                <h4>Step 1: Copy the template</h4>
                <div class="code-block">cp .env.example .env</div>
            </div>

            <div class="step">
                <h4>Step 2: Edit with your API keys</h4>
                <div class="code-block">
# Essential for mathematical backtesting:
DEEPSEEK_API_KEY=sk-your-actual-deepseek-key
NEWS_API_KEY=your-actual-news-api-key
ALPHA_VANTAGE_API_KEY=your-actual-alpha-vantage-key

# Database settings:
DB_HOST=localhost
DB_NAME=mathematical_trading
DB_USER=trading_user
DB_PASSWORD=hejhej

# Optional exchange APIs:
BINANCE_API_KEY=your_binance_key_if_you_have_one
COINBASE_API_KEY=your_coinbase_key_if_you_have_one</div>
            </div>

            <div class="warning">
                <strong>💰 Cost Estimate:</strong><br>
                • DeepSeek: ~$0.10-1.00 per day of testing<br>
                • NewsAPI: Free (500 requests/day)<br>
                • Alpha Vantage: Free (25 requests/day)<br>
                • <strong>Total: Under $1/day for comprehensive testing</strong>
            </div>
        </section>

        <section id="math-architecture" class="section">
            <h2>🧮 Mathematical Architecture - The Real System</h2>

            <div class="info">
                <strong>🎯 Understanding the REAL AstroA:</strong><br>
                This is a <strong>Mathematical Trading Engine</strong>, not simple strategies. The "mean reversion" and "momentum" you see are just execution templates. The real intelligence is in the mathematical analysis.
            </div>

            <h3>🔄 Mathematical Analysis Pipeline</h3>
            <div class="formula-box">
📡 Raw Data → 🧮 Mathematical Engine → 🤖 AI Interpretation → 📈 Strategy Selection → ⚖️ Risk Management

Where Mathematical Engine includes:
├── Calculus Formulas (derivatives, integrals, momentum)
├── Probability Theory (distributions, conditional probabilities)
├── Matrix Mathematics (correlations, optimizations)
├── Statistical Analysis (50+ indicators)
├── Pattern Recognition (mathematical patterns)
└── Risk Analysis (VaR, portfolio metrics)
            </div>

            <h3>🎯 Core Mathematical Components</h3>

            <table>
                <tr>
                    <th>Component</th>
                    <th>Mathematical Focus</th>
                    <th>File Location</th>
                </tr>
                <tr>
                    <td><strong>Calculus Formulas</strong></td>
                    <td>Price derivatives, momentum, acceleration</td>
                    <td>shared/mathematical/formulas.py</td>
                </tr>
                <tr>
                    <td><strong>Probability Formulas</strong></td>
                    <td>Market state probabilities, success rates</td>
                    <td>shared/mathematical/formulas.py</td>
                </tr>
                <tr>
                    <td><strong>Matrix Formulas</strong></td>
                    <td>Portfolio optimization, correlations</td>
                    <td>shared/mathematical/formulas.py</td>
                </tr>
                <tr>
                    <td><strong>Statistical Analyzer</strong></td>
                    <td>Technical indicators, moving averages</td>
                    <td>agents/mathematical_engine/analyzers/statistical/</td>
                </tr>
                <tr>
                    <td><strong>Correlation Analyzer</strong></td>
                    <td>Cross-asset correlations, lead-lag</td>
                    <td>agents/mathematical_engine/analyzers/correlation/</td>
                </tr>
                <tr>
                    <td><strong>Pattern Analyzer</strong></td>
                    <td>Mathematical pattern detection</td>
                    <td>agents/mathematical_engine/analyzers/pattern/</td>
                </tr>
                <tr>
                    <td><strong>Risk Analyzer</strong></td>
                    <td>VaR, portfolio risk, drawdowns</td>
                    <td>agents/mathematical_engine/analyzers/risk/</td>
                </tr>
            </table>

            <h3>🔬 Mathematical Analysis Examples</h3>

            <div class="formula-box">
<strong>Calculus-Based Price Momentum:</strong>
f'(x) = lim(h→0) [f(x+h) - f(x)] / h

<strong>Probability of Reaching Target Price:</strong>
P(target) = 1 - Φ(|z|) where z = (required_return - mean_return) / std_return

<strong>Portfolio Optimization (Matrix Math):</strong>
w* = λ(Σ⁻¹1) + γ(Σ⁻¹μ)
where λ = (C - μₚB)/D, γ = (μₚA - B)/D
            </div>
        </section>

        <section id="testing-math" class="section">
            <h2>🧪 Testing Your Mathematical Ideas</h2>

            <div class="math-focus">
                <h3>🎯 This is Where YOU Test YOUR Math</h3>
                <p>The system provides the framework - you provide the mathematical ideas. Here's how to test them:</p>
            </div>

            <h3>Method 1: Test Mathematical Engine Components</h3>
            <div class="step">
                <h4>Test All Mathematical Components</h4>
                <div class="code-block">python test_mathematical_engine.py</div>
                <p><strong>What this tests:</strong></p>
                <ul>
                    <li>✅ Calculus formulas (derivatives, momentum, acceleration)</li>
                    <li>✅ Probability calculations (market states, success rates)</li>
                    <li>✅ Matrix operations (correlations, portfolio optimization)</li>
                    <li>✅ Statistical analysis (50+ indicators)</li>
                    <li>✅ Pattern recognition algorithms</li>
                    <li>✅ Risk analysis calculations</li>
                </ul>
            </div>

            <h3>Method 2: Test Individual Mathematical Components</h3>

            <div class="step">
                <h4>Test Your Calculus Formulas</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import CalculusFormulas
import pandas as pd
import numpy as np

# Test your calculus ideas
calc = CalculusFormulas()
test_prices = pd.Series([100, 101, 103, 102, 105, 107, 106, 108, 110, 109])

# Test price momentum using derivatives
momentum = calc.derivative_price_momentum(test_prices)
print('Price Momentum (First Derivative):')
print(momentum.dropna())

# Test price acceleration using second derivatives
acceleration = calc.second_derivative_acceleration(test_prices)
print('\nPrice Acceleration (Second Derivative):')
print(acceleration.dropna())

# Test resistance levels using tangent lines
resistance = calc.tangent_line_resistance(test_prices, 5)
print(f'\nTangent Line Analysis: {resistance}')
"</div>
            </div>

            <div class="step">
                <h4>Test Your Probability Formulas</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import ProbabilityFormulas
import pandas as pd
import numpy as np

# Test your probability ideas
prob = ProbabilityFormulas()
test_prices = pd.Series(np.random.normal(100, 5, 100).cumsum() + 1000)

# Test probability of reaching target price
target_analysis = prob.normal_distribution_price_probability(test_prices, 1150)
print('Target Price Probability Analysis:')
for key, value in target_analysis.items():
    print(f'{key}: {value}')

# Test trading success probability
success_prob = prob.binomial_trading_success(0.6, 10, 7)
print(f'\nProbability of 7+ wins in 10 trades: {success_prob:.3f}')
"</div>
            </div>

            <div class="step">
                <h4>Test Your Matrix Operations</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import MatrixFormulas
import pandas as pd
import numpy as np

# Test your matrix ideas
matrix = MatrixFormulas()

# Create test return data
np.random.seed(42)
returns_data = pd.DataFrame({
    'BTC': np.random.normal(0.001, 0.03, 100),
    'ETH': np.random.normal(0.0008, 0.025, 100),
    'ADA': np.random.normal(0.0012, 0.02, 100)
})

# Test correlation matrix analysis
corr_analysis = matrix.correlation_matrix_analysis(returns_data)
print('Correlation Matrix Analysis:')
print(f'Determinant: {corr_analysis[\"determinant\"]:.6f}')
print(f'Max Correlation: {corr_analysis[\"max_correlation\"]:.3f}')
print(f'Min Correlation: {corr_analysis[\"min_correlation\"]:.3f}')

# Test portfolio optimization
target_return = 0.01  # 1% target return
portfolio_opt = matrix.portfolio_optimization_matrix(returns_data, target_return)
if 'optimal_weights' in portfolio_opt:
    print(f'\nOptimal Portfolio Weights:')
    for i, weight in enumerate(portfolio_opt['optimal_weights']):
        print(f'{returns_data.columns[i]}: {weight:.3f}')
"</div>
            </div>

            <h3>Method 3: Test Complete Mathematical Analysis</h3>
            <div class="step">
                <h4>Run Integrated Mathematical Analysis</h4>
                <div class="code-block">
python -c "
from shared.mathematical.formulas import IntegratedMathematicalAnalysis
import pandas as pd
import numpy as np

# Test integrated mathematical analysis
integrated = IntegratedMathematicalAnalysis()

# Create test market data
np.random.seed(42)
dates = pd.date_range('2023-01-01', periods=200, freq='H')
price_data = pd.DataFrame({
    'close_price': 100 * np.exp(np.cumsum(np.random.normal(0.001, 0.02, 200))),
    'volume': np.random.lognormal(10, 1, 200)
}, index=dates)

# Run comprehensive analysis
results = integrated.comprehensive_market_analysis(price_data)

print('📊 INTEGRATED MATHEMATICAL ANALYSIS RESULTS:')
print('=' * 50)

if 'calculus' in results:
    print('\n🧮 CALCULUS ANALYSIS:')
    momentum = results['calculus']['momentum'].dropna()
    print(f'   Latest Momentum: {momentum.iloc[-1]:.6f}')

    acceleration = results['calculus']['acceleration'].dropna()
    print(f'   Latest Acceleration: {acceleration.iloc[-1]:.6f}')

if 'probability' in results:
    print('\n📊 PROBABILITY ANALYSIS:')
    prob_data = results['probability']
    print(f'   Target Price: ${prob_data[\"target_price\"]:.2f}')
    print(f'   Probability: {prob_data[\"probability\"]:.3f}')

if 'indicators' in results:
    print('\n📈 ADVANCED INDICATORS:')
    momentum_osc = results['indicators']['momentum_oscillator'].dropna()
    if not momentum_osc.empty:
        print(f'   Momentum Oscillator: {momentum_osc.iloc[-1]:.3f}')

    trend_strength = results['indicators']['trend_strength'].dropna()
    if not trend_strength.empty:
        print(f'   Trend Strength: {trend_strength.iloc[-1]:.3f}')

print('\n✅ Mathematical analysis completed successfully!')
"</div>
            </div>
        </section>

        <section id="custom-formulas" class="section">
            <h2>📐 Adding Your Custom Mathematical Formulas</h2>

            <div class="math-focus">
                <h3>🎯 This is Where You Add YOUR Mathematical Ideas</h3>
                <p>The system is designed for you to add your own formulas. Here's how:</p>
            </div>

            <h3>Adding Custom Calculus Formulas</h3>
            <div class="step">
                <h4>Edit: shared/mathematical/formulas.py</h4>
                <div class="code-block">
# Add to CalculusFormulas class:

@staticmethod
def your_custom_derivative_formula(prices: pd.Series, window: int = 10) -> pd.Series:
    """Your custom mathematical idea using calculus"""
    # Example: Higher-order derivative for trend changes
    first_deriv = prices.diff()
    second_deriv = first_deriv.diff()
    third_deriv = second_deriv.diff()

    # Your mathematical logic here
    trend_change_signal = third_deriv.rolling(window).mean()
    return trend_change_signal

@staticmethod
def your_integral_based_indicator(volumes: pd.Series, prices: pd.Series) -> pd.Series:
    """Your custom integral-based volume analysis"""
    # Example: Volume-weighted price accumulation
    volume_price_product = volumes * prices
    accumulated_value = volume_price_product.rolling(20).sum()
    accumulated_volume = volumes.rolling(20).sum()

    # Volume-weighted average price using integration principles
    vwap = accumulated_value / accumulated_volume
    return vwap
                </div>
            </div>

            <h3>Adding Custom Probability Formulas</h3>
            <div class="step">
                <h4>Add to ProbabilityFormulas class:</h4>
                <div class="code-block">
@staticmethod
def your_custom_probability_model(returns: pd.Series, market_state: str) -> Dict:
    """Your custom probability model"""
    # Example: Regime-dependent probability analysis
    if market_state == 'volatile':
        # Use different distribution parameters for volatile markets
        volatility_multiplier = 1.5
    else:
        volatility_multiplier = 1.0

    mean_return = returns.mean()
    std_return = returns.std() * volatility_multiplier

    # Your probability calculations
    prob_positive = 1 - stats.norm.cdf(0, mean_return, std_return)
    prob_extreme_move = 2 * (1 - stats.norm.cdf(2, 0, 1))  # |z| > 2

    return {
        'prob_positive_return': prob_positive,
        'prob_extreme_move': prob_extreme_move,
        'market_regime': market_state,
        'adjusted_volatility': std_return
    }
                </div>
            </div>

            <h3>Adding Custom Matrix Operations</h3>
            <div class="step">
                <h4>Add to MatrixFormulas class:</h4>
                <div class="code-block">
@staticmethod
def your_custom_matrix_analysis(returns_df: pd.DataFrame) -> Dict:
    """Your custom matrix-based analysis"""
    # Example: Principal Component Analysis for factor extraction
    correlation_matrix = returns_df.corr()
    eigenvalues, eigenvectors = np.linalg.eig(correlation_matrix)

    # Sort by eigenvalue magnitude
    idx = eigenvalues.argsort()[::-1]
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]

    # Calculate explained variance
    explained_variance = eigenvalues / eigenvalues.sum()

    # Your mathematical insights
    market_factor_strength = explained_variance[0]  # First principal component
    diversification_potential = 1 - market_factor_strength

    return {
        'eigenvalues': eigenvalues,
        'market_factor_strength': market_factor_strength,
        'diversification_potential': diversification_potential,
        'explained_variance': explained_variance,
        'principal_components': eigenvectors
    }
                </div>
            </div>

            <h3>Testing Your Custom Formulas</h3>
            <div class="step">
                <h4>Test Your New Mathematical Ideas</h4>
                <div class="code-block">
# After adding your formulas, test them:
python -c "
from shared.mathematical.formulas import CalculusFormulas, ProbabilityFormulas, MatrixFormulas
import pandas as pd
import numpy as np

# Test your custom calculus formula
calc = CalculusFormulas()
test_prices = pd.Series(np.random.normal(100, 5, 100).cumsum() + 1000)
your_result = calc.your_custom_derivative_formula(test_prices)
print('Your Custom Calculus Result:', your_result.tail())

# Test your custom probability formula
prob = ProbabilityFormulas()
test_returns = test_prices.pct_change().dropna()
your_prob_result = prob.your_custom_probability_model(test_returns, 'volatile')
print('Your Custom Probability Result:', your_prob_result)

# Test your custom matrix formula
matrix = MatrixFormulas()
returns_data = pd.DataFrame({
    'Asset1': np.random.normal(0.001, 0.02, 100),
    'Asset2': np.random.normal(0.0008, 0.025, 100)
})
your_matrix_result = matrix.your_custom_matrix_analysis(returns_data)
print('Your Custom Matrix Result:', your_matrix_result['market_factor_strength'])
"</div>
            </div>
        </section>

        <section id="backtest-execution" class="section">
            <h2>⚡ Full Mathematical Backtest Execution</h2>

            <div class="critical">
                <strong>🔑 PREREQUISITE:</strong> Make sure you have set up your API keys in the .env file (see API Setup section above)
            </div>

            <h3>Step 1: Verify System Setup</h3>
            <div class="step">
                <h4>Check Mathematical Engine</h4>
                <div class="code-block">python verify_trading_system.py</div>
                <p><strong>Expected output:</strong> All checkmarks for mathematical components</p>
            </div>

            <h3>Step 2: Test Mathematical Components</h3>
            <div class="step">
                <h4>Run Mathematical Engine Test</h4>
                <div class="code-block">python test_mathematical_engine.py</div>
                <p><strong>What this tests:</strong></p>
                <ul>
                    <li>✅ All mathematical formulas (calculus, probability, matrix)</li>
                    <li>✅ Statistical analysis (50+ indicators)</li>
                    <li>✅ Correlation analysis (cross-asset relationships)</li>
                    <li>✅ Pattern recognition (mathematical patterns)</li>
                    <li>✅ Risk analysis (VaR, portfolio metrics)</li>
                    <li>✅ Database integration</li>
                    <li>✅ Performance benchmarks</li>
                </ul>
            </div>

            <h3>Step 3: Collect Data for Mathematical Analysis</h3>
            <div class="step">
                <h4>Run Data Collection with Mathematical Focus</h4>
                <div class="code-block">python main.py</div>
                <p><strong>What this collects:</strong></p>
                <ul>
                    <li>📊 Multi-timeframe crypto data (1m, 5m, 1h, 4h, 1d)</li>
                    <li>📰 News sentiment data (for probability calculations)</li>
                    <li>📈 Cross-asset correlations (stocks, forex, commodities)</li>
                    <li>🧮 Processed mathematical indicators</li>
                    <li>🤖 AI interpretation of mathematical results</li>
                </ul>
            </div>

            <h3>Step 4: Run Mathematical Analysis Backtest</h3>
            <div class="step">
                <h4>Execute Mathematical Trading Strategy Test</h4>
                <div class="code-block">python test_trading_strategy_agent.py</div>
                <p><strong>This tests your mathematical ideas in trading context:</strong></p>
                <ul>
                    <li>🧮 Mathematical analysis feeding into strategies</li>
                    <li>📊 Statistical indicators influencing decisions</li>
                    <li>🔢 Matrix-based position sizing</li>
                    <li>📈 Probability-based entry/exit timing</li>
                    <li>⚖️ Mathematical risk management</li>
                </ul>
            </div>

            <h3>Step 5: Continuous Mathematical Backtesting</h3>
            <div class="step">
                <h4>Run Ongoing Mathematical Analysis</h4>
                <div class="code-block">
# For continuous testing of your mathematical ideas:
while true; do
    echo "🧮 Starting mathematical analysis cycle at $(date)"
    python test_mathematical_engine.py
    echo "📊 Testing trading with mathematical inputs..."
    python test_trading_strategy_agent.py
    echo "⏰ Mathematical backtest completed. Waiting 1 hour..."
    sleep 3600
done</div>
            </div>
        </section>

        <section id="reading-math-results" class="section">
            <h2>📊 Reading Your Mathematical Backtest Results</h2>

            <h3>🧮 Mathematical Analysis Results</h3>

            <div class="step">
                <h4>Understanding Mathematical Engine Output</h4>
                <div class="code-block">
=== Mathematical Engine - Comprehensive Test Suite ===

📐 Mathematical Formulas: PASSED
   ✅ Calculus formulas: derivatives and momentum calculated
   ✅ Probability analysis: 0.234 chance of reaching target
   ✅ Matrix formulas: correlation analysis completed

🧮 Statistical Analyzer: PASSED
   ✅ Statistical analysis completed for 847 data points
   ✅ Current price: $45,234.56
   ✅ Volatility: 3.45%
   ✅ Moving averages calculated: 6 indicators
   ✅ Technical indicators calculated: 12 indicators

🔗 Correlation Analyzer: PASSED
   ✅ Correlation analysis completed for 3 assets
   ✅ Average correlation: 0.456
   ✅ Cross-correlation analysis completed, best lag: 2

🔍 Pattern Analyzer: PASSED
   ✅ Pattern analysis completed for 300 periods
   ✅ Candlestick patterns detected: 23 total patterns
   ✅ Trend analysis: upward with strength 0.6789

🛡️ Risk Analyzer: PASSED
   ✅ Risk analysis completed for 3 assets
   ✅ Portfolio volatility: 12.34%
   ✅ Sharpe ratio: 1.456
   ✅ Max drawdown: 8.90%
   ✅ VaR (95%): -0.0234

🎯 Integrated Analysis: PASSED
   ✅ Integrated analysis completed
   ✅ Calculus analysis: momentum and acceleration calculated
   ✅ Advanced indicators: momentum oscillator and trend strength

📊 DATABASE INTEGRATION: PASSED
   ✅ Database connection successful
   ✅ Market data available: 15,234 records

🤖 Mathematical Engine Agent: PASSED
   ✅ Agent initialization successful
   ✅ Market data fetched: 8 symbols

⚡ PERFORMANCE BENCHMARK: PASSED
   ✅ Statistical analysis benchmark: 2.34s for 5 assets
   ✅ Correlation analysis benchmark: 1.23s for 5x5 correlation matrix
   ✅ Performance: Excellent (3.57s total)

🎉 ALL TESTS PASSED! Mathematical Engine is ready!
                </div>
            </div>

            <h3>📈 Mathematical Trading Results</h3>

            <div class="step">
                <h4>Understanding How Math Influences Trading</h4>
                <div class="code-block">
=== Trading Strategy Agent Test ===

3. Testing strategy initialization...
   Loaded 2 strategies:
   - mean_reversion_001: mathematical_mean_reversion (5 symbols)
   - momentum_001: mathematical_momentum (5 symbols)

8. Testing strategy signal generation...
   🧮 Mathematical analysis input:
      - Calculus momentum: 0.0045 (positive acceleration)
      - Probability of upward move: 0.678
      - Matrix correlation strength: 0.234

   📊 Strategy mean_reversion_001 generated 2 signals:
   Signal: buy BTC/USDT at $45000.00 (confidence: 0.75)
      └── Mathematical basis: Z-score: -2.34, Probability: 0.78
   Signal: sell ETH/USDT at $3200.00 (confidence: 0.68)
      └── Mathematical basis: Z-score: +2.12, Probability: 0.71

   📈 Strategy momentum_001 generated 1 signal:
   Signal: strong_buy ADA/USDT at $0.45 (confidence: 0.82)
      └── Mathematical basis: Momentum: 0.0067, Trend strength: 0.84
</div>
            </div>

            <h3>🔍 Analyzing Your Mathematical Impact</h3>

            <table>
                <tr>
                    <th>Mathematical Component</th>
                    <th>How It Affects Trading</th>
                    <th>Example Output</th>
                </tr>
                <tr>
                    <td><strong>Calculus Derivatives</strong></td>
                    <td>Momentum detection, acceleration analysis</td>
                    <td>Momentum: 0.0045 → BUY signal</td>
                </tr>
                <tr>
                    <td><strong>Probability Calculations</strong></td>
                    <td>Entry/exit timing, confidence levels</td>
                    <td>P(upward) = 0.678 → High confidence</td>
                </tr>
                <tr>
                    <td><strong>Matrix Operations</strong></td>
                    <td>Position sizing, portfolio allocation</td>
                    <td>Correlation = 0.234 → Diversification OK</td>
                </tr>
                <tr>
                    <td><strong>Statistical Analysis</strong></td>
                    <td>Technical indicators, market state</td>
                    <td>Volatility = 3.45% → Low risk</td>
                </tr>
                <tr>
                    <td><strong>Pattern Recognition</strong></td>
                    <td>Signal confirmation, timing</td>
                    <td>23 patterns detected → Confirmation</td>
                </tr>
            </table>

            <h3>📊 Database Queries for Mathematical Results</h3>

            <div class="step">
                <h4>Query Mathematical Analysis Results</h4>
                <div class="code-block">
# See how your mathematical analysis influenced trades:
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    symbol,
    action,
    confidence,
    metadata->>'z_score' as z_score,
    metadata->>'volatility' as volatility,
    metadata->>'momentum' as momentum,
    timestamp
FROM trades
WHERE metadata IS NOT NULL
ORDER BY timestamp DESC
LIMIT 10;"

# See mathematical analysis stored results:
PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c "
SELECT
    timestamp,
    selected_symbols,
    mathematical_scores->>'calculus' as calculus_analysis,
    mathematical_scores->>'probability' as probability_analysis
FROM analysis_results
ORDER BY timestamp DESC
LIMIT 5;"
                </div>
            </div>
        </section>

        <section id="advanced-math" class="section">
            <h2>🎯 Advanced Mathematical Testing & Optimization</h2>

            <h3>🧪 Custom Mathematical Backtesting Scripts</h3>

            <div class="step">
                <h4>Create Your Own Mathematical Backtest</h4>
                <div class="code-block">
# Create custom_math_backtest.py:
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from agents.mathematical_engine.mathematical_engine_agent import MathematicalEngineAgent
from shared.mathematical.formulas import IntegratedMathematicalAnalysis

async def test_your_mathematical_ideas():
    """Test YOUR specific mathematical trading ideas"""
    print("🧮 Testing Your Custom Mathematical Ideas")

    # Initialize mathematical engine
    math_agent = MathematicalEngineAgent()
    await math_agent.initialize()

    # Get real market data
    market_data = await math_agent.fetch_market_data(['BTC/USDT', 'ETH/USDT'], limit=500)

    if not market_data:
        print("❌ No market data available")
        return

    # Test YOUR mathematical analysis
    integrated_analysis = IntegratedMathematicalAnalysis()

    for symbol, data in market_data.items():
        print(f"\n📊 Analyzing {symbol} with YOUR mathematical methods:")

        # YOUR calculus analysis
        momentum = integrated_analysis.calculus.derivative_price_momentum(data['close_price'])
        acceleration = integrated_analysis.calculus.second_derivative_acceleration(data['close_price'])

        print(f"   🧮 Calculus Analysis:")
        print(f"      Latest Momentum: {momentum.iloc[-1]:.6f}")
        print(f"      Latest Acceleration: {acceleration.iloc[-1]:.6f}")

        # YOUR probability analysis
        target_price = data['close_price'].iloc[-1] * 1.05  # 5% upside
        prob_analysis = integrated_analysis.probability.normal_distribution_price_probability(
            data['close_price'], target_price
        )

        print(f"   📊 Probability Analysis:")
        print(f"      Target Price: ${target_price:.2f}")
        print(f"      Probability of Success: {prob_analysis['probability']:.3f}")
        print(f"      Z-Score: {prob_analysis['z_score']:.3f}")

        # YOUR decision logic based on mathematics
        if momentum.iloc[-1] > 0 and prob_analysis['probability'] > 0.3:
            decision = "🟢 MATHEMATICAL BUY SIGNAL"
            reasoning = f"Positive momentum ({momentum.iloc[-1]:.6f}) + High probability ({prob_analysis['probability']:.3f})"
        elif momentum.iloc[-1] < 0 and prob_analysis['probability'] < 0.2:
            decision = "🔴 MATHEMATICAL SELL SIGNAL"
            reasoning = f"Negative momentum ({momentum.iloc[-1]:.6f}) + Low probability ({prob_analysis['probability']:.3f})"
        else:
            decision = "🟡 MATHEMATICAL HOLD"
            reasoning = "Mixed mathematical signals"

        print(f"   🎯 Mathematical Decision: {decision}")
        print(f"      Reasoning: {reasoning}")

    await math_agent.cleanup()

# Run your custom mathematical backtest
asyncio.run(test_your_mathematical_ideas())
                </div>
            </div>

            <h3>📊 Mathematical Performance Analysis</h3>

            <div class="step">
                <h4>Analyze Mathematical Strategy Performance</h4>
                <div class="code-block">
# Create math_performance_analysis.py:
import psycopg2
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def analyze_mathematical_performance():
    """Analyze how well your mathematical ideas performed"""

    # Connect to database
    conn = psycopg2.connect(
        host='localhost',
        database='mathematical_trading',
        user='trading_user',
        password='hejhej'
    )

    # Get trades with mathematical metadata
    trades_query = """
    SELECT
        symbol,
        action,
        price,
        confidence,
        timestamp,
        metadata->>'z_score' as z_score,
        metadata->>'momentum' as momentum,
        metadata->>'probability' as probability
    FROM trades
    WHERE metadata IS NOT NULL
    ORDER BY timestamp
    """

    trades_df = pd.read_sql(trades_query, conn)

    if len(trades_df) == 0:
        print("❌ No trades with mathematical metadata found")
        return

    print("📊 MATHEMATICAL TRADING PERFORMANCE ANALYSIS")
    print("=" * 50)

    # Analyze mathematical signal accuracy
    print(f"\n🧮 Mathematical Signal Statistics:")
    print(f"   Total trades: {len(trades_df)}")
    print(f"   Average confidence: {trades_df['confidence'].mean():.3f}")

    if 'z_score' in trades_df.columns:
        z_scores = pd.to_numeric(trades_df['z_score'], errors='coerce').dropna()
        print(f"   Average Z-score magnitude: {abs(z_scores).mean():.3f}")
        print(f"   Strong signals (|Z| > 2): {(abs(z_scores) > 2).sum()}")

    # Analyze by mathematical indicators
    buy_trades = trades_df[trades_df['action'].isin(['buy', 'strong_buy'])]
    sell_trades = trades_df[trades_df['action'].isin(['sell', 'strong_sell'])]

    print(f"\n📈 Buy Signal Analysis:")
    print(f"   Buy trades: {len(buy_trades)}")
    print(f"   Average buy confidence: {buy_trades['confidence'].mean():.3f}")

    print(f"\n📉 Sell Signal Analysis:")
    print(f"   Sell trades: {len(sell_trades)}")
    print(f"   Average sell confidence: {sell_trades['confidence'].mean():.3f}")

    # Create performance visualization
    plt.figure(figsize=(15, 10))

    # Plot 1: Confidence distribution
    plt.subplot(2, 3, 1)
    plt.hist(trades_df['confidence'], bins=20, alpha=0.7)
    plt.title('Mathematical Confidence Distribution')
    plt.xlabel('Confidence Level')
    plt.ylabel('Frequency')

    # Plot 2: Z-score distribution (if available)
    if 'z_score' in trades_df.columns:
        z_scores = pd.to_numeric(trades_df['z_score'], errors='coerce').dropna()
        plt.subplot(2, 3, 2)
        plt.hist(z_scores, bins=20, alpha=0.7)
        plt.title('Z-Score Distribution')
        plt.xlabel('Z-Score')
        plt.ylabel('Frequency')

    # Plot 3: Trades over time
    plt.subplot(2, 3, 3)
    trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
    daily_trades = trades_df.groupby(trades_df['timestamp'].dt.date).size()
    plt.plot(daily_trades.index, daily_trades.values)
    plt.title('Mathematical Trades Over Time')
    plt.xlabel('Date')
    plt.ylabel('Number of Trades')
    plt.xticks(rotation=45)

    # Plot 4: Confidence vs Performance (proxy)
    plt.subplot(2, 3, 4)
    plt.scatter(trades_df['confidence'], trades_df['price'], alpha=0.6)
    plt.title('Confidence vs Price Level')
    plt.xlabel('Mathematical Confidence')
    plt.ylabel('Trade Price')

    # Plot 5: Action distribution
    plt.subplot(2, 3, 5)
    action_counts = trades_df['action'].value_counts()
    plt.pie(action_counts.values, labels=action_counts.index, autopct='%1.1f%%')
    plt.title('Mathematical Signal Distribution')

    # Plot 6: Symbol performance
    plt.subplot(2, 3, 6)
    symbol_counts = trades_df['symbol'].value_counts()
    plt.bar(symbol_counts.index, symbol_counts.values)
    plt.title('Trades by Symbol')
    plt.xlabel('Symbol')
    plt.ylabel('Number of Trades')
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig('mathematical_trading_performance.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"\n📊 Performance chart saved as: mathematical_trading_performance.png")

    conn.close()

# Run the analysis
analyze_mathematical_performance()
                </div>
            </div>

            <h3>🚀 Optimization of Mathematical Parameters</h3>

            <div class="step">
                <h4>Optimize Your Mathematical Formula Parameters</h4>
                <div class="code-block">
# Create math_optimization.py:
import numpy as np
import pandas as pd
from scipy.optimize import minimize
from shared.mathematical.formulas import CalculusFormulas, ProbabilityFormulas

def optimize_mathematical_parameters():
    """Optimize your mathematical formula parameters for better performance"""

    print("🎯 OPTIMIZING YOUR MATHEMATICAL PARAMETERS")
    print("=" * 50)

    # Load historical data (you can replace this with real data)
    np.random.seed(42)
    historical_prices = pd.Series(100 * np.exp(np.cumsum(np.random.normal(0.001, 0.02, 1000))))

    def evaluate_mathematical_strategy(params):
        """Evaluate strategy performance with given mathematical parameters"""
        momentum_window, z_threshold, prob_threshold = params

        # Your mathematical analysis with parameters
        calc = CalculusFormulas()
        prob = ProbabilityFormulas()

        momentum = calc.derivative_price_momentum(historical_prices, int(momentum_window))

        # Simulate trading signals based on your math
        signals = []
        returns = []

        for i in range(50, len(historical_prices)-1):
            current_momentum = momentum.iloc[i]

            # Your mathematical decision logic
            if current_momentum > z_threshold:
                # Buy signal - calculate return
                entry_price = historical_prices.iloc[i]
                exit_price = historical_prices.iloc[i+1]
                ret = (exit_price - entry_price) / entry_price
                returns.append(ret)
                signals.append('buy')
            elif current_momentum < -z_threshold:
                # Sell signal
                entry_price = historical_prices.iloc[i]
                exit_price = historical_prices.iloc[i+1]
                ret = (entry_price - exit_price) / entry_price  # Short position
                returns.append(ret)
                signals.append('sell')

        if len(returns) == 0:
            return 1.0  # Penalty for no trades

        # Calculate performance metrics
        total_return = np.sum(returns)
        sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0

        # Objective: maximize risk-adjusted returns
        objective = -(sharpe_ratio + total_return)  # Negative because we minimize

        return objective

    # Optimize mathematical parameters
    initial_params = [10, 0.001, 0.5]  # momentum_window, z_threshold, prob_threshold
    bounds = [(5, 50), (0.0001, 0.01), (0.1, 0.9)]

    print("🧮 Optimizing mathematical parameters...")
    result = minimize(
        evaluate_mathematical_strategy,
        initial_params,
        bounds=bounds,
        method='L-BFGS-B'
    )

    optimal_params = result.x
    optimal_performance = -result.fun

    print(f"\n✅ OPTIMIZATION COMPLETE!")
    print(f"   Optimal momentum window: {optimal_params[0]:.1f}")
    print(f"   Optimal Z threshold: {optimal_params[1]:.6f}")
    print(f"   Optimal probability threshold: {optimal_params[2]:.3f}")
    print(f"   Optimized performance score: {optimal_performance:.6f}")

    # Test the optimized parameters
    print(f"\n🧪 Testing optimized mathematical parameters...")
    test_performance = evaluate_mathematical_strategy(optimal_params)
    print(f"   Test performance: {-test_performance:.6f}")

    return optimal_params

# Run optimization
optimal_mathematical_params = optimize_mathematical_parameters()
                </div>
            </div>
        </section>

        <div class="section">
            <h2>🎉 Ready to Test Your Mathematical Ideas!</h2>

            <div class="success">
                <strong>🧮 You Now Have Everything You Need:</strong><br>
                • ✅ Understanding of the REAL mathematical architecture<br>
                • ✅ API keys for data collection<br>
                • ✅ How to test individual mathematical components<br>
                • ✅ How to add your own mathematical formulas<br>
                • ✅ How to run comprehensive mathematical backtests<br>
                • ✅ How to analyze mathematical performance<br>
                • ✅ How to optimize mathematical parameters
            </div>

            <div class="step">
                <h4>🚀 Your Mathematical Backtesting Workflow:</h4>
                <ol>
                    <li><strong>Set up API keys</strong> - Get real market data for your math</li>
                    <li><strong>Test mathematical components</strong> - Verify your calculus, probability, matrix math</li>
                    <li><strong>Add your custom formulas</strong> - Implement YOUR mathematical ideas</li>
                    <li><strong>Run comprehensive backtests</strong> - See how your math performs in trading</li>
                    <li><strong>Analyze results</strong> - Understand which mathematical approaches work best</li>
                    <li><strong>Optimize parameters</strong> - Fine-tune your mathematical models</li>
                    <li><strong>Iterate and improve</strong> - Refine your mathematical trading ideas</li>
                </ol>
            </div>

            <div class="math-focus">
                <h3>🧠 Remember: This is YOUR Mathematical Laboratory</h3>
                <p>The simple "mean reversion" and "momentum" strategies are just templates. The real power is in:</p>
                <ul>
                    <li>🧮 <strong>Your calculus formulas</strong> for momentum and trend analysis</li>
                    <li>📊 <strong>Your probability models</strong> for market predictions</li>
                    <li>🔢 <strong>Your matrix operations</strong> for portfolio optimization</li>
                    <li>🔍 <strong>Your pattern recognition</strong> for signal confirmation</li>
                    <li>🤖 <strong>AI interpretation</strong> of your mathematical results</li>
                </ul>
                <p><strong>This system exists to test YOUR mathematical ideas - use it!</strong></p>
            </div>

            <div class="info">
                <strong>💡 Next Steps:</strong><br>
                1. Get your API keys (especially DeepSeek for AI analysis)<br>
                2. Run <code>python test_mathematical_engine.py</code> to verify everything works<br>
                3. Start adding YOUR mathematical formulas to test your ideas<br>
                4. Run backtests and see how your math performs in real markets!
            </div>
        </div>

        <footer style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; margin-top: 40px;">
            <p><strong>🧮 AstroA Mathematical Trading Engine</strong></p>
            <p><em>Your mathematical ideas + Real market data + AI analysis = Better trading decisions</em></p>
            <p>This is your mathematical laboratory - experiment, test, and discover!</p>
        </footer>
    </div>
</body>
</html>