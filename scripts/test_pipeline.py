#!/usr/bin/env python3
"""
Automated testing pipeline for the trading system.
Runs unit tests, integration tests, and performance tests.
"""

import os
import sys
import subprocess
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class TestResult:
    test_type: str
    test_name: str
    status: str  # "passed", "failed", "skipped"
    duration: float
    output: str
    error: Optional[str] = None

@dataclass
class PipelineResult:
    timestamp: str
    total_duration: float
    tests_run: int
    tests_passed: int
    tests_failed: int
    tests_skipped: int
    results: List[TestResult]
    coverage_report: Optional[Dict[str, Any]] = None

class TestPipeline:
    """Automated testing pipeline"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.logger = logging.getLogger(__name__)
        self.results: List[TestResult] = []

    def run_full_pipeline(self) -> PipelineResult:
        """Run the complete testing pipeline"""
        start_time = time.time()
        self.logger.info("Starting automated testing pipeline...")

        # Clear previous results
        self.results = []

        try:
            # 1. Environment validation
            self._run_environment_tests()

            # 2. Unit tests
            self._run_unit_tests()

            # 3. Integration tests
            self._run_integration_tests()

            # 4. Performance tests
            self._run_performance_tests()

            # 5. Code quality checks
            self._run_code_quality_tests()

            # 6. Generate coverage report
            coverage_report = self._generate_coverage_report()

        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            self.results.append(TestResult(
                test_type="pipeline",
                test_name="pipeline_execution",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

        # Calculate summary statistics
        total_duration = time.time() - start_time
        tests_run = len(self.results)
        tests_passed = len([r for r in self.results if r.status == "passed"])
        tests_failed = len([r for r in self.results if r.status == "failed"])
        tests_skipped = len([r for r in self.results if r.status == "skipped"])

        result = PipelineResult(
            timestamp=datetime.now().isoformat(),
            total_duration=total_duration,
            tests_run=tests_run,
            tests_passed=tests_passed,
            tests_failed=tests_failed,
            tests_skipped=tests_skipped,
            results=self.results,
            coverage_report=coverage_report
        )

        self._save_results(result)
        self._print_summary(result)

        return result

    def _run_environment_tests(self):
        """Test environment setup and dependencies"""
        self.logger.info("Running environment validation tests...")

        # Test Python version
        python_version = sys.version_info
        if python_version.major >= 3 and python_version.minor >= 8:
            self.results.append(TestResult(
                test_type="environment",
                test_name="python_version",
                status="passed",
                duration=0.001,
                output=f"Python {python_version.major}.{python_version.minor}.{python_version.micro}"
            ))
        else:
            self.results.append(TestResult(
                test_type="environment",
                test_name="python_version",
                status="failed",
                duration=0.001,
                output=f"Python {python_version.major}.{python_version.minor}.{python_version.micro}",
                error="Python 3.8+ required"
            ))

        # Test critical dependencies
        critical_deps = [
            "numpy", "pandas", "sklearn", "torch", "transformers",
            "psycopg2", "sqlalchemy", "ccxt"
        ]

        for dep in critical_deps:
            start_time = time.time()
            try:
                __import__(dep)
                self.results.append(TestResult(
                    test_type="environment",
                    test_name=f"dependency_{dep}",
                    status="passed",
                    duration=time.time() - start_time,
                    output=f"Successfully imported {dep}"
                ))
            except ImportError as e:
                self.results.append(TestResult(
                    test_type="environment",
                    test_name=f"dependency_{dep}",
                    status="failed",
                    duration=time.time() - start_time,
                    output="",
                    error=str(e)
                ))

        # Test database connection
        self._test_database_connection()

    def _test_database_connection(self):
        """Test database connectivity"""
        start_time = time.time()
        try:
            import psycopg2
            from config.settings import DATABASE_CONFIG

            conn = psycopg2.connect(**DATABASE_CONFIG)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            conn.close()

            self.results.append(TestResult(
                test_type="environment",
                test_name="database_connection",
                status="passed",
                duration=time.time() - start_time,
                output="Database connection successful"
            ))
        except Exception as e:
            self.results.append(TestResult(
                test_type="environment",
                test_name="database_connection",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _run_unit_tests(self):
        """Run unit tests using pytest"""
        self.logger.info("Running unit tests...")

        test_dirs = [
            "tests/unit",
            "tests/mathematical",
            "tests/agents",
            "tests/models"
        ]

        for test_dir in test_dirs:
            if not (self.project_root / test_dir).exists():
                continue

            start_time = time.time()
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pytest",
                    str(self.project_root / test_dir),
                    "-v", "--tb=short"
                ], capture_output=True, text=True, cwd=self.project_root)

                self.results.append(TestResult(
                    test_type="unit",
                    test_name=f"unit_tests_{test_dir.replace('/', '_')}",
                    status="passed" if result.returncode == 0 else "failed",
                    duration=time.time() - start_time,
                    output=result.stdout,
                    error=result.stderr if result.returncode != 0 else None
                ))

            except Exception as e:
                self.results.append(TestResult(
                    test_type="unit",
                    test_name=f"unit_tests_{test_dir.replace('/', '_')}",
                    status="failed",
                    duration=time.time() - start_time,
                    output="",
                    error=str(e)
                ))

    def _run_integration_tests(self):
        """Run integration tests"""
        self.logger.info("Running integration tests...")

        # Test data collection integration
        self._test_data_collection_integration()

        # Test trading strategy integration
        self._test_trading_strategy_integration()

        # Test ML model integration
        self._test_ml_model_integration()

    def _test_data_collection_integration(self):
        """Test data collection agent integration"""
        start_time = time.time()
        try:
            from agents.data_collector.data_collection_agent import DataCollectionAgent

            agent = DataCollectionAgent()
            # Basic functionality test
            symbols = ["AAPL", "MSFT"]
            result = agent.fetch_latest_data(symbols, limit=1)

            if result and len(result) > 0:
                self.results.append(TestResult(
                    test_type="integration",
                    test_name="data_collection_integration",
                    status="passed",
                    duration=time.time() - start_time,
                    output=f"Successfully collected data for {len(result)} symbols"
                ))
            else:
                self.results.append(TestResult(
                    test_type="integration",
                    test_name="data_collection_integration",
                    status="failed",
                    duration=time.time() - start_time,
                    output="",
                    error="No data collected"
                ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="integration",
                test_name="data_collection_integration",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _test_trading_strategy_integration(self):
        """Test trading strategy integration"""
        start_time = time.time()
        try:
            from agents.strategy.trading_strategy_agent import TradingStrategyAgent

            agent = TradingStrategyAgent()
            # Test strategy evaluation without actual trading
            test_data = {
                "symbol": "AAPL",
                "price": 150.0,
                "volume": 1000000,
                "timestamp": time.time()
            }

            decision = agent.evaluate_market_conditions(test_data)

            if decision and "action" in decision:
                self.results.append(TestResult(
                    test_type="integration",
                    test_name="trading_strategy_integration",
                    status="passed",
                    duration=time.time() - start_time,
                    output=f"Strategy evaluation successful: {decision['action']}"
                ))
            else:
                self.results.append(TestResult(
                    test_type="integration",
                    test_name="trading_strategy_integration",
                    status="failed",
                    duration=time.time() - start_time,
                    output="",
                    error="Invalid strategy decision"
                ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="integration",
                test_name="trading_strategy_integration",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _test_ml_model_integration(self):
        """Test ML model integration"""
        start_time = time.time()
        try:
            from ml_models.model_manager import ModelManager

            manager = ModelManager()
            # Test model loading/initialization
            models = manager.list_available_models()

            if models:
                self.results.append(TestResult(
                    test_type="integration",
                    test_name="ml_model_integration",
                    status="passed",
                    duration=time.time() - start_time,
                    output=f"Available models: {len(models)}"
                ))
            else:
                self.results.append(TestResult(
                    test_type="integration",
                    test_name="ml_model_integration",
                    status="skipped",
                    duration=time.time() - start_time,
                    output="No trained models available"
                ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="integration",
                test_name="ml_model_integration",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _run_performance_tests(self):
        """Run performance tests"""
        self.logger.info("Running performance tests...")

        # Test data processing performance
        self._test_data_processing_performance()

        # Test prediction latency
        self._test_prediction_latency()

    def _test_data_processing_performance(self):
        """Test data processing performance"""
        start_time = time.time()
        try:
            import pandas as pd
            import numpy as np

            # Create test dataset
            data_size = 10000
            test_data = pd.DataFrame({
                "timestamp": pd.date_range(start="2023-01-01", periods=data_size, freq="1min"),
                "open": np.random.random(data_size) * 100,
                "high": np.random.random(data_size) * 100,
                "low": np.random.random(data_size) * 100,
                "close": np.random.random(data_size) * 100,
                "volume": np.random.randint(1000, 100000, data_size)
            })

            # Test basic data processing operations
            processing_start = time.time()
            test_data["sma_20"] = test_data["close"].rolling(window=20).mean()
            test_data["rsi"] = self._calculate_rsi(test_data["close"])
            processing_time = time.time() - processing_start

            # Performance threshold: should process 10k records in < 1 second
            if processing_time < 1.0:
                self.results.append(TestResult(
                    test_type="performance",
                    test_name="data_processing_performance",
                    status="passed",
                    duration=time.time() - start_time,
                    output=f"Processed {data_size} records in {processing_time:.3f}s"
                ))
            else:
                self.results.append(TestResult(
                    test_type="performance",
                    test_name="data_processing_performance",
                    status="failed",
                    duration=time.time() - start_time,
                    output=f"Processed {data_size} records in {processing_time:.3f}s",
                    error="Processing too slow (>1s for 10k records)"
                ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="performance",
                test_name="data_processing_performance",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _calculate_rsi(self, prices, period=14):
        """Calculate RSI for performance testing"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _test_prediction_latency(self):
        """Test ML prediction latency"""
        start_time = time.time()
        try:
            # Test simple prediction latency
            import numpy as np
            from sklearn.ensemble import RandomForestRegressor

            # Create and train a simple model
            X = np.random.random((1000, 10))
            y = np.random.random(1000)
            model = RandomForestRegressor(n_estimators=10)
            model.fit(X, y)

            # Test prediction latency
            test_input = np.random.random((1, 10))
            prediction_start = time.time()
            prediction = model.predict(test_input)
            prediction_time = time.time() - prediction_start

            # Latency threshold: should predict in < 10ms
            if prediction_time < 0.01:
                self.results.append(TestResult(
                    test_type="performance",
                    test_name="prediction_latency",
                    status="passed",
                    duration=time.time() - start_time,
                    output=f"Prediction latency: {prediction_time*1000:.2f}ms"
                ))
            else:
                self.results.append(TestResult(
                    test_type="performance",
                    test_name="prediction_latency",
                    status="failed",
                    duration=time.time() - start_time,
                    output=f"Prediction latency: {prediction_time*1000:.2f}ms",
                    error="Prediction too slow (>10ms)"
                ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="performance",
                test_name="prediction_latency",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _run_code_quality_tests(self):
        """Run code quality checks"""
        self.logger.info("Running code quality tests...")

        # Run flake8 for style checks
        self._run_flake8()

        # Run mypy for type checking
        self._run_mypy()

    def _run_flake8(self):
        """Run flake8 code style checks"""
        start_time = time.time()
        try:
            result = subprocess.run([
                sys.executable, "-m", "flake8",
                "--max-line-length=100",
                "--ignore=E203,W503",
                "agents", "ml_models", "shared"
            ], capture_output=True, text=True, cwd=self.project_root)

            self.results.append(TestResult(
                test_type="quality",
                test_name="flake8_style_check",
                status="passed" if result.returncode == 0 else "failed",
                duration=time.time() - start_time,
                output=result.stdout,
                error=result.stderr if result.returncode != 0 else None
            ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="quality",
                test_name="flake8_style_check",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _run_mypy(self):
        """Run mypy type checking"""
        start_time = time.time()
        try:
            result = subprocess.run([
                sys.executable, "-m", "mypy",
                "--ignore-missing-imports",
                "agents", "ml_models", "shared"
            ], capture_output=True, text=True, cwd=self.project_root)

            # Mypy warnings are acceptable, only errors fail the test
            error_count = result.stdout.count("error:")
            status = "passed" if error_count == 0 else "failed"

            self.results.append(TestResult(
                test_type="quality",
                test_name="mypy_type_check",
                status=status,
                duration=time.time() - start_time,
                output=result.stdout,
                error=result.stderr if result.returncode != 0 and error_count > 0 else None
            ))

        except Exception as e:
            self.results.append(TestResult(
                test_type="quality",
                test_name="mypy_type_check",
                status="failed",
                duration=time.time() - start_time,
                output="",
                error=str(e)
            ))

    def _generate_coverage_report(self) -> Optional[Dict[str, Any]]:
        """Generate code coverage report"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "coverage", "run", "-m", "pytest", "tests/",
                "--source=agents,ml_models,shared"
            ], capture_output=True, text=True, cwd=self.project_root)

            if result.returncode == 0:
                coverage_result = subprocess.run([
                    sys.executable, "-m", "coverage", "report", "--format=json"
                ], capture_output=True, text=True, cwd=self.project_root)

                if coverage_result.returncode == 0:
                    return json.loads(coverage_result.stdout)

        except Exception as e:
            self.logger.warning(f"Could not generate coverage report: {e}")

        return None

    def _save_results(self, result: PipelineResult):
        """Save test results to file"""
        results_dir = self.project_root / "test_results"
        results_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"test_results_{timestamp}.json"

        with open(results_file, "w") as f:
            json.dump(asdict(result), f, indent=2)

        self.logger.info(f"Test results saved to {results_file}")

    def _print_summary(self, result: PipelineResult):
        """Print test summary"""
        print("\n" + "="*80)
        print("AUTOMATED TESTING PIPELINE SUMMARY")
        print("="*80)
        print(f"Timestamp: {result.timestamp}")
        print(f"Duration: {result.total_duration:.2f}s")
        print(f"Tests Run: {result.tests_run}")
        print(f"Passed: {result.tests_passed}")
        print(f"Failed: {result.tests_failed}")
        print(f"Skipped: {result.tests_skipped}")

        if result.tests_failed > 0:
            print("\nFAILED TESTS:")
            for test in result.results:
                if test.status == "failed":
                    print(f"  ❌ {test.test_type}.{test.test_name}")
                    if test.error:
                        print(f"     Error: {test.error}")

        print("\nTEST BREAKDOWN BY TYPE:")
        test_types = {}
        for test in result.results:
            if test.test_type not in test_types:
                test_types[test.test_type] = {"passed": 0, "failed": 0, "skipped": 0}
            test_types[test.test_type][test.status] += 1

        for test_type, counts in test_types.items():
            print(f"  {test_type.title()}: {counts['passed']} passed, {counts['failed']} failed, {counts['skipped']} skipped")

        if result.coverage_report:
            total_coverage = result.coverage_report.get("totals", {}).get("percent_covered", 0)
            print(f"\nCode Coverage: {total_coverage:.1f}%")

        print("="*80)

def main():
    """Main entry point for test pipeline"""
    import argparse

    parser = argparse.ArgumentParser(description="Automated Testing Pipeline")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")

    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Run pipeline
    pipeline = TestPipeline(args.project_root)
    result = pipeline.run_full_pipeline()

    # Exit with error code if tests failed
    sys.exit(1 if result.tests_failed > 0 else 0)

if __name__ == "__main__":
    main()