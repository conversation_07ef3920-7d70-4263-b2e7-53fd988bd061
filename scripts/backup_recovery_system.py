"""
Comprehensive Backup and Recovery System for Live Trading
Handles database backups, configuration backups, and disaster recovery
"""

import asyncio
import os
import shutil
import json
import tarfile
import gzip
import logging
import psycopg2
import redis
import boto3
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import subprocess
from pathlib import Path
import hashlib

class BackupType(Enum):
    """Types of backups"""
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"
    TRANSACTION_LOG = "transaction_log"

class BackupStatus(Enum):
    """Backup operation status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    VERIFIED = "verified"

class StorageBackend(Enum):
    """Storage backend types"""
    LOCAL = "local"
    S3 = "s3"
    GCS = "gcs"
    AZURE = "azure"

@dataclass
class BackupJob:
    """Backup job definition"""
    job_id: str
    name: str
    backup_type: BackupType
    source_type: str  # database, files, config
    source_path: str
    destination: str
    schedule: str  # cron expression
    retention_days: int
    compression: bool = True
    encryption: bool = True
    verify_after_backup: bool = True
    status: BackupStatus = BackupStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BackupResult:
    """Backup operation result"""
    job_id: str
    backup_id: str
    status: BackupStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    size_bytes: int = 0
    checksum: Optional[str] = None
    file_path: str = ""
    error_message: Optional[str] = None
    verification_passed: bool = False

class DatabaseBackupManager:
    """PostgreSQL database backup manager"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_config = config.get('database', {})
        self.backup_dir = config.get('backup_dir', '/var/backups/trading')
        self.logger = logging.getLogger("DatabaseBackupManager")

        # Ensure backup directory exists
        Path(self.backup_dir).mkdir(parents=True, exist_ok=True)

    async def create_full_backup(self, backup_id: str) -> BackupResult:
        """Create full database backup"""
        start_time = datetime.now()
        backup_file = f"{self.backup_dir}/db_full_{backup_id}_{start_time.strftime('%Y%m%d_%H%M%S')}.sql"

        try:
            # Create pg_dump command
            cmd = [
                'pg_dump',
                '-h', self.db_config.get('host', 'localhost'),
                '-p', str(self.db_config.get('port', 5432)),
                '-U', self.db_config.get('user', 'trading_user'),
                '-d', self.db_config.get('database', 'mathematical_trading'),
                '-f', backup_file,
                '--verbose',
                '--no-password'
            ]

            # Set password environment variable
            env = os.environ.copy()
            env['PGPASSWORD'] = self.db_config.get('password', '')

            # Execute backup
            process = await asyncio.create_subprocess_exec(
                *cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # Compress backup
                compressed_file = f"{backup_file}.gz"
                with open(backup_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

                # Remove uncompressed file
                os.remove(backup_file)
                backup_file = compressed_file

                # Calculate checksum
                checksum = await self._calculate_checksum(backup_file)

                # Get file size
                file_size = os.path.getsize(backup_file)

                result = BackupResult(
                    job_id="database_full",
                    backup_id=backup_id,
                    status=BackupStatus.COMPLETED,
                    start_time=start_time,
                    end_time=datetime.now(),
                    size_bytes=file_size,
                    checksum=checksum,
                    file_path=backup_file,
                    verification_passed=True
                )

                self.logger.info(f"Database backup completed: {backup_file}")
                return result

            else:
                error_msg = stderr.decode() if stderr else "Unknown error"
                self.logger.error(f"Database backup failed: {error_msg}")

                return BackupResult(
                    job_id="database_full",
                    backup_id=backup_id,
                    status=BackupStatus.FAILED,
                    start_time=start_time,
                    end_time=datetime.now(),
                    error_message=error_msg
                )

        except Exception as e:
            self.logger.error(f"Error creating database backup: {e}")
            return BackupResult(
                job_id="database_full",
                backup_id=backup_id,
                status=BackupStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now(),
                error_message=str(e)
            )

    async def create_incremental_backup(self, backup_id: str, base_backup_time: datetime) -> BackupResult:
        """Create incremental backup using WAL files"""
        start_time = datetime.now()

        try:
            # This would use PostgreSQL WAL-E or similar for incremental backups
            # For now, we'll create a simplified version

            backup_file = f"{self.backup_dir}/db_incr_{backup_id}_{start_time.strftime('%Y%m%d_%H%M%S')}.tar.gz"

            # Get changes since base backup
            cmd = [
                'pg_dump',
                '-h', self.db_config.get('host', 'localhost'),
                '-p', str(self.db_config.get('port', 5432)),
                '-U', self.db_config.get('user', 'trading_user'),
                '-d', self.db_config.get('database', 'mathematical_trading'),
                '--data-only',
                '--inserts',
                '-f', backup_file.replace('.tar.gz', '.sql')
            ]

            env = os.environ.copy()
            env['PGPASSWORD'] = self.db_config.get('password', '')

            process = await asyncio.create_subprocess_exec(
                *cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # Compress backup
                sql_file = backup_file.replace('.tar.gz', '.sql')
                with tarfile.open(backup_file, 'w:gz') as tar:
                    tar.add(sql_file, arcname=os.path.basename(sql_file))

                os.remove(sql_file)

                checksum = await self._calculate_checksum(backup_file)
                file_size = os.path.getsize(backup_file)

                return BackupResult(
                    job_id="database_incremental",
                    backup_id=backup_id,
                    status=BackupStatus.COMPLETED,
                    start_time=start_time,
                    end_time=datetime.now(),
                    size_bytes=file_size,
                    checksum=checksum,
                    file_path=backup_file,
                    verification_passed=True
                )

            else:
                error_msg = stderr.decode() if stderr else "Unknown error"
                return BackupResult(
                    job_id="database_incremental",
                    backup_id=backup_id,
                    status=BackupStatus.FAILED,
                    start_time=start_time,
                    end_time=datetime.now(),
                    error_message=error_msg
                )

        except Exception as e:
            self.logger.error(f"Error creating incremental backup: {e}")
            return BackupResult(
                job_id="database_incremental",
                backup_id=backup_id,
                status=BackupStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now(),
                error_message=str(e)
            )

    async def restore_database(self, backup_file: str, target_db: str = None) -> bool:
        """Restore database from backup"""
        try:
            if not os.path.exists(backup_file):
                self.logger.error(f"Backup file not found: {backup_file}")
                return False

            target_database = target_db or self.db_config.get('database', 'mathematical_trading')

            # Decompress if needed
            if backup_file.endswith('.gz'):
                decompressed_file = backup_file[:-3]
                with gzip.open(backup_file, 'rb') as f_in:
                    with open(decompressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                sql_file = decompressed_file
            else:
                sql_file = backup_file

            # Restore database
            cmd = [
                'psql',
                '-h', self.db_config.get('host', 'localhost'),
                '-p', str(self.db_config.get('port', 5432)),
                '-U', self.db_config.get('user', 'trading_user'),
                '-d', target_database,
                '-f', sql_file
            ]

            env = os.environ.copy()
            env['PGPASSWORD'] = self.db_config.get('password', '')

            process = await asyncio.create_subprocess_exec(
                *cmd,
                env=env,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            # Clean up decompressed file if created
            if backup_file.endswith('.gz') and os.path.exists(sql_file):
                os.remove(sql_file)

            if process.returncode == 0:
                self.logger.info(f"Database restored successfully from {backup_file}")
                return True
            else:
                error_msg = stderr.decode() if stderr else "Unknown error"
                self.logger.error(f"Database restore failed: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"Error restoring database: {e}")
            return False

    async def _calculate_checksum(self, file_path: str) -> str:
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()

class FileBackupManager:
    """File system backup manager"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.backup_dir = config.get('backup_dir', '/var/backups/trading')
        self.logger = logging.getLogger("FileBackupManager")

        Path(self.backup_dir).mkdir(parents=True, exist_ok=True)

    async def backup_directory(self, source_dir: str, backup_id: str,
                              exclude_patterns: List[str] = None) -> BackupResult:
        """Backup a directory"""
        start_time = datetime.now()
        backup_file = f"{self.backup_dir}/files_{backup_id}_{start_time.strftime('%Y%m%d_%H%M%S')}.tar.gz"

        try:
            exclude_patterns = exclude_patterns or []

            # Create tarfile with compression
            with tarfile.open(backup_file, 'w:gz') as tar:
                def filter_func(tarinfo):
                    # Apply exclude patterns
                    for pattern in exclude_patterns:
                        if pattern in tarinfo.name:
                            return None
                    return tarinfo

                tar.add(source_dir, arcname=os.path.basename(source_dir), filter=filter_func)

            # Calculate checksum and size
            checksum = await self._calculate_checksum(backup_file)
            file_size = os.path.getsize(backup_file)

            self.logger.info(f"Directory backup completed: {backup_file}")

            return BackupResult(
                job_id="files",
                backup_id=backup_id,
                status=BackupStatus.COMPLETED,
                start_time=start_time,
                end_time=datetime.now(),
                size_bytes=file_size,
                checksum=checksum,
                file_path=backup_file,
                verification_passed=True
            )

        except Exception as e:
            self.logger.error(f"Error backing up directory {source_dir}: {e}")
            return BackupResult(
                job_id="files",
                backup_id=backup_id,
                status=BackupStatus.FAILED,
                start_time=start_time,
                end_time=datetime.now(),
                error_message=str(e)
            )

    async def restore_directory(self, backup_file: str, target_dir: str) -> bool:
        """Restore directory from backup"""
        try:
            if not os.path.exists(backup_file):
                self.logger.error(f"Backup file not found: {backup_file}")
                return False

            # Create target directory if it doesn't exist
            Path(target_dir).mkdir(parents=True, exist_ok=True)

            # Extract tarfile
            with tarfile.open(backup_file, 'r:gz') as tar:
                tar.extractall(path=target_dir)

            self.logger.info(f"Directory restored successfully to {target_dir}")
            return True

        except Exception as e:
            self.logger.error(f"Error restoring directory: {e}")
            return False

    async def _calculate_checksum(self, file_path: str) -> str:
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()

class CloudStorageManager:
    """Cloud storage backup manager"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.storage_backend = StorageBackend(config.get('backend', 'local'))
        self.logger = logging.getLogger("CloudStorageManager")

        # Initialize cloud clients
        if self.storage_backend == StorageBackend.S3:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=config.get('aws_access_key'),
                aws_secret_access_key=config.get('aws_secret_key'),
                region_name=config.get('aws_region', 'us-east-1')
            )
            self.bucket_name = config.get('s3_bucket')

    async def upload_backup(self, local_file: str, remote_path: str) -> bool:
        """Upload backup to cloud storage"""
        try:
            if self.storage_backend == StorageBackend.S3:
                return await self._upload_to_s3(local_file, remote_path)
            elif self.storage_backend == StorageBackend.LOCAL:
                return await self._copy_local(local_file, remote_path)
            else:
                self.logger.error(f"Unsupported storage backend: {self.storage_backend}")
                return False

        except Exception as e:
            self.logger.error(f"Error uploading backup: {e}")
            return False

    async def download_backup(self, remote_path: str, local_file: str) -> bool:
        """Download backup from cloud storage"""
        try:
            if self.storage_backend == StorageBackend.S3:
                return await self._download_from_s3(remote_path, local_file)
            elif self.storage_backend == StorageBackend.LOCAL:
                return await self._copy_local(remote_path, local_file)
            else:
                self.logger.error(f"Unsupported storage backend: {self.storage_backend}")
                return False

        except Exception as e:
            self.logger.error(f"Error downloading backup: {e}")
            return False

    async def _upload_to_s3(self, local_file: str, remote_path: str) -> bool:
        """Upload file to S3"""
        try:
            self.s3_client.upload_file(local_file, self.bucket_name, remote_path)
            self.logger.info(f"Uploaded {local_file} to S3: s3://{self.bucket_name}/{remote_path}")
            return True

        except Exception as e:
            self.logger.error(f"S3 upload failed: {e}")
            return False

    async def _download_from_s3(self, remote_path: str, local_file: str) -> bool:
        """Download file from S3"""
        try:
            self.s3_client.download_file(self.bucket_name, remote_path, local_file)
            self.logger.info(f"Downloaded from S3: s3://{self.bucket_name}/{remote_path} to {local_file}")
            return True

        except Exception as e:
            self.logger.error(f"S3 download failed: {e}")
            return False

    async def _copy_local(self, source: str, destination: str) -> bool:
        """Copy file locally"""
        try:
            shutil.copy2(source, destination)
            self.logger.info(f"Copied {source} to {destination}")
            return True

        except Exception as e:
            self.logger.error(f"Local copy failed: {e}")
            return False

class BackupScheduler:
    """Backup job scheduler"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.jobs: Dict[str, BackupJob] = {}
        self.is_running = False
        self.db_manager = DatabaseBackupManager(config)
        self.file_manager = FileBackupManager(config)
        self.cloud_manager = CloudStorageManager(config.get('cloud_storage', {}))
        self.logger = logging.getLogger("BackupScheduler")

    def add_job(self, job: BackupJob):
        """Add backup job to scheduler"""
        self.jobs[job.job_id] = job
        self.logger.info(f"Added backup job: {job.job_id}")

    async def start_scheduler(self):
        """Start backup scheduler"""
        self.is_running = True
        self.logger.info("Backup scheduler started")

        while self.is_running:
            try:
                current_time = datetime.now()

                for job in self.jobs.values():
                    if self._should_run_job(job, current_time):
                        await self._execute_job(job)

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Error in backup scheduler: {e}")
                await asyncio.sleep(60)

    def stop_scheduler(self):
        """Stop backup scheduler"""
        self.is_running = False
        self.logger.info("Backup scheduler stopped")

    def _should_run_job(self, job: BackupJob, current_time: datetime) -> bool:
        """Check if job should run"""
        if job.next_run is None:
            # First run - schedule based on current time
            job.next_run = self._calculate_next_run(job, current_time)
            return False

        return current_time >= job.next_run

    def _calculate_next_run(self, job: BackupJob, current_time: datetime) -> datetime:
        """Calculate next run time based on schedule"""
        # Simplified scheduler - in production, use proper cron parser
        if job.schedule == "@hourly":
            return current_time + timedelta(hours=1)
        elif job.schedule == "@daily":
            return current_time + timedelta(days=1)
        elif job.schedule == "@weekly":
            return current_time + timedelta(weeks=1)
        else:
            # Default to daily
            return current_time + timedelta(days=1)

    async def _execute_job(self, job: BackupJob):
        """Execute backup job"""
        try:
            self.logger.info(f"Executing backup job: {job.job_id}")
            job.status = BackupStatus.IN_PROGRESS
            job.last_run = datetime.now()

            backup_id = f"{job.job_id}_{int(job.last_run.timestamp())}"

            if job.source_type == "database":
                if job.backup_type == BackupType.FULL:
                    result = await self.db_manager.create_full_backup(backup_id)
                elif job.backup_type == BackupType.INCREMENTAL:
                    base_time = job.last_run - timedelta(days=1)  # Simplified
                    result = await self.db_manager.create_incremental_backup(backup_id, base_time)
                else:
                    result = await self.db_manager.create_full_backup(backup_id)

            elif job.source_type == "files":
                exclude_patterns = job.metadata.get('exclude_patterns', [])
                result = await self.file_manager.backup_directory(
                    job.source_path, backup_id, exclude_patterns
                )

            else:
                self.logger.error(f"Unknown source type: {job.source_type}")
                job.status = BackupStatus.FAILED
                return

            # Update job status
            job.status = result.status

            # Upload to cloud storage if configured
            if result.status == BackupStatus.COMPLETED and result.file_path:
                remote_path = f"backups/{job.job_id}/{os.path.basename(result.file_path)}"
                upload_success = await self.cloud_manager.upload_backup(
                    result.file_path, remote_path
                )

                if upload_success:
                    self.logger.info(f"Backup uploaded to cloud: {remote_path}")
                else:
                    self.logger.warning(f"Failed to upload backup to cloud: {result.file_path}")

            # Clean up old backups
            await self._cleanup_old_backups(job)

            # Schedule next run
            job.next_run = self._calculate_next_run(job, datetime.now())

            self.logger.info(f"Backup job completed: {job.job_id}, Status: {result.status.value}")

        except Exception as e:
            self.logger.error(f"Error executing backup job {job.job_id}: {e}")
            job.status = BackupStatus.FAILED

    async def _cleanup_old_backups(self, job: BackupJob):
        """Clean up old backup files"""
        try:
            backup_dir = self.config.get('backup_dir', '/var/backups/trading')
            cutoff_date = datetime.now() - timedelta(days=job.retention_days)

            # Find old backup files for this job
            pattern = f"{job.source_type}_{job.job_id}_*"
            backup_files = list(Path(backup_dir).glob(pattern))

            for backup_file in backup_files:
                # Get file modification time
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)

                if file_time < cutoff_date:
                    backup_file.unlink()
                    self.logger.info(f"Deleted old backup: {backup_file}")

        except Exception as e:
            self.logger.error(f"Error cleaning up old backups: {e}")

class DisasterRecoveryManager:
    """Disaster recovery management"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_manager = DatabaseBackupManager(config)
        self.file_manager = FileBackupManager(config)
        self.cloud_manager = CloudStorageManager(config.get('cloud_storage', {}))
        self.logger = logging.getLogger("DisasterRecoveryManager")

    async def create_disaster_recovery_snapshot(self) -> Dict[str, Any]:
        """Create complete system snapshot for disaster recovery"""
        snapshot_id = f"dr_snapshot_{int(datetime.now().timestamp())}"
        start_time = datetime.now()

        try:
            self.logger.info(f"Creating disaster recovery snapshot: {snapshot_id}")

            results = {}

            # Backup database
            db_result = await self.db_manager.create_full_backup(f"{snapshot_id}_db")
            results['database'] = db_result

            # Backup application files
            app_result = await self.file_manager.backup_directory(
                "/home/<USER>/axmadcodes/AstroA",
                f"{snapshot_id}_app",
                exclude_patterns=['__pycache__', '*.pyc', 'venv', '.git', 'logs']
            )
            results['application'] = app_result

            # Backup configuration
            config_result = await self.file_manager.backup_directory(
                "/etc/trading",  # Assuming config directory
                f"{snapshot_id}_config"
            )
            results['configuration'] = config_result

            # Create recovery manifest
            manifest = {
                'snapshot_id': snapshot_id,
                'created_at': start_time.isoformat(),
                'components': {
                    'database': {
                        'backup_file': db_result.file_path if db_result.status == BackupStatus.COMPLETED else None,
                        'checksum': db_result.checksum,
                        'size_bytes': db_result.size_bytes
                    },
                    'application': {
                        'backup_file': app_result.file_path if app_result.status == BackupStatus.COMPLETED else None,
                        'checksum': app_result.checksum,
                        'size_bytes': app_result.size_bytes
                    },
                    'configuration': {
                        'backup_file': config_result.file_path if config_result.status == BackupStatus.COMPLETED else None,
                        'checksum': config_result.checksum,
                        'size_bytes': config_result.size_bytes
                    }
                },
                'recovery_instructions': [
                    "1. Restore database from database backup",
                    "2. Restore application files from application backup",
                    "3. Restore configuration from configuration backup",
                    "4. Update environment variables and connections",
                    "5. Restart all services",
                    "6. Verify system functionality"
                ]
            }

            # Save manifest
            manifest_file = f"{self.config.get('backup_dir', '/var/backups/trading')}/DR_MANIFEST_{snapshot_id}.json"
            with open(manifest_file, 'w') as f:
                json.dump(manifest, f, indent=2)

            self.logger.info(f"Disaster recovery snapshot completed: {snapshot_id}")

            return {
                'snapshot_id': snapshot_id,
                'status': 'completed',
                'manifest_file': manifest_file,
                'components': results,
                'total_size_bytes': sum(r.size_bytes for r in results.values() if r.status == BackupStatus.COMPLETED)
            }

        except Exception as e:
            self.logger.error(f"Error creating disaster recovery snapshot: {e}")
            return {
                'snapshot_id': snapshot_id,
                'status': 'failed',
                'error': str(e)
            }

    async def restore_from_snapshot(self, snapshot_id: str, target_environment: str = "production") -> bool:
        """Restore system from disaster recovery snapshot"""
        try:
            self.logger.info(f"Starting disaster recovery from snapshot: {snapshot_id}")

            # Load manifest
            manifest_file = f"{self.config.get('backup_dir', '/var/backups/trading')}/DR_MANIFEST_{snapshot_id}.json"

            if not os.path.exists(manifest_file):
                self.logger.error(f"Recovery manifest not found: {manifest_file}")
                return False

            with open(manifest_file, 'r') as f:
                manifest = json.load(f)

            # Restore database
            db_backup = manifest['components']['database']['backup_file']
            if db_backup and os.path.exists(db_backup):
                db_success = await self.db_manager.restore_database(db_backup)
                if not db_success:
                    self.logger.error("Database restore failed")
                    return False
            else:
                self.logger.error("Database backup file not found")
                return False

            # Restore application files
            app_backup = manifest['components']['application']['backup_file']
            if app_backup and os.path.exists(app_backup):
                app_success = await self.file_manager.restore_directory(
                    app_backup, "/home/<USER>/axmadcodes/AstroA_restored"
                )
                if not app_success:
                    self.logger.error("Application restore failed")
                    return False

            # Restore configuration
            config_backup = manifest['components']['configuration']['backup_file']
            if config_backup and os.path.exists(config_backup):
                config_success = await self.file_manager.restore_directory(
                    config_backup, "/etc/trading_restored"
                )
                if not config_success:
                    self.logger.warning("Configuration restore failed (non-critical)")

            self.logger.info(f"Disaster recovery completed successfully from snapshot: {snapshot_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error during disaster recovery: {e}")
            return False

def create_backup_jobs() -> List[BackupJob]:
    """Create default backup jobs"""
    jobs = []

    # Daily full database backup
    jobs.append(BackupJob(
        job_id="daily_db_full",
        name="Daily Full Database Backup",
        backup_type=BackupType.FULL,
        source_type="database",
        source_path="mathematical_trading",
        destination="local",
        schedule="@daily",
        retention_days=30,
        compression=True,
        encryption=True
    ))

    # Hourly incremental database backup
    jobs.append(BackupJob(
        job_id="hourly_db_incr",
        name="Hourly Incremental Database Backup",
        backup_type=BackupType.INCREMENTAL,
        source_type="database",
        source_path="mathematical_trading",
        destination="local",
        schedule="@hourly",
        retention_days=7,
        compression=True,
        encryption=True
    ))

    # Daily application files backup
    jobs.append(BackupJob(
        job_id="daily_app_files",
        name="Daily Application Files Backup",
        backup_type=BackupType.FULL,
        source_type="files",
        source_path="/home/<USER>/axmadcodes/AstroA",
        destination="local",
        schedule="@daily",
        retention_days=14,
        compression=True,
        encryption=True,
        metadata={
            'exclude_patterns': ['__pycache__', '*.pyc', 'venv', '.git', 'logs', 'node_modules']
        }
    ))

    # Weekly configuration backup
    jobs.append(BackupJob(
        job_id="weekly_config",
        name="Weekly Configuration Backup",
        backup_type=BackupType.FULL,
        source_type="files",
        source_path="/etc/trading",
        destination="local",
        schedule="@weekly",
        retention_days=90,
        compression=True,
        encryption=True
    ))

    return jobs

# Usage example
async def setup_backup_system():
    """Setup complete backup and recovery system"""
    config = {
        'database': {
            'host': 'localhost',
            'port': 5432,
            'user': 'trading_user',
            'password': 'hejhej',
            'database': 'mathematical_trading'
        },
        'backup_dir': '/var/backups/trading',
        'cloud_storage': {
            'backend': 'local',  # Change to 's3' for cloud storage
            'aws_access_key': os.getenv('AWS_ACCESS_KEY_ID'),
            'aws_secret_key': os.getenv('AWS_SECRET_ACCESS_KEY'),
            'aws_region': 'us-east-1',
            's3_bucket': 'trading-backups'
        }
    }

    # Create scheduler
    scheduler = BackupScheduler(config)

    # Add backup jobs
    backup_jobs = create_backup_jobs()
    for job in backup_jobs:
        scheduler.add_job(job)

    # Create disaster recovery manager
    dr_manager = DisasterRecoveryManager(config)

    return scheduler, dr_manager