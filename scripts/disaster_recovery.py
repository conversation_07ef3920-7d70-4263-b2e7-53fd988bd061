#!/usr/bin/env python3
"""
Disaster Recovery System for Trading Platform.
Handles backup, restoration, and emergency procedures.
"""

import os
import sys
import shutil
import subprocess
import json
import time
import logging
import psycopg2
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

@dataclass
class BackupMetadata:
    timestamp: str
    backup_type: str
    size_bytes: int
    files_count: int
    database_size: int
    checksum: str
    status: str

@dataclass
class RecoveryPlan:
    priority: int
    component: str
    recovery_time_objective: int  # minutes
    recovery_point_objective: int  # minutes
    procedure: str
    dependencies: List[str]

class DisasterRecoveryManager:
    """Comprehensive disaster recovery management system"""

    def __init__(self, config_path: str = "disaster_recovery_config.json"):
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()
        self.backup_dir = Path(self.config.get("backup_directory", "backups"))
        self.backup_dir.mkdir(exist_ok=True)

    def _load_config(self) -> Dict[str, Any]:
        """Load disaster recovery configuration"""
        default_config = {
            "backup_directory": "backups",
            "retention_days": 30,
            "backup_schedule": {
                "database": "daily",
                "code": "daily",
                "logs": "weekly",
                "models": "weekly"
            },
            "recovery_plans": [
                {
                    "priority": 1,
                    "component": "database",
                    "recovery_time_objective": 15,
                    "recovery_point_objective": 60,
                    "procedure": "restore_database_from_backup",
                    "dependencies": []
                },
                {
                    "priority": 2,
                    "component": "application",
                    "recovery_time_objective": 30,
                    "recovery_point_objective": 60,
                    "procedure": "restore_application_code",
                    "dependencies": ["database"]
                },
                {
                    "priority": 3,
                    "component": "models",
                    "recovery_time_objective": 60,
                    "recovery_point_objective": 1440,
                    "procedure": "restore_ml_models",
                    "dependencies": ["database", "application"]
                }
            ],
            "notification": {
                "email_enabled": False,
                "webhook_url": None,
                "alert_channels": []
            }
        }

        if Path(self.config_path).exists():
            try:
                with open(self.config_path) as f:
                    file_config = json.load(f)
                    default_config.update(file_config)
            except Exception as e:
                self.logger.warning(f"Failed to load config from {self.config_path}: {e}")

        return default_config

    def create_full_backup(self) -> BackupMetadata:
        """Create a complete system backup"""
        self.logger.info("Starting full system backup...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"full_backup_{timestamp}"
        backup_path = self.backup_dir / backup_name

        try:
            backup_path.mkdir()

            # Backup database
            db_backup_path = backup_path / "database"
            db_backup_path.mkdir()
            self._backup_database(db_backup_path)

            # Backup application code
            code_backup_path = backup_path / "code"
            self._backup_application_code(code_backup_path)

            # Backup ML models
            models_backup_path = backup_path / "models"
            self._backup_ml_models(models_backup_path)

            # Backup configuration
            config_backup_path = backup_path / "config"
            self._backup_configuration(config_backup_path)

            # Backup logs
            logs_backup_path = backup_path / "logs"
            self._backup_logs(logs_backup_path)

            # Calculate backup statistics
            size_bytes = self._calculate_directory_size(backup_path)
            files_count = self._count_files(backup_path)
            db_size = self._get_database_size()
            checksum = self._calculate_backup_checksum(backup_path)

            # Create metadata
            metadata = BackupMetadata(
                timestamp=timestamp,
                backup_type="full",
                size_bytes=size_bytes,
                files_count=files_count,
                database_size=db_size,
                checksum=checksum,
                status="completed"
            )

            # Save metadata
            with open(backup_path / "metadata.json", "w") as f:
                json.dump(asdict(metadata), f, indent=2)

            self.logger.info(f"Full backup completed: {backup_path}")
            return metadata

        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            metadata = BackupMetadata(
                timestamp=timestamp,
                backup_type="full",
                size_bytes=0,
                files_count=0,
                database_size=0,
                checksum="",
                status="failed"
            )
            return metadata

    def _backup_database(self, backup_path: Path):
        """Backup PostgreSQL database"""
        self.logger.info("Backing up database...")

        try:
            from config.settings import DATABASE_CONFIG

            db_name = DATABASE_CONFIG["database"]
            db_user = DATABASE_CONFIG["user"]
            db_host = DATABASE_CONFIG.get("host", "localhost")
            db_port = DATABASE_CONFIG.get("port", 5432)

            dump_file = backup_path / f"{db_name}.sql"

            env = os.environ.copy()
            env["PGPASSWORD"] = DATABASE_CONFIG["password"]

            subprocess.run([
                "pg_dump",
                "-h", db_host,
                "-p", str(db_port),
                "-U", db_user,
                "-d", db_name,
                "-f", str(dump_file),
                "--verbose"
            ], env=env, check=True)

            # Compress the dump
            subprocess.run([
                "gzip", str(dump_file)
            ], check=True)

            self.logger.info(f"Database backup completed: {dump_file}.gz")

        except Exception as e:
            self.logger.error(f"Database backup failed: {e}")
            raise

    def _backup_application_code(self, backup_path: Path):
        """Backup application source code"""
        self.logger.info("Backing up application code...")

        source_dirs = ["agents", "ml_models", "shared", "config", "scripts", "api"]
        backup_path.mkdir(exist_ok=True)

        for source_dir in source_dirs:
            if Path(source_dir).exists():
                dest_dir = backup_path / source_dir
                shutil.copytree(source_dir, dest_dir, ignore=shutil.ignore_patterns(
                    "*.pyc", "__pycache__", "*.tmp", ".DS_Store"
                ))

        # Backup important files
        important_files = [
            "requirements.txt", "main.py", "README.md",
            ".env.example", "setup_db.sql"
        ]

        for file_name in important_files:
            if Path(file_name).exists():
                shutil.copy2(file_name, backup_path)

        self.logger.info("Application code backup completed")

    def _backup_ml_models(self, backup_path: Path):
        """Backup ML models and weights"""
        self.logger.info("Backing up ML models...")

        backup_path.mkdir(exist_ok=True)
        models_dir = Path("ml_models")

        if models_dir.exists():
            # Copy model files
            for model_file in models_dir.glob("**/*.pkl"):
                dest_file = backup_path / model_file.relative_to(models_dir)
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(model_file, dest_file)

            for model_file in models_dir.glob("**/*.pt"):
                dest_file = backup_path / model_file.relative_to(models_dir)
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(model_file, dest_file)

        # Backup model metadata
        data_dir = Path("data")
        if data_dir.exists():
            model_data_path = backup_path / "data"
            model_data_path.mkdir(exist_ok=True)

            for data_file in data_dir.glob("**/model_*.json"):
                dest_file = model_data_path / data_file.relative_to(data_dir)
                dest_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(data_file, dest_file)

        self.logger.info("ML models backup completed")

    def _backup_configuration(self, backup_path: Path):
        """Backup configuration files"""
        self.logger.info("Backing up configuration...")

        backup_path.mkdir(exist_ok=True)

        config_files = [
            ".env", "monitoring/config.json",
            self.config_path
        ]

        for config_file in config_files:
            if Path(config_file).exists():
                dest_file = backup_path / Path(config_file).name
                shutil.copy2(config_file, dest_file)

        self.logger.info("Configuration backup completed")

    def _backup_logs(self, backup_path: Path):
        """Backup recent log files"""
        self.logger.info("Backing up logs...")

        backup_path.mkdir(exist_ok=True)
        logs_dir = Path("logs")

        if logs_dir.exists():
            # Only backup logs from last 7 days
            cutoff_date = datetime.now() - timedelta(days=7)

            for log_file in logs_dir.glob("**/*.log"):
                if log_file.stat().st_mtime > cutoff_date.timestamp():
                    dest_file = backup_path / log_file.relative_to(logs_dir)
                    dest_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(log_file, dest_file)

        self.logger.info("Logs backup completed")

    def restore_from_backup(self, backup_name: str, components: List[str] = None) -> bool:
        """Restore system from backup"""
        self.logger.info(f"Starting restoration from backup: {backup_name}")

        backup_path = self.backup_dir / backup_name

        if not backup_path.exists():
            self.logger.error(f"Backup not found: {backup_path}")
            return False

        # Load backup metadata
        metadata_file = backup_path / "metadata.json"
        if not metadata_file.exists():
            self.logger.error("Backup metadata not found")
            return False

        with open(metadata_file) as f:
            metadata = json.load(f)

        # Verify backup integrity
        if not self._verify_backup_integrity(backup_path, metadata):
            self.logger.error("Backup integrity check failed")
            return False

        components = components or ["database", "code", "models", "config"]

        try:
            # Execute recovery plan in priority order
            recovery_plans = [RecoveryPlan(**plan) for plan in self.config["recovery_plans"]]
            recovery_plans.sort(key=lambda x: x.priority)

            for plan in recovery_plans:
                if plan.component in components:
                    self.logger.info(f"Restoring {plan.component}...")
                    success = self._execute_recovery_procedure(plan, backup_path)
                    if not success:
                        self.logger.error(f"Failed to restore {plan.component}")
                        return False

            self.logger.info("System restoration completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Restoration failed: {e}")
            return False

    def _execute_recovery_procedure(self, plan: RecoveryPlan, backup_path: Path) -> bool:
        """Execute a specific recovery procedure"""
        if plan.procedure == "restore_database_from_backup":
            return self._restore_database(backup_path / "database")
        elif plan.procedure == "restore_application_code":
            return self._restore_application_code(backup_path / "code")
        elif plan.procedure == "restore_ml_models":
            return self._restore_ml_models(backup_path / "models")
        else:
            self.logger.warning(f"Unknown recovery procedure: {plan.procedure}")
            return False

    def _restore_database(self, backup_path: Path) -> bool:
        """Restore database from backup"""
        try:
            from config.settings import DATABASE_CONFIG

            db_name = DATABASE_CONFIG["database"]
            db_user = DATABASE_CONFIG["user"]
            db_host = DATABASE_CONFIG.get("host", "localhost")
            db_port = DATABASE_CONFIG.get("port", 5432)

            dump_file = backup_path / f"{db_name}.sql.gz"

            if not dump_file.exists():
                self.logger.error(f"Database dump not found: {dump_file}")
                return False

            # Decompress dump
            subprocess.run(["gunzip", str(dump_file)], check=True)
            decompressed_file = backup_path / f"{db_name}.sql"

            # Drop and recreate database
            env = os.environ.copy()
            env["PGPASSWORD"] = DATABASE_CONFIG["password"]

            subprocess.run([
                "dropdb", "-h", db_host, "-p", str(db_port),
                "-U", db_user, db_name
            ], env=env)

            subprocess.run([
                "createdb", "-h", db_host, "-p", str(db_port),
                "-U", db_user, db_name
            ], env=env, check=True)

            # Restore from dump
            subprocess.run([
                "psql", "-h", db_host, "-p", str(db_port),
                "-U", db_user, "-d", db_name,
                "-f", str(decompressed_file)
            ], env=env, check=True)

            self.logger.info("Database restoration completed")
            return True

        except Exception as e:
            self.logger.error(f"Database restoration failed: {e}")
            return False

    def _restore_application_code(self, backup_path: Path) -> bool:
        """Restore application code from backup"""
        try:
            if not backup_path.exists():
                self.logger.error(f"Code backup not found: {backup_path}")
                return False

            # Backup current code
            current_backup = f"current_code_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            current_backup_path = self.backup_dir / current_backup
            current_backup_path.mkdir()

            for source_dir in ["agents", "ml_models", "shared", "config"]:
                if Path(source_dir).exists():
                    shutil.copytree(source_dir, current_backup_path / source_dir)

            # Restore from backup
            for item in backup_path.iterdir():
                if item.is_dir():
                    if Path(item.name).exists():
                        shutil.rmtree(item.name)
                    shutil.copytree(item, item.name)
                else:
                    shutil.copy2(item, item.name)

            self.logger.info("Application code restoration completed")
            return True

        except Exception as e:
            self.logger.error(f"Code restoration failed: {e}")
            return False

    def _restore_ml_models(self, backup_path: Path) -> bool:
        """Restore ML models from backup"""
        try:
            if not backup_path.exists():
                self.logger.warning(f"Models backup not found: {backup_path}")
                return True  # Not critical

            models_dir = Path("ml_models")
            if models_dir.exists():
                # Backup current models
                current_models_backup = self.backup_dir / f"current_models_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copytree(models_dir, current_models_backup)

            # Restore models
            for item in backup_path.rglob("*"):
                if item.is_file():
                    dest_path = Path("ml_models") / item.relative_to(backup_path)
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, dest_path)

            self.logger.info("ML models restoration completed")
            return True

        except Exception as e:
            self.logger.error(f"Models restoration failed: {e}")
            return False

    def test_disaster_recovery(self) -> bool:
        """Test disaster recovery procedures"""
        self.logger.info("Starting disaster recovery test...")

        try:
            # Create test backup
            test_backup = self.create_full_backup()
            if test_backup.status != "completed":
                self.logger.error("Test backup creation failed")
                return False

            # Test backup verification
            backup_path = self.backup_dir / f"full_backup_{test_backup.timestamp}"
            if not self._verify_backup_integrity(backup_path, test_backup):
                self.logger.error("Backup integrity verification failed")
                return False

            # Test partial restoration (non-destructive)
            test_restore_path = Path("/tmp/dr_test")
            if test_restore_path.exists():
                shutil.rmtree(test_restore_path)

            test_restore_path.mkdir()

            # Copy backup for testing
            shutil.copytree(backup_path, test_restore_path / "test_backup")

            self.logger.info("Disaster recovery test completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Disaster recovery test failed: {e}")
            return False

    def cleanup_old_backups(self):
        """Clean up old backups based on retention policy"""
        self.logger.info("Cleaning up old backups...")

        retention_days = self.config.get("retention_days", 30)
        cutoff_date = datetime.now() - timedelta(days=retention_days)

        for backup_dir in self.backup_dir.iterdir():
            if backup_dir.is_dir() and backup_dir.name.startswith("full_backup_"):
                try:
                    # Extract timestamp from backup name
                    timestamp_str = backup_dir.name.replace("full_backup_", "")
                    backup_date = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")

                    if backup_date < cutoff_date:
                        self.logger.info(f"Removing old backup: {backup_dir}")
                        shutil.rmtree(backup_dir)
                except Exception as e:
                    self.logger.warning(f"Failed to process backup {backup_dir}: {e}")

    def list_backups(self) -> List[Dict[str, Any]]:
        """List available backups"""
        backups = []

        for backup_dir in self.backup_dir.iterdir():
            if backup_dir.is_dir() and backup_dir.name.startswith("full_backup_"):
                metadata_file = backup_dir / "metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file) as f:
                            metadata = json.load(f)
                            backups.append(metadata)
                    except Exception as e:
                        self.logger.warning(f"Failed to read metadata for {backup_dir}: {e}")

        return sorted(backups, key=lambda x: x["timestamp"], reverse=True)

    def _verify_backup_integrity(self, backup_path: Path, metadata: BackupMetadata) -> bool:
        """Verify backup integrity using checksums"""
        try:
            current_checksum = self._calculate_backup_checksum(backup_path)
            return current_checksum == metadata.checksum
        except Exception as e:
            self.logger.error(f"Integrity verification failed: {e}")
            return False

    def _calculate_backup_checksum(self, backup_path: Path) -> str:
        """Calculate backup checksum"""
        import hashlib

        md5_hash = hashlib.md5()
        for file_path in sorted(backup_path.rglob("*")):
            if file_path.is_file():
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        md5_hash.update(chunk)

        return md5_hash.hexdigest()

    def _calculate_directory_size(self, path: Path) -> int:
        """Calculate total size of directory"""
        total_size = 0
        for file_path in path.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size

    def _count_files(self, path: Path) -> int:
        """Count total number of files in directory"""
        return len([f for f in path.rglob("*") if f.is_file()])

    def _get_database_size(self) -> int:
        """Get database size in bytes"""
        try:
            from config.settings import DATABASE_CONFIG

            conn = psycopg2.connect(**DATABASE_CONFIG)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT pg_database_size(current_database())
            """)

            size = cursor.fetchone()[0]
            conn.close()

            return size
        except Exception:
            return 0

def main():
    """Main entry point for disaster recovery operations"""
    import argparse

    parser = argparse.ArgumentParser(description="Disaster Recovery Management")
    parser.add_argument("action", choices=[
        "backup", "restore", "test", "cleanup", "list"
    ], help="Action to perform")
    parser.add_argument("--backup-name", help="Backup name for restore operation")
    parser.add_argument("--components", nargs="+", help="Components to restore")
    parser.add_argument("--config", help="Configuration file path")

    args = parser.parse_args()

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('disaster_recovery.log'),
            logging.StreamHandler()
        ]
    )

    # Initialize disaster recovery manager
    config_path = args.config or "disaster_recovery_config.json"
    dr_manager = DisasterRecoveryManager(config_path)

    try:
        if args.action == "backup":
            metadata = dr_manager.create_full_backup()
            print(f"Backup completed: {metadata.status}")
            if metadata.status == "completed":
                print(f"Size: {metadata.size_bytes / 1024 / 1024:.2f} MB")
                print(f"Files: {metadata.files_count}")

        elif args.action == "restore":
            if not args.backup_name:
                print("Error: --backup-name required for restore operation")
                sys.exit(1)

            success = dr_manager.restore_from_backup(
                args.backup_name,
                args.components
            )
            print(f"Restoration {'completed' if success else 'failed'}")

        elif args.action == "test":
            success = dr_manager.test_disaster_recovery()
            print(f"Disaster recovery test {'passed' if success else 'failed'}")

        elif args.action == "cleanup":
            dr_manager.cleanup_old_backups()
            print("Backup cleanup completed")

        elif args.action == "list":
            backups = dr_manager.list_backups()
            print("\nAvailable backups:")
            print("-" * 80)
            for backup in backups:
                print(f"Timestamp: {backup['timestamp']}")
                print(f"Type: {backup['backup_type']}")
                print(f"Status: {backup['status']}")
                print(f"Size: {backup['size_bytes'] / 1024 / 1024:.2f} MB")
                print(f"Files: {backup['files_count']}")
                print("-" * 40)

    except Exception as e:
        logging.error(f"Operation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()