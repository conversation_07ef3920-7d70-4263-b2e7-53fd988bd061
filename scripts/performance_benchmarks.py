#!/usr/bin/env python3
"""
Performance benchmarking suite for the trading system.
Measures performance of critical components and tracks regression.
"""

import time
import json
import psutil
import tracemalloc
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

@dataclass
class BenchmarkResult:
    name: str
    duration: float
    memory_peak: float
    memory_current: float
    cpu_percent: float
    throughput: float
    metadata: Dict[str, Any]

class PerformanceBenchmarks:
    """Performance benchmarking suite"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.results: List[BenchmarkResult] = []

    def run_all_benchmarks(self) -> List[BenchmarkResult]:
        """Run all performance benchmarks"""
        self.logger.info("Starting performance benchmarks...")

        # Data processing benchmarks
        self._benchmark_data_processing()
        self._benchmark_technical_indicators()

        # ML model benchmarks
        self._benchmark_model_training()
        self._benchmark_model_prediction()

        # Database benchmarks
        self._benchmark_database_operations()

        # Trading strategy benchmarks
        self._benchmark_strategy_evaluation()

        self._save_results()
        self._print_summary()

        return self.results

    def _benchmark_data_processing(self):
        """Benchmark data processing operations"""
        self.logger.info("Benchmarking data processing...")

        # Generate test data
        data_sizes = [1000, 10000, 100000]

        for size in data_sizes:
            test_data = self._generate_market_data(size)

            # Benchmark data cleaning and preprocessing
            tracemalloc.start()
            start_time = time.time()
            start_cpu = psutil.cpu_percent()

            # Simulate data processing operations
            processed_data = self._process_market_data(test_data)

            duration = time.time() - start_time
            current_memory, peak_memory = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            end_cpu = psutil.cpu_percent()

            throughput = size / duration  # records per second

            self.results.append(BenchmarkResult(
                name=f"data_processing_{size}_records",
                duration=duration,
                memory_peak=peak_memory / 1024 / 1024,  # MB
                memory_current=current_memory / 1024 / 1024,  # MB
                cpu_percent=(start_cpu + end_cpu) / 2,
                throughput=throughput,
                metadata={"data_size": size, "operation": "data_processing"}
            ))

    def _benchmark_technical_indicators(self):
        """Benchmark technical indicator calculations"""
        self.logger.info("Benchmarking technical indicators...")

        data = self._generate_market_data(10000)

        indicators = [
            ("SMA_20", lambda df: df['close'].rolling(window=20).mean()),
            ("EMA_20", lambda df: df['close'].ewm(span=20).mean()),
            ("RSI_14", self._calculate_rsi),
            ("MACD", self._calculate_macd),
            ("Bollinger_Bands", self._calculate_bollinger_bands)
        ]

        for name, indicator_func in indicators:
            tracemalloc.start()
            start_time = time.time()

            try:
                result = indicator_func(data)
                duration = time.time() - start_time
                current_memory, peak_memory = tracemalloc.get_traced_memory()
                tracemalloc.stop()

                self.results.append(BenchmarkResult(
                    name=f"indicator_{name}",
                    duration=duration,
                    memory_peak=peak_memory / 1024 / 1024,
                    memory_current=current_memory / 1024 / 1024,
                    cpu_percent=psutil.cpu_percent(),
                    throughput=len(data) / duration,
                    metadata={"indicator": name, "data_size": len(data)}
                ))
            except Exception as e:
                self.logger.error(f"Failed to benchmark {name}: {e}")
                tracemalloc.stop()

    def _benchmark_model_training(self):
        """Benchmark ML model training performance"""
        self.logger.info("Benchmarking model training...")

        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.linear_model import LinearRegression
            import torch
            import torch.nn as nn

            # Generate training data
            X = np.random.random((10000, 20))
            y = np.random.random(10000)

            models = [
                ("LinearRegression", LinearRegression()),
                ("RandomForest_10", RandomForestRegressor(n_estimators=10)),
                ("RandomForest_100", RandomForestRegressor(n_estimators=100))
            ]

            for name, model in models:
                tracemalloc.start()
                start_time = time.time()

                model.fit(X, y)

                duration = time.time() - start_time
                current_memory, peak_memory = tracemalloc.get_traced_memory()
                tracemalloc.stop()

                self.results.append(BenchmarkResult(
                    name=f"training_{name}",
                    duration=duration,
                    memory_peak=peak_memory / 1024 / 1024,
                    memory_current=current_memory / 1024 / 1024,
                    cpu_percent=psutil.cpu_percent(),
                    throughput=len(X) / duration,
                    metadata={"model": name, "samples": len(X), "features": X.shape[1]}
                ))

            # PyTorch neural network benchmark
            if torch.cuda.is_available():
                self._benchmark_pytorch_training(X, y)

        except ImportError as e:
            self.logger.warning(f"Skipping ML benchmarks: {e}")

    def _benchmark_pytorch_training(self, X, y):
        """Benchmark PyTorch neural network training"""
        import torch
        import torch.nn as nn
        import torch.optim as optim

        class SimpleNN(nn.Module):
            def __init__(self, input_size):
                super().__init__()
                self.layers = nn.Sequential(
                    nn.Linear(input_size, 64),
                    nn.ReLU(),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1)
                )

            def forward(self, x):
                return self.layers(x)

        # Convert to tensors
        X_tensor = torch.FloatTensor(X[:1000])  # Smaller dataset for speed
        y_tensor = torch.FloatTensor(y[:1000]).unsqueeze(1)

        model = SimpleNN(X.shape[1])
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters())

        tracemalloc.start()
        start_time = time.time()

        # Training loop
        for epoch in range(100):
            optimizer.zero_grad()
            outputs = model(X_tensor)
            loss = criterion(outputs, y_tensor)
            loss.backward()
            optimizer.step()

        duration = time.time() - start_time
        current_memory, peak_memory = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        self.results.append(BenchmarkResult(
            name="training_pytorch_nn",
            duration=duration,
            memory_peak=peak_memory / 1024 / 1024,
            memory_current=current_memory / 1024 / 1024,
            cpu_percent=psutil.cpu_percent(),
            throughput=len(X_tensor) * 100 / duration,  # samples * epochs / time
            metadata={"model": "PyTorch_NN", "epochs": 100, "samples": len(X_tensor)}
        ))

    def _benchmark_model_prediction(self):
        """Benchmark model prediction performance"""
        self.logger.info("Benchmarking model predictions...")

        try:
            from sklearn.ensemble import RandomForestRegressor

            # Train a model for prediction benchmarking
            X_train = np.random.random((1000, 20))
            y_train = np.random.random(1000)
            model = RandomForestRegressor(n_estimators=10)
            model.fit(X_train, y_train)

            # Benchmark prediction latency
            batch_sizes = [1, 10, 100, 1000]

            for batch_size in batch_sizes:
                X_test = np.random.random((batch_size, 20))

                tracemalloc.start()
                start_time = time.time()

                predictions = model.predict(X_test)

                duration = time.time() - start_time
                current_memory, peak_memory = tracemalloc.get_traced_memory()
                tracemalloc.stop()

                latency_per_sample = duration / batch_size * 1000  # ms per sample

                self.results.append(BenchmarkResult(
                    name=f"prediction_batch_{batch_size}",
                    duration=duration,
                    memory_peak=peak_memory / 1024 / 1024,
                    memory_current=current_memory / 1024 / 1024,
                    cpu_percent=psutil.cpu_percent(),
                    throughput=batch_size / duration,
                    metadata={
                        "batch_size": batch_size,
                        "latency_per_sample_ms": latency_per_sample
                    }
                ))

        except ImportError as e:
            self.logger.warning(f"Skipping prediction benchmarks: {e}")

    def _benchmark_database_operations(self):
        """Benchmark database operations"""
        self.logger.info("Benchmarking database operations...")

        try:
            import psycopg2
            from config.settings import DATABASE_CONFIG

            conn = psycopg2.connect(**DATABASE_CONFIG)
            cursor = conn.cursor()

            # Benchmark insert operations
            self._benchmark_db_inserts(cursor)

            # Benchmark query operations
            self._benchmark_db_queries(cursor)

            conn.close()

        except Exception as e:
            self.logger.warning(f"Skipping database benchmarks: {e}")

    def _benchmark_db_inserts(self, cursor):
        """Benchmark database insert operations"""
        batch_sizes = [100, 1000, 5000]

        for batch_size in batch_sizes:
            # Generate test data
            test_data = []
            for i in range(batch_size):
                test_data.append((
                    f"TEST_SYMBOL_{i}",
                    time.time(),
                    100.0 + np.random.random(),
                    100.0 + np.random.random(),
                    100.0 + np.random.random(),
                    100.0 + np.random.random(),
                    np.random.randint(1000, 100000)
                ))

            tracemalloc.start()
            start_time = time.time()

            # Batch insert
            cursor.executemany("""
                INSERT INTO market_data (symbol, timestamp, open_price, high_price, low_price, close_price, volume)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, test_data)

            duration = time.time() - start_time
            current_memory, peak_memory = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            self.results.append(BenchmarkResult(
                name=f"db_insert_batch_{batch_size}",
                duration=duration,
                memory_peak=peak_memory / 1024 / 1024,
                memory_current=current_memory / 1024 / 1024,
                cpu_percent=psutil.cpu_percent(),
                throughput=batch_size / duration,
                metadata={"operation": "insert", "batch_size": batch_size}
            ))

    def _benchmark_db_queries(self, cursor):
        """Benchmark database query operations"""
        queries = [
            ("simple_select", "SELECT COUNT(*) FROM market_data"),
            ("filtered_select", "SELECT * FROM market_data WHERE symbol = 'AAPL' LIMIT 1000"),
            ("aggregation", "SELECT symbol, AVG(close_price) FROM market_data GROUP BY symbol LIMIT 100")
        ]

        for name, query in queries:
            tracemalloc.start()
            start_time = time.time()

            cursor.execute(query)
            results = cursor.fetchall()

            duration = time.time() - start_time
            current_memory, peak_memory = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            self.results.append(BenchmarkResult(
                name=f"db_query_{name}",
                duration=duration,
                memory_peak=peak_memory / 1024 / 1024,
                memory_current=current_memory / 1024 / 1024,
                cpu_percent=psutil.cpu_percent(),
                throughput=len(results) / duration if results else 0,
                metadata={"operation": "query", "query_type": name}
            ))

    def _benchmark_strategy_evaluation(self):
        """Benchmark trading strategy evaluation"""
        self.logger.info("Benchmarking strategy evaluation...")

        # Simulate strategy evaluation with test data
        market_data = self._generate_market_data(1000)

        tracemalloc.start()
        start_time = time.time()

        # Simulate strategy evaluation
        signals = []
        for i in range(len(market_data)):
            # Simple moving average crossover strategy
            if i >= 20:
                sma_short = market_data['close'].iloc[i-10:i].mean()
                sma_long = market_data['close'].iloc[i-20:i].mean()

                if sma_short > sma_long:
                    signals.append('BUY')
                elif sma_short < sma_long:
                    signals.append('SELL')
                else:
                    signals.append('HOLD')

        duration = time.time() - start_time
        current_memory, peak_memory = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        self.results.append(BenchmarkResult(
            name="strategy_evaluation",
            duration=duration,
            memory_peak=peak_memory / 1024 / 1024,
            memory_current=current_memory / 1024 / 1024,
            cpu_percent=psutil.cpu_percent(),
            throughput=len(market_data) / duration,
            metadata={"signals_generated": len(signals), "data_points": len(market_data)}
        ))

    def _generate_market_data(self, size: int) -> pd.DataFrame:
        """Generate synthetic market data for testing"""
        dates = pd.date_range(start='2023-01-01', periods=size, freq='1min')
        data = {
            'timestamp': dates,
            'open': 100 + np.cumsum(np.random.randn(size) * 0.1),
            'high': 100 + np.cumsum(np.random.randn(size) * 0.1) + np.random.rand(size),
            'low': 100 + np.cumsum(np.random.randn(size) * 0.1) - np.random.rand(size),
            'close': 100 + np.cumsum(np.random.randn(size) * 0.1),
            'volume': np.random.randint(1000, 100000, size)
        }
        return pd.DataFrame(data)

    def _process_market_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Simulate data processing operations"""
        # Data cleaning and feature engineering
        data['returns'] = data['close'].pct_change()
        data['volatility'] = data['returns'].rolling(window=20).std()
        data['sma_20'] = data['close'].rolling(window=20).mean()
        data['sma_50'] = data['close'].rolling(window=50).mean()
        data.dropna(inplace=True)
        return data

    def _calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        close = data['close']
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate MACD indicator"""
        close = data['close']
        exp1 = close.ewm(span=12).mean()
        exp2 = close.ewm(span=26).mean()
        macd = exp1 - exp2
        signal = macd.ewm(span=9).mean()
        return pd.DataFrame({'macd': macd, 'signal': signal, 'histogram': macd - signal})

    def _calculate_bollinger_bands(self, data: pd.DataFrame, period: int = 20) -> pd.DataFrame:
        """Calculate Bollinger Bands"""
        close = data['close']
        sma = close.rolling(window=period).mean()
        std = close.rolling(window=period).std()
        upper = sma + (std * 2)
        lower = sma - (std * 2)
        return pd.DataFrame({'upper': upper, 'middle': sma, 'lower': lower})

    def _save_results(self):
        """Save benchmark results to file"""
        results_dir = Path("performance_results")
        results_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"performance_benchmark_{timestamp}.json"

        # Convert results to dict for JSON serialization
        results_data = {
            "timestamp": datetime.now().isoformat(),
            "benchmarks": [asdict(result) for result in self.results]
        }

        with open(results_file, "w") as f:
            json.dump(results_data, f, indent=2)

        self.logger.info(f"Performance results saved to {results_file}")

    def _print_summary(self):
        """Print benchmark summary"""
        print("\n" + "="*80)
        print("PERFORMANCE BENCHMARK SUMMARY")
        print("="*80)

        categories = {}
        for result in self.results:
            category = result.name.split('_')[0]
            if category not in categories:
                categories[category] = []
            categories[category].append(result)

        for category, results in categories.items():
            print(f"\n{category.upper()} BENCHMARKS:")
            print("-" * 40)

            for result in results:
                print(f"  {result.name}:")
                print(f"    Duration: {result.duration:.4f}s")
                print(f"    Memory Peak: {result.memory_peak:.2f}MB")
                print(f"    Throughput: {result.throughput:.0f} ops/sec")
                if "latency_per_sample_ms" in result.metadata:
                    print(f"    Latency: {result.metadata['latency_per_sample_ms']:.3f}ms per sample")

        print("\n" + "="*80)

def main():
    """Main entry point"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    benchmarks = PerformanceBenchmarks()
    benchmarks.run_all_benchmarks()

if __name__ == "__main__":
    main()