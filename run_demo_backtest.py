#!/usr/bin/env python3
"""
AstroA Demo Backtest - Quick demonstration run
Runs a compressed version to show system capabilities
"""
import asyncio
import logging
import sys
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import random

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Config

class DemoBacktestRunner:
    """Runs a demonstration backtest showing AstroA capabilities"""

    def __init__(self, cycles=10):
        self.start_time = datetime.now()
        self.cycles = cycles
        self.current_cycle = 0

        # Demo portfolio simulation
        self.initial_value = 100000
        self.current_value = self.initial_value
        self.cash = 80000
        self.positions = {}
        self.trades_executed = []

        # Performance tracking
        self.performance_data = {
            'start_time': self.start_time.isoformat(),
            'portfolio_snapshots': [],
            'trades_executed': [],
            'data_points_collected': 0,
            'analysis_cycles': 0,
            'errors': [],
            'risk_assessments': [],
            'market_conditions': []
        }

        # Simulated market data
        self.symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'SOL/USDT', 'DOT/USDT']
        self.prices = {
            'BTC/USDT': 45000,
            'ETH/USDT': 3200,
            'ADA/USDT': 0.85,
            'SOL/USDT': 180,
            'DOT/USDT': 12.5
        }

        self.setup_logging()
        self.logger = logging.getLogger('DemoBacktest')

    def setup_logging(self):
        """Setup logging"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(level=logging.INFO, format=log_format)

    def simulate_market_movement(self):
        """Simulate realistic market price movements"""
        for symbol in self.symbols:
            # Random walk with slight trend
            change_percent = random.uniform(-0.02, 0.025)  # -2% to +2.5%
            self.prices[symbol] *= (1 + change_percent)

    def generate_trading_signal(self):
        """Generate simulated trading signals"""
        if random.random() < 0.3:  # 30% chance of signal
            symbol = random.choice(self.symbols)
            action = random.choice(['buy', 'sell'])
            quantity = random.uniform(0.1, 2.0)
            confidence = random.uniform(0.6, 0.95)

            return {
                'symbol': symbol,
                'action': action,
                'quantity': quantity,
                'price': self.prices[symbol],
                'timestamp': datetime.now().isoformat(),
                'strategy_id': random.choice(['momentum_001', 'mean_reversion_001']),
                'confidence': confidence
            }
        return None

    def execute_trade(self, signal):
        """Execute a simulated trade"""
        symbol = signal['symbol']
        action = signal['action']
        quantity = signal['quantity']
        price = signal['price']

        trade_value = quantity * price

        if action == 'buy' and trade_value <= self.cash:
            self.cash -= trade_value
            if symbol in self.positions:
                self.positions[symbol] += quantity
            else:
                self.positions[symbol] = quantity

            self.trades_executed.append(signal)
            self.logger.info(f"🟢 BUY {quantity:.2f} {symbol} at ${price:.2f}")

        elif action == 'sell' and symbol in self.positions and self.positions[symbol] >= quantity:
            self.cash += trade_value
            self.positions[symbol] -= quantity
            if self.positions[symbol] <= 0:
                del self.positions[symbol]

            self.trades_executed.append(signal)
            self.logger.info(f"🔴 SELL {quantity:.2f} {symbol} at ${price:.2f}")

    def calculate_portfolio_value(self):
        """Calculate current portfolio value"""
        positions_value = 0
        for symbol, quantity in self.positions.items():
            positions_value += quantity * self.prices[symbol]

        self.current_value = self.cash + positions_value
        return self.current_value

    def take_snapshot(self):
        """Take portfolio snapshot"""
        portfolio_value = self.calculate_portfolio_value()

        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'total_value': portfolio_value,
            'cash': self.cash,
            'positions_value': portfolio_value - self.cash,
            'total_pnl': portfolio_value - self.initial_value,
            'daily_pnl': 0,  # Simplified for demo
            'positions_count': len(self.positions),
            'risk_score': random.uniform(0.2, 0.6),
            'max_drawdown': 0,  # Simplified for demo
            'positions': []
        }

        # Add position details
        for symbol, quantity in self.positions.items():
            snapshot['positions'].append({
                'symbol': symbol,
                'quantity': quantity,
                'current_price': self.prices[symbol],
                'value': quantity * self.prices[symbol]
            })

        self.performance_data['portfolio_snapshots'].append(snapshot)

        # Add risk assessment
        self.performance_data['risk_assessments'].append({
            'timestamp': datetime.now().isoformat(),
            'portfolio_value': portfolio_value,
            'risk_score': snapshot['risk_score'],
            'positions_count': len(self.positions),
            'total_pnl': portfolio_value - self.initial_value
        })

    async def run_demo_cycle(self):
        """Run a single demo cycle"""
        self.current_cycle += 1
        self.logger.info(f"📊 Running demo cycle {self.current_cycle}/{self.cycles}")

        # Simulate market movement
        self.simulate_market_movement()

        # Simulate data collection
        self.performance_data['data_points_collected'] += len(self.symbols) * 3  # 3 timeframes

        # Generate and potentially execute trading signal
        signal = self.generate_trading_signal()
        if signal:
            self.execute_trade(signal)
            self.performance_data['trades_executed'].append(signal)

        # Take portfolio snapshot
        self.take_snapshot()

        # Simulate analysis results
        self.performance_data['market_conditions'].append({
            'timestamp': datetime.now().isoformat(),
            'analysis': {
                'market_sentiment': random.choice(['bullish', 'bearish', 'neutral']),
                'volatility': random.uniform(0.15, 0.35),
                'trend_strength': random.uniform(0.3, 0.8)
            }
        })

        self.performance_data['analysis_cycles'] = self.current_cycle

        # Show progress
        portfolio_value = self.calculate_portfolio_value()
        pnl_percent = ((portfolio_value - self.initial_value) / self.initial_value) * 100
        self.logger.info(f"💰 Portfolio: ${portfolio_value:,.2f} ({pnl_percent:+.2f}%)")

    async def run_demo_backtest(self):
        """Run the complete demo backtest"""
        self.logger.info("🚀 Starting AstroA Demo Backtest")
        self.logger.info(f"📈 Initial Portfolio: ${self.initial_value:,}")

        try:
            for i in range(self.cycles):
                await self.run_demo_cycle()

                # Small delay between cycles
                await asyncio.sleep(0.5)

                # Show progress
                progress = ((i + 1) / self.cycles) * 100
                print(f"Progress: {progress:.1f}% complete")

        except KeyboardInterrupt:
            self.logger.info("🛑 Demo backtest interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Error during demo backtest: {str(e)}")

        self.logger.info("🏁 Demo backtest completed")

    def calculate_performance_metrics(self):
        """Calculate performance metrics"""
        snapshots = self.performance_data['portfolio_snapshots']
        if not snapshots:
            return {}

        initial_value = snapshots[0]['total_value']
        final_value = snapshots[-1]['total_value']

        # Calculate returns
        total_return = (final_value - initial_value) / initial_value

        # Calculate max drawdown
        peak_value = initial_value
        max_drawdown = 0
        for snapshot in snapshots:
            value = snapshot['total_value']
            peak_value = max(peak_value, value)
            drawdown = (peak_value - value) / peak_value
            max_drawdown = max(max_drawdown, drawdown)

        return {
            'initial_value': initial_value,
            'final_value': final_value,
            'total_return': total_return,
            'total_return_percent': total_return * 100,
            'max_drawdown': max_drawdown,
            'max_drawdown_percent': max_drawdown * 100,
            'sharpe_ratio': random.uniform(0.5, 1.2),  # Simulated
            'total_trades': len(self.performance_data['trades_executed']),
            'analysis_cycles': self.performance_data['analysis_cycles'],
            'error_count': len(self.performance_data['errors']),
            'avg_positions': sum(s['positions_count'] for s in snapshots) / len(snapshots),
            'duration_hours': (self.cycles * 0.5) / 60  # Convert cycles to hours
        }

    def save_results(self):
        """Save results to file"""
        # Calculate final metrics
        metrics = self.calculate_performance_metrics()
        self.performance_data['performance_metrics'] = metrics
        self.performance_data['end_time'] = datetime.now().isoformat()

        # Save to file
        Config.DATA_DIR.mkdir(exist_ok=True)
        results_file = Config.DATA_DIR / f"demo_backtest_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(results_file, 'w') as f:
            json.dump(self.performance_data, f, indent=2)

        self.logger.info(f"📊 Results saved to: {results_file}")

        # Print summary
        self.print_summary(metrics)

        return results_file

    def print_summary(self, metrics):
        """Print demo summary"""
        print("\n" + "="*60)
        print("🎯 ASTROA DEMO BACKTEST SUMMARY")
        print("="*60)
        print(f"⏱️  Cycles Run: {metrics['analysis_cycles']}")
        print(f"💰 Initial Portfolio: ${metrics['initial_value']:,.2f}")
        print(f"💰 Final Portfolio: ${metrics['final_value']:,.2f}")
        print(f"📈 Total Return: {metrics['total_return_percent']:+.2f}%")
        print(f"📉 Max Drawdown: {metrics['max_drawdown_percent']:.2f}%")
        print(f"🔄 Total Trades: {metrics['total_trades']}")
        print(f"📊 Analysis Cycles: {metrics['analysis_cycles']}")
        print(f"📍 Avg Positions: {metrics['avg_positions']:.1f}")
        print(f"📈 Simulated Sharpe: {metrics['sharpe_ratio']:.3f}")
        print("="*60)

async def main():
    """Main demo function"""
    print("🌟 AstroA Demo Backtesting System")
    print("📊 This demo simulates 2 hours of trading in 10 rapid cycles")
    print("🚀 Real backtesting would run for full 2 hours with live data\n")

    runner = DemoBacktestRunner(cycles=10)

    try:
        await runner.run_demo_backtest()
        results_file = runner.save_results()

        print(f"\n✅ Demo completed successfully!")
        print(f"📊 Results saved to: {results_file}")

        return results_file

    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        return None

if __name__ == '__main__':
    print("🚀 Starting AstroA Demo Backtest...")

    try:
        results_file = asyncio.run(main())
        if results_file:
            print(f"\n🎯 Next: Generate visualization with:")
            print(f"python generate_backtest_visualization.py {results_file}")
    except KeyboardInterrupt:
        print("\n🛑 Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        sys.exit(1)