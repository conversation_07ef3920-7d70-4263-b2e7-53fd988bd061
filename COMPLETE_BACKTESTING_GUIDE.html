<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroA Complete Backtesting Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-tab {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-tab:hover, .nav-tab.active {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .content-section {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 20px;
            border-bottom: 3px solid #4299e1;
            padding-bottom: 10px;
        }

        .subsection {
            margin-bottom: 30px;
        }

        .subsection h3 {
            font-size: 1.4rem;
            color: #4a5568;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            position: relative;
        }

        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 5px;
            right: 10px;
            background: #4299e1;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .command-line {
            background: #2d3748;
            color: #68d391;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            border-left: 4px solid #38a169;
        }

        .warning {
            background: #fed7d7;
            border: 1px solid #fc8181;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .info {
            background: #bee3f8;
            border: 1px solid #63b3ed;
            color: #2c5282;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .success {
            background: #c6f6d5;
            border: 1px solid #68d391;
            color: #276749;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .quiz-container {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .quiz-question {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3748;
        }

        .quiz-options {
            list-style: none;
            padding: 0;
        }

        .quiz-options li {
            background: white;
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-options li:hover {
            background: #e2e8f0;
            border-color: #4299e1;
        }

        .quiz-options li.correct {
            background: #c6f6d5;
            border-color: #38a169;
        }

        .quiz-options li.incorrect {
            background: #fed7d7;
            border-color: #e53e3e;
        }

        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #38a169;
        }

        .btn-success:hover {
            background: #2f855a;
        }

        .btn-warning {
            background: #ed8936;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #38a169);
            transition: width 0.3s ease;
        }

        .file-tree {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin: 15px 0;
        }

        .metric-card {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            border-left: 4px solid #4299e1;
        }

        .metric-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: #4299e1;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .grid-2, .grid-3 {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-tabs {
                flex-direction: column;
                align-items: center;
            }
        }

        .interactive-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .demo-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running {
            background: #38a169;
            animation: pulse 2s infinite;
        }

        .status-stopped {
            background: #e53e3e;
        }

        .status-ready {
            background: #ed8936;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AstroA Complete Backtesting Guide</h1>
            <p>From Novice to Expert: Master Your Trading System</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('overview')">📋 Overview</button>
            <button class="nav-tab" onclick="showSection('setup')">⚙️ Setup</button>
            <button class="nav-tab" onclick="showSection('running')">🏃 Running Tests</button>
            <button class="nav-tab" onclick="showSection('analysis')">📊 Analysis</button>
            <button class="nav-tab" onclick="showSection('optimization')">🔧 Optimization</button>
            <button class="nav-tab" onclick="showSection('quiz')">🧠 Knowledge Check</button>
        </div>

        <!-- Overview Section -->
        <div id="overview" class="content-section active">
            <h2 class="section-title">📋 What is Backtesting?</h2>
            
            <div class="subsection">
                <h3>🎯 Definition & Purpose</h3>
                <p>Backtesting is the process of testing a trading strategy using historical data to evaluate its performance. Think of it as a "time machine" for your trading strategy - you can see how it would have performed in the past without risking real money.</p>
                
                <div class="info">
                    <strong>Why Backtest?</strong><br>
                    • Validate strategy effectiveness before live trading<br>
                    • Identify potential risks and drawdowns<br>
                    • Optimize parameters for better performance<br>
                    • Build confidence in your trading system
                </div>
            </div>

            <div class="subsection">
                <h3>🏗️ Your AstroA System Architecture</h3>
                <div class="file-tree">
AstroA/
├── 🎯 run_backtest.py          # Full 2-hour backtest
├── 🚀 run_demo_backtest.py     # Quick demo (10 cycles)
├── ⚡ run_simple_backtest.py   # Simplified version
├── 📊 generate_backtest_visualization.py
├── 📁 data/                    # Results storage
├── 📁 logs/                    # Execution logs
├── 📁 agents/                  # Trading agents
└── 📁 config/                  # System configuration
                </div>
            </div>

            <div class="subsection">
                <h3>🔄 Backtest Types Available</h3>
                <div class="grid-3">
                    <div class="metric-card">
                        <div class="metric-title">🎯 Full Backtest</div>
                        <div class="metric-value">2 Hours</div>
                        <p>Complete system test with real-time simulation</p>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">🚀 Demo Backtest</div>
                        <div class="metric-value">10 Cycles</div>
                        <p>Quick demonstration of capabilities</p>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">⚡ Simple Backtest</div>
                        <div class="metric-value">Customizable</div>
                        <p>Streamlined version for testing</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Setup Section -->
        <div id="setup" class="content-section">
            <h2 class="section-title">⚙️ Environment Setup</h2>

            <div class="subsection">
                <h3>🐧 Linux Environment Setup</h3>
                <p>Your AstroA system is designed for Linux. Here's how to set up your environment:</p>

                <div class="warning">
                    <strong>⚠️ Important:</strong> Always work in a virtual environment to avoid conflicts!
                </div>

                <h4>Option 1: Using Virtual Environment (Recommended)</h4>
                <div class="code-block" data-lang="bash">
# Navigate to your project directory
cd /home/<USER>/axmadcodes/AstroA

# Activate your existing virtual environment
source venv/bin/activate

# Verify you're in the virtual environment
which python
# Should show: /home/<USER>/axmadcodes/AstroA/venv/bin/python

# Install/update dependencies
pip install -r requirements.txt
                </div>

                <h4>Option 2: Global Installation (Not Recommended)</h4>
                <div class="code-block" data-lang="bash">
# Only if you must install globally
sudo pip install -r requirements.txt
                </div>

                <h4>Option 3: Create New Virtual Environment</h4>
                <div class="code-block" data-lang="bash">
# Create new virtual environment
python3 -m venv astro_backtest_env

# Activate it
source astro_backtest_env/bin/activate

# Install dependencies
pip install -r requirements.txt
                </div>
            </div>

            <div class="subsection">
                <h3>🔧 System Requirements Check</h3>
                <div class="code-block" data-lang="bash">
# Check Python version (should be 3.8+)
python --version

# Check if PostgreSQL is running (for database)
sudo systemctl status postgresql

# Check available disk space (need ~1GB for logs/data)
df -h

# Check memory (recommended 4GB+)
free -h

# Verify key dependencies
python -c "import pandas, numpy, psycopg2, asyncio; print('✅ Core dependencies OK')"
                </div>
            </div>

            <div class="subsection">
                <h3>📁 Directory Structure Verification</h3>
                <div class="code-block" data-lang="bash">
# Verify all required directories exist
ls -la /home/<USER>/axmadcodes/AstroA/

# Check for key files
ls -la run_*.py
ls -la generate_backtest_visualization.py

# Verify data and logs directories
mkdir -p data logs reports
ls -la data/ logs/ reports/
                </div>
            </div>

            <div class="subsection">
                <h3>🗄️ Database Setup</h3>
                <div class="info">
                    Your system uses PostgreSQL for storing trading data and results.
                </div>

                <div class="code-block" data-lang="bash">
# Start PostgreSQL service
sudo systemctl start postgresql

# Create database (if not exists)
sudo -u postgres createdb astroa_trading

# Run database setup scripts
psql -U postgres -d astroa_trading -f database_schema.sql
psql -U postgres -d astroa_trading -f setup_trading_tables.sql

# Test database connection
python -c "
import psycopg2
try:
    conn = psycopg2.connect('dbname=astroa_trading user=postgres')
    print('✅ Database connection successful')
    conn.close()
except Exception as e:
    print(f'❌ Database error: {e}')
"
                </div>
            </div>
        </div>

        <!-- Running Tests Section -->
        <div id="running" class="content-section">
            <h2 class="section-title">🏃 Running Backtests</h2>

            <div class="subsection">
                <h3>🚀 Quick Start - Demo Backtest</h3>
                <p>Perfect for beginners! This runs a 10-cycle simulation in under 1 minute:</p>

                <div class="success">
                    <strong>✅ Recommended for first-time users</strong><br>
                    This demo shows you exactly what to expect without waiting 2 hours.
                </div>

                <div class="code-block" data-lang="bash">
# Make sure you're in the project directory
cd /home/<USER>/axmadcodes/AstroA

# Activate virtual environment
source venv/bin/activate

# Run demo backtest
python run_demo_backtest.py

# Expected output:
# 🚀 Starting AstroA Demo Backtest
# 📈 Initial Portfolio: $100,000
# 📊 Running demo cycle 1/10
# 💰 Portfolio: $100,234.56 (+0.23%)
# ... (continues for 10 cycles)
# ✅ Demo completed successfully!
                </div>

                <div class="interactive-demo">
                    <h4>🎮 Interactive Demo Status</h4>
                    <div class="demo-controls">
                        <button class="btn" onclick="simulateDemo()">▶️ Start Demo</button>
                        <button class="btn btn-warning" onclick="stopDemo()">⏸️ Pause</button>
                        <button class="btn btn-success" onclick="resetDemo()">🔄 Reset</button>
                    </div>
                    <div id="demo-status">
                        <span class="status-indicator status-ready"></span>Ready to start demo
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="demo-progress" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="subsection">
                <h3>⚡ Simple Backtest (Customizable Duration)</h3>
                <p>More control over the testing process:</p>

                <div class="code-block" data-lang="bash">
# Run simple backtest (default 2 hours)
python run_simple_backtest.py

# Monitor progress in real-time
tail -f logs/backtest_*.log

# Stop early if needed (Ctrl+C)
# The system will save partial results
                </div>

                <div class="info">
                    <strong>💡 Pro Tip:</strong> The simple backtest runs in 1-minute cycles. You can stop it anytime with Ctrl+C and still get results!
                </div>
            </div>

            <div class="subsection">
                <h3>🎯 Full System Backtest (2 Hours)</h3>
                <p>Complete system test with all agents and real-time simulation:</p>

                <div class="warning">
                    <strong>⚠️ Time Commitment:</strong> This runs for exactly 2 hours. Make sure you have time!
                </div>

                <div class="code-block" data-lang="bash">
# Run full backtest
python run_backtest.py

# Expected runtime: Exactly 2 hours
# Expected output files:
# - data/backtest_results_YYYYMMDD_HHMMSS.json
# - logs/backtest_YYYYMMDD_HHMMSS.log

# Monitor in another terminal:
watch -n 30 'tail -10 logs/backtest_*.log'
                </div>
            </div>

            <div class="subsection">
                <h3>📊 Generate Visualization</h3>
                <p>After any backtest, create beautiful HTML reports:</p>

                <div class="code-block" data-lang="bash">
# Find your results file
ls -la data/backtest_results_*.json
ls -la data/demo_backtest_results_*.json

# Generate visualization (replace with your actual file)
python generate_backtest_visualization.py data/backtest_results_20250928_120000.json

# Output:
# ✅ Visualization generated: data/backtest_visualization_20250928_120500.html
# 🌐 Open the file in your browser to view the results

# Open in browser
firefox data/backtest_visualization_*.html
# or
google-chrome data/backtest_visualization_*.html
                </div>
            </div>
        </div>

        <!-- Analysis Section -->
        <div id="analysis" class="content-section">
            <h2 class="section-title">📊 Understanding Your Results</h2>

            <div class="subsection">
                <h3>📈 Key Performance Metrics</h3>
                <p>Here's what each metric means and what to look for:</p>

                <div class="grid-2">
                    <div class="metric-card">
                        <div class="metric-title">💰 Total Return</div>
                        <div class="metric-value">+2.34%</div>
                        <p><strong>What it means:</strong> Overall profit/loss percentage</p>
                        <p><strong>Good range:</strong> +1% to +5% for 2-hour test</p>
                        <p><strong>Red flags:</strong> Losses > -2% or gains > +10%</p>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">📉 Max Drawdown</div>
                        <div class="metric-value">-1.23%</div>
                        <p><strong>What it means:</strong> Largest peak-to-trough decline</p>
                        <p><strong>Good range:</strong> < 3% for conservative strategies</p>
                        <p><strong>Red flags:</strong> > 5% indicates high risk</p>
                    </div>
                </div>

                <div class="grid-2">
                    <div class="metric-card">
                        <div class="metric-title">📊 Sharpe Ratio</div>
                        <div class="metric-value">1.45</div>
                        <p><strong>What it means:</strong> Risk-adjusted returns</p>
                        <p><strong>Good range:</strong> > 1.0 is good, > 2.0 is excellent</p>
                        <p><strong>Red flags:</strong> < 0.5 indicates poor risk management</p>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">🔄 Total Trades</div>
                        <div class="metric-value">23</div>
                        <p><strong>What it means:</strong> Number of buy/sell actions</p>
                        <p><strong>Good range:</strong> 10-50 for 2-hour test</p>
                        <p><strong>Red flags:</strong> < 5 (too passive) or > 100 (overtrading)</p>
                    </div>
                </div>
            </div>

            <div class="subsection">
                <h3>🎯 What Makes a Good Backtest?</h3>

                <div class="success">
                    <strong>✅ Excellent Results:</strong><br>
                    • Total Return: +2% to +4%<br>
                    • Max Drawdown: < 2%<br>
                    • Sharpe Ratio: > 1.5<br>
                    • Win Rate: > 60%<br>
                    • Consistent performance across cycles
                </div>

                <div class="info">
                    <strong>📊 Good Results:</strong><br>
                    • Total Return: +0.5% to +2%<br>
                    • Max Drawdown: < 3%<br>
                    • Sharpe Ratio: > 1.0<br>
                    • Win Rate: > 50%<br>
                    • Stable portfolio value
                </div>

                <div class="warning">
                    <strong>⚠️ Concerning Results:</strong><br>
                    • Total Return: < 0% or > +8%<br>
                    • Max Drawdown: > 5%<br>
                    • Sharpe Ratio: < 0.5<br>
                    • Win Rate: < 40%<br>
                    • High volatility or many errors
                </div>
            </div>

            <div class="subsection">
                <h3>📋 Results File Analysis</h3>
                <p>Your backtest generates detailed JSON files. Here's how to analyze them:</p>

                <div class="code-block" data-lang="bash">
# View results summary
python -c "
import json
with open('data/backtest_results_YYYYMMDD_HHMMSS.json', 'r') as f:
    data = json.load(f)
    metrics = data['performance_metrics']
    print(f'Return: {metrics[\"total_return_percent\"]:.2f}%')
    print(f'Drawdown: {metrics[\"max_drawdown_percent\"]:.2f}%')
    print(f'Trades: {metrics[\"total_trades\"]}')
    print(f'Sharpe: {metrics[\"sharpe_ratio\"]:.3f}')
"

# Count successful vs failed cycles
grep -c "✅" logs/backtest_*.log
grep -c "❌" logs/backtest_*.log

# Check for any system errors
grep "ERROR" logs/backtest_*.log
                </div>
            </div>

            <div class="subsection">
                <h3>🔍 Advanced Analysis Commands</h3>

                <div class="code-block" data-lang="bash">
# Analyze trading patterns
python -c "
import json, pandas as pd
with open('data/backtest_results_YYYYMMDD_HHMMSS.json', 'r') as f:
    data = json.load(f)

# Convert trades to DataFrame for analysis
trades_df = pd.DataFrame(data['trades_executed'])
if not trades_df.empty:
    print('Trading Summary:')
    print(trades_df['action'].value_counts())
    print('\\nMost traded symbols:')
    print(trades_df['symbol'].value_counts().head())
    print('\\nAverage confidence:')
    print(f'{trades_df[\"confidence\"].mean():.1%}')
"

# Portfolio evolution analysis
python -c "
import json, matplotlib.pyplot as plt
with open('data/backtest_results_YYYYMMDD_HHMMSS.json', 'r') as f:
    data = json.load(f)

snapshots = data['portfolio_snapshots']
values = [s['total_value'] for s in snapshots]
times = range(len(values))

plt.figure(figsize=(10, 6))
plt.plot(times, values, 'b-', linewidth=2)
plt.title('Portfolio Value Over Time')
plt.xlabel('Snapshot Number')
plt.ylabel('Portfolio Value ($)')
plt.grid(True, alpha=0.3)
plt.savefig('portfolio_evolution.png', dpi=300, bbox_inches='tight')
print('📊 Portfolio chart saved as portfolio_evolution.png')
"
                </div>
            </div>
        </div>

        <!-- Optimization Section -->
        <div id="optimization" class="content-section">
            <h2 class="section-title">🔧 Optimization & Tuning</h2>

            <div class="subsection">
                <h3>⚙️ When to Optimize</h3>
                <p>Only optimize after you understand your baseline performance:</p>

                <div class="info">
                    <strong>🎯 Optimization Triggers:</strong><br>
                    • Max drawdown > 3%<br>
                    • Sharpe ratio < 1.0<br>
                    • Win rate < 50%<br>
                    • Excessive trading (> 100 trades in 2 hours)<br>
                    • Inconsistent performance across runs
                </div>
            </div>

            <div class="subsection">
                <h3>🔧 Key Parameters to Tune</h3>

                <div class="grid-2">
                    <div class="metric-card">
                        <div class="metric-title">📊 Risk Management</div>
                        <p><strong>Position Size:</strong> Max 20% per trade</p>
                        <p><strong>Stop Loss:</strong> 2-5% per position</p>
                        <p><strong>Max Positions:</strong> 5-10 concurrent</p>
                        <p><strong>Cash Reserve:</strong> Keep 20-30% cash</p>
                    </div>
                    <div class="metric-card">
                        <div class="metric-title">⏱️ Timing Parameters</div>
                        <p><strong>Analysis Frequency:</strong> 1-5 minutes</p>
                        <p><strong>Signal Threshold:</strong> 0.6-0.8 confidence</p>
                        <p><strong>Hold Time:</strong> 15-60 minutes</p>
                        <p><strong>Rebalance Frequency:</strong> Every 30 minutes</p>
                    </div>
                </div>
            </div>

            <div class="subsection">
                <h3>🧪 A/B Testing Your Strategy</h3>
                <p>Run multiple backtests with different parameters:</p>

                <div class="code-block" data-lang="bash">
# Test different confidence thresholds
for confidence in 0.6 0.7 0.8 0.9; do
    echo "Testing confidence threshold: $confidence"
    # Modify config and run backtest
    python run_simple_backtest.py --confidence-threshold $confidence
    # Save results with descriptive name
    mv data/backtest_results_*.json data/backtest_confidence_${confidence}.json
done

# Compare results
python -c "
import json, glob
results = []
for file in glob.glob('data/backtest_confidence_*.json'):
    with open(file, 'r') as f:
        data = json.load(f)
        confidence = file.split('_')[-1].replace('.json', '')
        metrics = data['performance_metrics']
        results.append({
            'confidence': confidence,
            'return': metrics['total_return_percent'],
            'drawdown': metrics['max_drawdown_percent'],
            'sharpe': metrics['sharpe_ratio']
        })

print('Confidence Threshold Comparison:')
for r in sorted(results, key=lambda x: x['sharpe'], reverse=True):
    print(f'Confidence {r[\"confidence\"]}: Return {r[\"return\"]:.2f}%, Drawdown {r[\"drawdown\"]:.2f}%, Sharpe {r[\"sharpe\"]:.3f}')
"
                </div>
            </div>

            <div class="subsection">
                <h3>📈 Performance Improvement Tips</h3>

                <div class="success">
                    <strong>✅ Proven Optimization Strategies:</strong><br>
                    1. <strong>Reduce Position Sizes:</strong> If drawdown > 3%, cut position sizes by 25%<br>
                    2. <strong>Increase Signal Quality:</strong> Raise confidence threshold from 0.6 to 0.75<br>
                    3. <strong>Add Stop Losses:</strong> Implement 2% stop loss on all positions<br>
                    4. <strong>Diversify Timeframes:</strong> Use 1h, 4h, and 1d analysis together<br>
                    5. <strong>Filter Market Conditions:</strong> Avoid trading in high volatility periods
                </div>
            </div>

            <div class="subsection">
                <h3>🚨 Backtesting Limitations</h3>

                <div class="warning">
                    <strong>⚠️ Important Limitations:</strong><br>
                    • <strong>No Slippage:</strong> Real trades have execution delays<br>
                    • <strong>Perfect Information:</strong> Real markets have data delays<br>
                    • <strong>No Market Impact:</strong> Large orders affect prices<br>
                    • <strong>Survivorship Bias:</strong> Only tests on available data<br>
                    • <strong>Overfitting Risk:</strong> Don't over-optimize on limited data
                </div>

                <div class="info">
                    <strong>💡 Best Practices:</strong><br>
                    • Run multiple backtests with different time periods<br>
                    • Test on both bull and bear market conditions<br>
                    • Keep some data for out-of-sample testing<br>
                    • Start with paper trading before live deployment<br>
                    • Monitor live performance vs backtest results
                </div>
            </div>
        </div>

        <!-- Quiz Section -->
        <div id="quiz" class="content-section">
            <h2 class="section-title">🧠 Knowledge Check Quiz</h2>

            <div class="subsection">
                <h3>📝 Test Your Understanding</h3>
                <p>Complete this quiz to verify your backtesting knowledge:</p>

                <div class="quiz-container">
                    <div class="quiz-question">
                        1. What is the recommended first backtest to run for beginners?
                    </div>
                    <ul class="quiz-options" data-question="1">
                        <li onclick="selectAnswer(this, false)">Full 2-hour backtest</li>
                        <li onclick="selectAnswer(this, true)">Demo backtest (10 cycles)</li>
                        <li onclick="selectAnswer(this, false)">Simple backtest with custom duration</li>
                        <li onclick="selectAnswer(this, false)">Live trading immediately</li>
                    </ul>
                </div>

                <div class="quiz-container">
                    <div class="quiz-question">
                        2. What does a Sharpe ratio of 1.5 indicate?
                    </div>
                    <ul class="quiz-options" data-question="2">
                        <li onclick="selectAnswer(this, false)">Poor risk-adjusted returns</li>
                        <li onclick="selectAnswer(this, true)">Good risk-adjusted returns</li>
                        <li onclick="selectAnswer(this, false)">Excessive risk taking</li>
                        <li onclick="selectAnswer(this, false)">No correlation to risk</li>
                    </ul>
                </div>

                <div class="quiz-container">
                    <div class="quiz-question">
                        3. Which environment setup is recommended?
                    </div>
                    <ul class="quiz-options" data-question="3">
                        <li onclick="selectAnswer(this, false)">Global Python installation</li>
                        <li onclick="selectAnswer(this, true)">Virtual environment (venv)</li>
                        <li onclick="selectAnswer(this, false)">System-wide package installation</li>
                        <li onclick="selectAnswer(this, false)">Docker container only</li>
                    </ul>
                </div>

                <div class="quiz-container">
                    <div class="quiz-question">
                        4. What is a concerning max drawdown for a 2-hour backtest?
                    </div>
                    <ul class="quiz-options" data-question="4">
                        <li onclick="selectAnswer(this, false)">1.5%</li>
                        <li onclick="selectAnswer(this, false)">2.8%</li>
                        <li onclick="selectAnswer(this, true)">6.2%</li>
                        <li onclick="selectAnswer(this, false)">0.5%</li>
                    </ul>
                </div>

                <div class="quiz-container">
                    <div class="quiz-question">
                        5. How do you generate a visualization after backtesting?
                    </div>
                    <ul class="quiz-options" data-question="5">
                        <li onclick="selectAnswer(this, false)">It's generated automatically</li>
                        <li onclick="selectAnswer(this, true)">Run generate_backtest_visualization.py with results file</li>
                        <li onclick="selectAnswer(this, false)">Use Excel to create charts</li>
                        <li onclick="selectAnswer(this, false)">View the JSON file directly</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn btn-success" onclick="checkQuizResults()">📊 Check My Score</button>
                    <button class="btn" onclick="resetQuiz()">🔄 Reset Quiz</button>
                </div>

                <div id="quiz-results" style="display: none; margin-top: 20px;"></div>
            </div>

            <div class="subsection">
                <h3>🎓 Next Steps Based on Your Score</h3>

                <div id="score-recommendations" style="display: none;">
                    <div class="success" id="excellent-score" style="display: none;">
                        <strong>🌟 Excellent (80-100%)!</strong><br>
                        You're ready to run full backtests and optimize your strategy. Consider:
                        <ul>
                            <li>Running multiple 2-hour backtests with different parameters</li>
                            <li>Implementing A/B testing for strategy optimization</li>
                            <li>Moving to paper trading with real market data</li>
                        </ul>
                    </div>

                    <div class="info" id="good-score" style="display: none;">
                        <strong>📊 Good (60-79%)!</strong><br>
                        You understand the basics. Recommended next steps:
                        <ul>
                            <li>Run a few demo backtests to gain experience</li>
                            <li>Study the analysis section more carefully</li>
                            <li>Practice interpreting backtest results</li>
                        </ul>
                    </div>

                    <div class="warning" id="needs-review" style="display: none;">
                        <strong>📚 Needs Review (< 60%)</strong><br>
                        Please review the guide sections you missed:
                        <ul>
                            <li>Re-read the Overview and Setup sections</li>
                            <li>Practice with demo backtests only</li>
                            <li>Focus on understanding key metrics</li>
                            <li>Retake the quiz after studying</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Demo simulation functionality
        let demoInterval;
        let demoProgress = 0;
        let demoRunning = false;

        function simulateDemo() {
            if (demoRunning) return;

            demoRunning = true;
            demoProgress = 0;

            const statusElement = document.getElementById('demo-status');
            const progressElement = document.getElementById('demo-progress');

            statusElement.innerHTML = '<span class="status-indicator status-running"></span>Running demo backtest...';

            demoInterval = setInterval(() => {
                demoProgress += 10;
                progressElement.style.width = demoProgress + '%';

                if (demoProgress >= 100) {
                    clearInterval(demoInterval);
                    statusElement.innerHTML = '<span class="status-indicator status-ready"></span>Demo completed! Check results in data/ directory';
                    demoRunning = false;
                }
            }, 500);
        }

        function stopDemo() {
            if (demoInterval) {
                clearInterval(demoInterval);
                demoRunning = false;
                document.getElementById('demo-status').innerHTML = '<span class="status-indicator status-stopped"></span>Demo paused';
            }
        }

        function resetDemo() {
            if (demoInterval) {
                clearInterval(demoInterval);
            }
            demoRunning = false;
            demoProgress = 0;
            document.getElementById('demo-progress').style.width = '0%';
            document.getElementById('demo-status').innerHTML = '<span class="status-indicator status-ready"></span>Ready to start demo';
        }

        // Quiz functionality
        let quizAnswers = {};

        function selectAnswer(element, isCorrect) {
            const questionNum = element.parentElement.getAttribute('data-question');
            const options = element.parentElement.querySelectorAll('li');

            // Reset all options in this question
            options.forEach(option => {
                option.classList.remove('correct', 'incorrect');
            });

            // Mark selected answer
            if (isCorrect) {
                element.classList.add('correct');
            } else {
                element.classList.add('incorrect');
            }

            // Store answer
            quizAnswers[questionNum] = isCorrect;
        }

        function checkQuizResults() {
            const totalQuestions = 5;
            const correctAnswers = Object.values(quizAnswers).filter(answer => answer === true).length;
            const score = Math.round((correctAnswers / totalQuestions) * 100);

            const resultsElement = document.getElementById('quiz-results');
            const recommendationsElement = document.getElementById('score-recommendations');

            resultsElement.innerHTML = `
                <div class="metric-card">
                    <div class="metric-title">Your Score</div>
                    <div class="metric-value">${score}%</div>
                    <p>You got ${correctAnswers} out of ${totalQuestions} questions correct.</p>
                </div>
            `;
            resultsElement.style.display = 'block';
            recommendationsElement.style.display = 'block';

            // Hide all score recommendations
            document.getElementById('excellent-score').style.display = 'none';
            document.getElementById('good-score').style.display = 'none';
            document.getElementById('needs-review').style.display = 'none';

            // Show appropriate recommendation
            if (score >= 80) {
                document.getElementById('excellent-score').style.display = 'block';
            } else if (score >= 60) {
                document.getElementById('good-score').style.display = 'block';
            } else {
                document.getElementById('needs-review').style.display = 'block';
            }
        }

        function resetQuiz() {
            quizAnswers = {};

            // Reset all quiz options
            const options = document.querySelectorAll('.quiz-options li');
            options.forEach(option => {
                option.classList.remove('correct', 'incorrect');
            });

            // Hide results
            document.getElementById('quiz-results').style.display = 'none';
            document.getElementById('score-recommendations').style.display = 'none';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show overview section by default
            showSection('overview');

            // Add some interactive elements
            console.log('🚀 AstroA Backtesting Guide Loaded');
            console.log('📊 Ready to help you master backtesting!');
        });
    </script>
</body>
</html>
