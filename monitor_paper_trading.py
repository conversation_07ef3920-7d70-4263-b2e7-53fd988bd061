#!/usr/bin/env python3
"""
Real-Time Paper Trading Monitor
Shows live updates of your paper trading session
"""

import time
import sys
import os
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import alpaca_trade_api as tradeapi
    from config.paper_trading_config import paper_config
    import psycopg2
    from config.settings import Config
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

class PaperTradingMonitor:
    def __init__(self):
        # Initialize Alpaca API
        self.api = tradeapi.REST(
            paper_config.api_key,
            paper_config.secret_key,
            paper_config.base_url,
            api_version='v2'
        )
        
        # Database connection
        self.db_conn = None
        try:
            self.db_conn = psycopg2.connect(Config.DATABASE_URL)
        except Exception as e:
            print(f"⚠️  Database connection failed: {e}")

    def clear_screen(self):
        """Clear terminal screen"""
        os.system('clear' if os.name == 'posix' else 'cls')

    def get_account_info(self):
        """Get current account information"""
        try:
            account = self.api.get_account()
            return {
                'status': account.status,
                'portfolio_value': float(account.portfolio_value),
                'cash': float(account.cash),
                'buying_power': float(account.buying_power),
                'day_trade_count': account.day_trade_count
            }
        except Exception as e:
            return {'error': str(e)}

    def get_positions(self):
        """Get current positions"""
        try:
            positions = self.api.list_positions()
            return [{
                'symbol': pos.symbol,
                'qty': float(pos.qty),
                'market_value': float(pos.market_value),
                'cost_basis': float(pos.cost_basis),
                'unrealized_pl': float(pos.unrealized_pl),
                'unrealized_plpc': float(pos.unrealized_plpc) * 100,
                'side': pos.side
            } for pos in positions]
        except Exception as e:
            return []

    def get_recent_orders(self, limit=5):
        """Get recent orders"""
        try:
            orders = self.api.list_orders(status='all', limit=limit)
            return [{
                'symbol': order.symbol,
                'side': order.side,
                'qty': float(order.qty),
                'order_type': order.order_type,
                'status': order.status,
                'submitted_at': order.submitted_at,
                'filled_qty': float(order.filled_qty) if order.filled_qty else 0
            } for order in orders]
        except Exception as e:
            return []

    def get_market_data_stats(self):
        """Get market data statistics from database"""
        if not self.db_conn:
            return {}
        
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT symbol) as unique_symbols,
                    MAX(timestamp) as latest_data
                FROM market_data
            """)
            result = cursor.fetchone()
            cursor.close()
            
            return {
                'total_records': result[0],
                'unique_symbols': result[1],
                'latest_data': result[2]
            }
        except Exception as e:
            return {'error': str(e)}

    def display_dashboard(self):
        """Display the monitoring dashboard"""
        self.clear_screen()
        
        print("🌟 AstroA Paper Trading - Live Monitor")
        print("=" * 60)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        # Account Information
        account = self.get_account_info()
        if 'error' not in account:
            print(f"📊 Account Status: {account['status']}")
            print(f"💰 Portfolio Value: ${account['portfolio_value']:,.2f}")
            print(f"💵 Available Cash: ${account['cash']:,.2f}")
            print(f"🔋 Buying Power: ${account['buying_power']:,.2f}")
            
            # Calculate P&L
            initial_value = 100000.0  # Initial paper trading amount
            total_pnl = account['portfolio_value'] - initial_value
            pnl_pct = (total_pnl / initial_value) * 100
            
            pnl_color = "🟢" if total_pnl >= 0 else "🔴"
            print(f"{pnl_color} Total P&L: ${total_pnl:,.2f} ({pnl_pct:+.2f}%)")
        else:
            print(f"❌ Account Error: {account['error']}")

        print("\n" + "─" * 60)

        # Positions
        positions = self.get_positions()
        if positions:
            print("📈 Current Positions:")
            for pos in positions:
                pnl_color = "🟢" if pos['unrealized_pl'] >= 0 else "🔴"
                print(f"  {pos['symbol']}: {pos['qty']:+.4f} shares")
                print(f"    Market Value: ${pos['market_value']:,.2f}")
                print(f"    {pnl_color} P&L: ${pos['unrealized_pl']:+,.2f} ({pos['unrealized_plpc']:+.2f}%)")
        else:
            print("📈 Current Positions: None")

        print("\n" + "─" * 60)

        # Recent Orders
        orders = self.get_recent_orders()
        if orders:
            print("📝 Recent Orders:")
            for order in orders[:3]:  # Show last 3 orders
                status_icon = "✅" if order['status'] == 'filled' else "⏳" if order['status'] == 'pending_new' else "❌"
                print(f"  {status_icon} {order['side'].upper()} {order['qty']:.4f} {order['symbol']}")
                print(f"    Status: {order['status']} | Type: {order['order_type']}")
                if order['filled_qty'] > 0:
                    print(f"    Filled: {order['filled_qty']:.4f}")
        else:
            print("📝 Recent Orders: None")

        print("\n" + "─" * 60)

        # Market Data Stats
        market_stats = self.get_market_data_stats()
        if 'error' not in market_stats:
            print("📊 Market Data Status:")
            print(f"  Total Records: {market_stats['total_records']:,}")
            print(f"  Unique Symbols: {market_stats['unique_symbols']}")
            print(f"  Latest Data: {market_stats['latest_data']}")
        else:
            print(f"📊 Market Data: {market_stats.get('error', 'No data')}")

        print("\n" + "─" * 60)
        print("🔄 Refreshing every 10 seconds... (Ctrl+C to exit)")

    def run(self):
        """Run the monitoring dashboard"""
        try:
            while True:
                self.display_dashboard()
                time.sleep(10)  # Refresh every 10 seconds
        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped")
        finally:
            if self.db_conn:
                self.db_conn.close()

if __name__ == "__main__":
    monitor = PaperTradingMonitor()
    monitor.run()
