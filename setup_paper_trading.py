#!/usr/bin/env python3
"""
AstroA Paper Trading Setup Script
Sets up paper trading environment and validates configuration
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'alpaca-trade-api',
        'pandas',
        'psycopg2-binary',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """Install missing dependencies"""
    print(f"📦 Installing missing packages: {', '.join(packages)}")
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def setup_environment():
    """Setup environment variables"""
    env_file = Path('.env')
    
    if not env_file.exists():
        print("📝 Creating .env file...")
        with open(env_file, 'w') as f:
            f.write("""# AstroA Paper Trading Configuration
# Get your free Alpaca paper trading keys at: https://alpaca.markets/

# Alpaca Paper Trading API
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Paper Trading Settings
PAPER_TRADING_ENABLED=true
PAPER_TRADING_INITIAL_CASH=100000
PAPER_TRADING_MAX_POSITIONS=10
PAPER_TRADING_RISK_LIMIT=0.02

# Data Feed Settings
REAL_TIME_DATA_ENABLED=true
DATA_FEED_PROVIDER=database
MARKET_DATA_DELAY=0

# Database Configuration (if not already set)
DATABASE_URL=postgresql://trading_user:secure_password_123@localhost:5432/mathematical_trading

# DeepSeek AI Configuration (if not already set)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
""")
        print("✅ Created .env file")
        print("⚠️  Please edit .env file with your actual API keys")
    else:
        print("✅ .env file already exists")

def create_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        'data',
        'agents/paper_trading'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def test_configuration():
    """Test paper trading configuration"""
    try:
        from config.paper_trading_config import paper_config
        
        print("🧪 Testing configuration...")
        
        # Test basic validation
        paper_config.validate()
        print("✅ Configuration validation passed")
        
        # Test database connection
        from config.settings import Config
        import psycopg2
        
        try:
            conn = psycopg2.connect(Config.DATABASE_URL)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            conn.close()
            print("✅ Database connection successful")
        except Exception as e:
            print(f"⚠️  Database connection failed: {e}")
            print("   Make sure PostgreSQL is running and database is set up")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🌟 AstroA Paper Trading Setup")
    print("=" * 40)
    
    # Check dependencies
    print("1. Checking dependencies...")
    missing = check_dependencies()
    
    if missing:
        print(f"📦 Missing packages: {', '.join(missing)}")
        if input("Install missing packages? (y/n): ").lower() == 'y':
            if not install_dependencies(missing):
                print("❌ Failed to install dependencies")
                return False
        else:
            print("❌ Cannot proceed without required packages")
            return False
    else:
        print("✅ All dependencies are installed")
    
    # Setup environment
    print("\n2. Setting up environment...")
    setup_environment()
    
    # Create directories
    print("\n3. Creating directories...")
    create_directories()
    
    # Test configuration
    print("\n4. Testing configuration...")
    if test_configuration():
        print("\n🎉 Paper trading setup complete!")
        print("\n📋 Next steps:")
        print("1. Edit .env file with your Alpaca API keys")
        print("2. Ensure database is running and populated with market data")
        print("3. Run: python run_paper_trading.py --test-config")
        print("4. Start paper trading: python run_paper_trading.py")
        return True
    else:
        print("\n❌ Setup completed with errors")
        print("Please fix configuration issues before running paper trading")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
