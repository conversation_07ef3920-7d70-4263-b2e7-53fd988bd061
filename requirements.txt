# Core async libraries for DeepSeek API
aiodns==3.5.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.15
aiosignal==1.4.0
alembic==1.16.5
alpha_vantage==3.0.0
amqp==5.3.1
annotated-types==0.7.0
anthropic==0.68.1
anyio==4.11.0
argon2-cffi==25.1.0
argon2-cffi-bindings==25.1.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.5
attrs==25.3.0
autogen-agentchat==0.7.4
autogen-core==0.7.4
babel==2.17.0
backoff==2.2.1
bcrypt==5.0.0
beautifulsoup4==4.13.5
billiard==4.2.2
black==25.9.0
bleach==6.2.0
blis==1.3.0
build==1.3.0
cachetools==5.5.2
catalogue==2.0.10
ccxt==4.5.6
celery==5.5.3
certifi==2025.8.3
cffi==2.0.0
cfgv==3.4.0
charset-normalizer==3.4.3
chromadb==1.1.0
clarabel==0.11.1
click==8.3.0
click-didyoumean==0.3.1
click-plugins==1.1.1.2
click-repl==0.3.0
cloudpathlib==0.22.0
cmdstanpy==1.2.5
coloredlogs==15.0.1
comm==0.2.3
confection==0.1.5
contourpy==1.3.3
cryptography==46.0.1
curl_cffi==0.13.0
cvxpy==1.7.3
cycler==0.12.1
cymem==2.0.11
Cython==3.1.4
debugpy==1.8.17
decorator==5.2.1
defusedxml==0.7.1
distlib==0.4.0
distro==1.9.0
docstring_parser==0.17.0
durationpy==0.10
ecos==2.0.14
en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl#sha256=1932429db727d4bff3deed6b34cfc05df17794f4a52eeb26cf8928f7c1a0fb85
executing==2.2.1
fastapi==0.117.1
fastjsonschema==2.21.2
filelock==3.19.1
flake8==7.3.0
flatbuffers==25.9.23
fonttools==4.60.0
fqdn==1.5.1
frozendict==2.4.6
frozenlist==1.7.0
fsspec==2025.9.0
google-auth==2.40.3
googleapis-common-protos==1.70.0
greenlet==3.2.4
grpcio==1.75.1
h11==0.16.0
hf-xet==1.1.10
holidays==0.81
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.35.1
humanfriendly==10.0
identify==2.6.14
idna==3.10
importlib_metadata==8.7.0
importlib_resources==6.5.2
inflection==0.5.1
iniconfig==2.1.0
ipykernel==6.30.1
ipython==9.5.0
ipython_pygments_lexers==1.1.1
ipywidgets==8.1.7
isoduration==20.11.0
isort==6.0.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.11.0
joblib==1.5.2
json5==0.12.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.25.1
jsonschema-specifications==2025.9.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.12.0
jupyter-lsp==2.3.0
jupyter_client==8.6.3
jupyter_core==5.8.1
jupyter_server==2.17.0
jupyter_server_terminals==0.5.3
jupyterlab==4.4.9
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.15
kiwisolver==1.4.9
kombu==5.5.4
kubernetes==33.1.0
langchain==0.3.27
langchain-core==0.3.76
langchain-text-splitters==0.3.11
langcodes==3.5.0
langsmith==0.4.31
language_data==1.3.0
lark==1.3.0
lightgbm==4.6.0
lxml==6.0.2
Mako==1.3.10
marisa-trie==1.3.1
markdown-it-py==4.0.0
MarkupSafe==3.0.2
matplotlib==3.10.6
matplotlib-inline==0.1.7
mccabe==0.7.0
mdurl==0.1.2
mistune==3.1.4
mmh3==5.2.0
more-itertools==10.8.0
mpmath==1.3.0
multidict==6.6.4
multitasking==0.0.12
murmurhash==1.0.13
mypy==1.18.2
mypy_extensions==1.1.0
narwhals==2.5.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.5
newsapi-python==0.2.7
nltk==3.9.1
nodeenv==1.9.1
notebook==7.4.6
notebook_shim==0.2.4
numpy==2.3.3
nvidia-cublas-cu12==12.8.4.1
nvidia-cuda-cupti-cu12==12.8.90
nvidia-cuda-nvrtc-cu12==12.8.93
nvidia-cuda-runtime-cu12==12.8.90
nvidia-cudnn-cu12==9.10.2.21
nvidia-cufft-cu12==11.3.3.83
nvidia-cufile-cu12==1.13.1.3
nvidia-curand-cu12==10.3.9.90
nvidia-cusolver-cu12==11.7.3.90
nvidia-cusparse-cu12==12.5.8.93
nvidia-cusparselt-cu12==0.7.1
nvidia-nccl-cu12==2.27.3
nvidia-nvjitlink-cu12==12.8.93
nvidia-nvtx-cu12==12.8.90
oauthlib==3.3.1
onnxruntime==1.23.0
openai==1.109.1
opentelemetry-api==1.37.0
opentelemetry-exporter-otlp-proto-common==1.37.0
opentelemetry-exporter-otlp-proto-grpc==1.37.0
opentelemetry-proto==1.37.0
opentelemetry-sdk==1.37.0
opentelemetry-semantic-conventions==0.58b0
orjson==3.11.3
osqp==1.0.4
outcome==1.3.0.post0
overrides==7.7.0
packaging==25.0
pandas==2.3.2
pandocfilters==1.5.1
parso==0.8.5
pathspec==0.12.1
patsy==1.0.1
peewee==3.18.2
pexpect==4.9.0
pillow==11.3.0
platformdirs==4.4.0
plotly==5.24.1
pluggy==1.6.0
posthog==5.4.0
praw==7.8.1
prawcore==2.4.0
pre_commit==4.3.0
preshed==3.0.10
prometheus_client==0.23.1
prompt_toolkit==3.0.52
propcache==0.3.2
prophet==1.1.7
protobuf==5.29.5
psutil==7.1.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.2
pycares==4.11.0
pycodestyle==2.14.0
pycparser==2.23
pydantic==2.11.9
pydantic_core==2.33.2
pyflakes==3.4.0
Pygments==2.19.2
pyparsing==3.2.5
PyPika==0.48.9
pyportfolioopt==1.5.6
pyproject_hooks==1.2.0
PySocks==1.7.1
pytest==8.4.2
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-json-logger==3.3.0
pytokens==0.1.10
pytz==2025.2
PyYAML==6.0.3
pyzmq==27.1.0
Quandl==3.7.0
redis==6.4.0
referencing==0.36.2
regex==2025.9.18
requests==2.32.5
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rfc3987-syntax==1.1.0
rich==14.1.0
rpds-py==0.27.1
rsa==4.9.1
safetensors==0.6.2
scikit-learn==1.7.2
scipy==1.16.2
scs==3.2.8
seaborn==0.13.2
selenium==4.35.0
Send2Trash==1.8.3
sentence-transformers==5.1.1
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
smart_open==7.3.1
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.8
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.43
srsly==2.5.1
stack-data==0.6.3
stanio==0.5.1
starlette==0.48.0
statsmodels==0.14.5
sympy==1.14.0
TA-Lib==0.6.7
tenacity==9.1.2
terminado==0.18.1
textblob==0.19.0
thinc==8.3.6
threadpoolctl==3.6.0
tinycss2==1.4.0
tokenizers==0.22.1
torch==2.8.0
tornado==6.5.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.56.2
trio==0.30.0
trio-websocket==0.12.2
triton==3.4.0
tweepy==4.16.0
typer==0.19.2
types-python-dateutil==2.9.0.20250822
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
update-checker==0.18.0
uri-template==1.3.0
urllib3==2.5.0
uvicorn==0.37.0
uvloop==0.21.0
vine==5.1.0
virtualenv==20.34.0
wasabi==1.1.3
watchfiles==1.1.0
wcwidth==0.2.14
weasel==0.4.1
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
widgetsnbextension==4.0.14
wrapt==1.17.3
wsproto==1.2.0
xgboost==3.0.5
yarl==1.20.1
yfinance==0.2.66
zipp==3.23.0
zstandard==0.25.0
