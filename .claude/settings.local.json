{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(chmod:*)", "Bash(tree:*)", "Bash(npm create:*)", "Bash(npx create-vite:*)", "Bash(npm install)", "Bash(npm install:*)", "Bash(npx tailwindcss init:*)", "Bash(./node_modules/.bin/tailwindcss:*)", "<PERSON><PERSON>(curl:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(python test_installation:*)", "Bash(sudo systemctl status:*)", "Bash(systemctl:*)", "Bash(systemctl status:*)", "<PERSON><PERSON>(sudo:*)", "Bash(PGPASSWORD='' psql:*)", "Read(//home/<USER>/**)", "Read(//home/<USER>/axmadcodes/**)", "Bash(find:*)", "Read(//tmp/**)", "Bash(lsof:*)", "Bash(./COMPLETE_RECOVERY_SCRIPT.sh)", "Bash(python test_installation.py:*)", "<PERSON><PERSON>(python test:*)", "Bash(PGPASSWORD='secure_password_123' psql -h localhost -U trading_user -d mathematical_trading -c \"\\dt\")", "Bash(PGPASSWORD='hejhej' psql:*)", "Bash(PGPASSWORD='hejhej' psql -h localhost -U trading_user -d postgres -c \"SELECT datname FROM pg_database WHERE datname=''mathematical_trading'';\")", "Bash(PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c \"\\dt\")", "Bash(python -c \"import sys; sys.path.append(''.''); from agents.data_collector.data_collection_agent import DataCollectionAgent; print(''Import successful'')\")", "Bash(PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c \"SELECT COUNT(*) as market_data_count FROM market_data;\")", "Bash(PGPASSWORD:*)", "Bash(PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -c \"SELECT symbol, timestamp, open_price, close_price, volume, exchange FROM market_data WHERE symbol=''AAPL'' LIMIT 5;\")", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(cat:*)", "Bash(PGPASSWORD='hejhej' psql -h localhost -U trading_user -d mathematical_trading -f setup_trading_tables.sql)"], "deny": [], "ask": []}}