# AstroA Frontend 🚀

**Stellar Trading Interface**

The frontend dashboard for AstroA's multi-agent cryptocurrency analysis system. Built with React + Vite for lightning-fast performance and real-time market visualization.

## 🌟 Features

- 📊 Real-time crypto market dashboard
- 🧠 AI agent status monitoring
- 📈 Interactive charts and visualizations
- 📋 Automated report viewer
- 🎯 Asset selection interface

## 🚀 Quick Start

```bash
npm install
npm run dev
```

## 🛰️ Tech Stack

- **Framework**: React + Vite
- **Styling**: Tailwind CSS
- **Charts**: Chart.js / Plotly.js
- **State**: Redux Toolkit
- **Real-time**: WebSocket connections

---

*Navigate the markets with stellar precision.* ⭐
