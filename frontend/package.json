{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.9", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.19", "axios": "^1.12.2", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.2.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.3", "autoprefixer": "^10.4.21", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "postcss": "^8.5.6", "vite": "^7.1.7"}}