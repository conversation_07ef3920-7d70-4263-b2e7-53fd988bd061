#!/usr/bin/env python3
"""
Test script for Data Collection Agent
"""
import asyncio
import sys
import os
sys.path.append(os.getcwd())

from agents.data_collector.data_collection_agent import DataCollectionAgent

async def test_data_collection():
    """Test the data collection agent"""
    print("🚀 Testing Data Collection Agent")
    print("=" * 50)

    # Create agent
    agent = DataCollectionAgent()

    try:
        # Initialize agent
        print("📋 Initializing agent...")
        await agent.initialize()
        print("✅ Agent initialized successfully")

        # Test market data collection
        print("\n📊 Testing market data collection...")
        await agent.collect_and_store_market_data()
        print("✅ Market data collection completed")

        # Test news data collection (only if API key is available)
        print("\n📰 Testing news data collection...")
        if os.getenv('NEWS_API_KEY'):
            await agent.collect_and_store_news_data()
            print("✅ News data collection completed")
        else:
            print("⚠️ NEWS_API_KEY not found, skipping news collection")

        # Check agent status
        status = agent.get_status()
        print(f"\n📊 Agent Status:")
        print(f"  Agent ID: {status['agent_id']}")
        print(f"  Type: {status['agent_type']}")
        print(f"  Status: {status['status']}")
        print(f"  Running: {status['running']}")

        print("\n🎉 Data Collection Agent test completed successfully!")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False
    finally:
        await agent.cleanup()

    return True

if __name__ == "__main__":
    result = asyncio.run(test_data_collection())
    sys.exit(0 if result else 1)