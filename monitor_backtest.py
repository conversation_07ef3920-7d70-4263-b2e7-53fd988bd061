#!/usr/bin/env python3
"""
Monitor the running backtest and show periodic updates
"""
import time
import subprocess
import json
from pathlib import Path

def monitor_backtest():
    """Monitor the running backtest"""
    print("🔍 Monitoring AstroA Backtest Progress...")
    print("📊 Updates every 30 seconds\n")

    start_time = time.time()

    while True:
        try:
            # Check for any results files
            data_dir = Path("data")
            if data_dir.exists():
                json_files = list(data_dir.glob("backtest_results_*.json"))
                if json_files:
                    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)

                    with open(latest_file) as f:
                        data = json.load(f)

                    snapshots = data.get('portfolio_snapshots', [])
                    if snapshots:
                        latest = snapshots[-1]
                        print(f"💰 Latest Portfolio: ${latest['total_value']:,.2f}")
                        print(f"📊 Positions: {latest['positions_count']}")
                        print(f"🔄 Total Trades: {len(data.get('trades_executed', []))}")
                        print(f"📈 Cycles: {data.get('analysis_cycles', 0)}")

            elapsed = time.time() - start_time
            print(f"⏱️  Elapsed: {elapsed/60:.1f} minutes")
            print("-" * 40)

            time.sleep(30)

        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(30)

if __name__ == "__main__":
    monitor_backtest()