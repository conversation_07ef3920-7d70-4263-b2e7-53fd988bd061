-- Mathematical Trading System Database Schema

-- Market data table for OHLCV data
CREATE TABLE IF NOT EXISTS market_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(20,8),
    high_price DECIMAL(20,8),
    low_price DECIMAL(20,8),
    close_price DECIMAL(20,8),
    volume DECIMAL(20,8),
    exchange VARCHAR(50),
    timeframe VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp, exchange, timeframe)
);

-- News data table
CREATE TABLE IF NOT EXISTS news_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20),
    title TEXT,
    content TEXT,
    url TEXT UNIQUE,
    source VARCHAR(100),
    published_at TIMESTAMP,
    sentiment_score DECIMAL(5,4),
    relevance_score DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Features table for engineered features
CREATE TABLE IF NOT EXISTS features (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    timeframe VARCHAR(10),
    feature_name VARCHAR(100),
    feature_value DECIMAL(20,8),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, timestamp, timeframe, feature_name)
);
