#!/usr/bin/env python3
"""
Comprehensive installation test for Mathematical Trading System
"""
import sys
import subprocess
import importlib

def test_python_version():
    """Test Python version"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    if version >= (3, 11):
        print("✅ Python version is 3.11+")
        return True
    else:
        print("❌ Python version must be 3.11+")
        return False

def test_package_imports():
    """Test critical package imports"""
    packages = {
        'pandas': 'Data manipulation',
        'numpy': 'Numerical computing',
        'scipy': 'Scientific computing',
        'matplotlib': 'Plotting',
        'plotly': 'Interactive plots',
        'sympy': 'Symbolic mathematics',
        'sklearn': 'Machine learning',
        'lightgbm': 'Gradient boosting',
        'ccxt': 'Crypto exchange APIs',
        'yfinance': 'Financial data',
        'langchain': 'LLM framework',
        'fastapi': 'Web framework',
        'sqlalchemy': 'Database ORM',
        'redis': 'Cache client',
        'psycopg2': 'PostgreSQL client'
    }

    failed_imports = []
    print("\n📦 Testing package imports:")

    for package, description in packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {package:<15} - {description}")
        except ImportError as e:
            print(f"❌ {package:<15} - Failed: {e}")
            failed_imports.append(package)

    return failed_imports

def test_database_connection():
    """Test database connectivity"""
    print("\n🗄️  Testing database connections:")

    # Test PostgreSQL
    try:
        import psycopg2
        conn = psycopg2.connect(
            host='localhost',
            database='mathematical_trading',
            user='trading_user',
            password='hejhej'
        )
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
        table_count = cursor.fetchone()[0]
        print(f"✅ PostgreSQL: Connected, {table_count} tables")
        conn.close()
        pg_ok = True
    except Exception as e:
        print(f"❌ PostgreSQL: {e}")
        pg_ok = False

    # Test Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        info = r.info()
        print(f"✅ Redis: Connected, version {info['redis_version']}")
        redis_ok = True
    except Exception as e:
        print(f"❌ Redis: {e}")
        redis_ok = False

    return pg_ok and redis_ok

def test_api_functionality():
    """Test API functionality"""
    print("\n🌐 Testing API functionality:")

    # Test CCXT
    try:
        import ccxt
        exchange = ccxt.binance()
        markets = exchange.load_markets()
        print(f"✅ CCXT: {len(markets)} markets loaded from Binance")
        ccxt_ok = True
    except Exception as e:
        print(f"❌ CCXT: {e}")
        ccxt_ok = False

    # Test yfinance
    try:
        import yfinance as yf
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        if 'regularMarketPrice' in info:
            print(f"✅ yfinance: AAPL price ${info['regularMarketPrice']}")
            yf_ok = True
        else:
            print("⚠️ yfinance: Data retrieved but no price info")
            yf_ok = True
    except Exception as e:
        print(f"❌ yfinance: {e}")
        yf_ok = False

    return ccxt_ok and yf_ok

def test_mathematical_libraries():
    """Test mathematical computation libraries"""
    print("\n🧮 Testing mathematical libraries:")

    try:
        import numpy as np
        import pandas as pd
        from scipy import stats
        import sympy as sp

        # Test NumPy
        arr = np.random.normal(0, 1, 1000)
        mean = np.mean(arr)
        print(f"✅ NumPy: Random array mean = {mean:.3f}")

        # Test Pandas
        df = pd.DataFrame({'x': arr, 'y': arr * 2})
        corr = df.corr().iloc[0, 1]
        print(f"✅ Pandas: Correlation = {corr:.3f}")

        # Test SciPy
        stat, p_value = stats.normaltest(arr)
        print(f"✅ SciPy: Normality test p-value = {p_value:.3f}")

        # Test SymPy
        x = sp.Symbol('x')
        derivative = sp.diff(x**2 + 2*x + 1, x)
        print(f"✅ SymPy: d/dx(x²+2x+1) = {derivative}")

        return True
    except Exception as e:
        print(f"❌ Mathematical libraries: {e}")
        return False

def test_system_resources():
    """Test system resources"""
    print("\n💻 System resource check:")

    import psutil

    # CPU cores
    cpu_count = psutil.cpu_count()
    print(f"CPU cores: {cpu_count}")

    # Memory
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    print(f"Total RAM: {memory_gb:.1f} GB")
    print(f"Available RAM: {memory.available / (1024**3):.1f} GB")

    # Disk space
    disk = psutil.disk_usage('/')
    disk_free_gb = disk.free / (1024**3)
    print(f"Free disk space: {disk_free_gb:.1f} GB")

    # Check minimums
    sufficient = (cpu_count >= 2 and memory_gb >= 4 and disk_free_gb >= 10)
    if sufficient:
        print("✅ System resources sufficient")
    else:
        print("⚠️ System resources may be insufficient")

    return sufficient

def main():
    """Run all tests"""
    print("🚀 Mathematical Trading System - Installation Test")
    print("=" * 60)

    tests = [
        ("Python Version", test_python_version),
        ("Package Imports", lambda: len(test_package_imports()) == 0),
        ("Database Connections", test_database_connection),
        ("API Functionality", test_api_functionality),
        ("Mathematical Libraries", test_mathematical_libraries),
        ("System Resources", test_system_resources),
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Your installation is ready!")
        print("Next steps:")
        print("1. Configure your API keys in .env file")
        print("2. Start developing your agents")
        print("3. Run the development manual examples")
        return 0
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
