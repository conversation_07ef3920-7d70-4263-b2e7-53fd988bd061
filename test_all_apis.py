#!/usr/bin/env python3
"""
Test All API Connections for Enhanced Paper Trading
"""

import sys
import requests
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Config

def test_binance_api():
    """Test Binance API connection"""
    try:
        print("🔍 Testing Binance API...")
        
        # Test ping
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code != 200:
            print("❌ Binance ping failed")
            return False
        
        # Test getting BTC price
        response = requests.get("https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT", timeout=10)
        if response.status_code == 200:
            data = response.json()
            price = float(data['price'])
            print(f"✅ Binance API: BTC = ${price:,.2f}")
            return True
        else:
            print("❌ Binance price fetch failed")
            return False
            
    except Exception as e:
        print(f"❌ Binance API error: {e}")
        return False

def test_alpha_vantage_api():
    """Test Alpha Vantage API connection"""
    try:
        print("🔍 Testing Alpha Vantage API...")
        
        api_key = Config.ALPHA_VANTAGE_API_KEY
        if not api_key:
            print("⚠️  Alpha Vantage API key not found")
            return False
        
        url = "https://www.alphavantage.co/query"
        params = {
            'function': 'GLOBAL_QUOTE',
            'symbol': 'AAPL',
            'apikey': api_key
        }
        
        response = requests.get(url, params=params, timeout=15)
        if response.status_code == 200:
            data = response.json()
            if 'Global Quote' in data:
                price = data['Global Quote']['05. price']
                print(f"✅ Alpha Vantage API: AAPL = ${float(price):.2f}")
                return True
            else:
                print(f"⚠️  Alpha Vantage response: {data}")
                return False
        else:
            print("❌ Alpha Vantage request failed")
            return False
            
    except Exception as e:
        print(f"❌ Alpha Vantage API error: {e}")
        return False

def test_deepseek_api():
    """Test DeepSeek AI API connection"""
    try:
        print("🔍 Testing DeepSeek AI API...")
        
        api_key = Config.DEEPSEEK_API_KEY
        if not api_key:
            print("⚠️  DeepSeek API key not found")
            return False
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': 'deepseek-chat',
            'messages': [{'role': 'user', 'content': 'Hello, test message'}],
            'max_tokens': 10
        }
        
        response = requests.post(
            'https://api.deepseek.com/chat/completions',
            headers=headers,
            json=payload,
            timeout=15
        )
        
        if response.status_code == 200:
            print("✅ DeepSeek AI API: Connected")
            return True
        else:
            print(f"❌ DeepSeek API failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek API error: {e}")
        return False

def test_alpaca_api():
    """Test Alpaca API connection"""
    try:
        print("🔍 Testing Alpaca API...")
        
        from config.paper_trading_config import paper_config
        import alpaca_trade_api as tradeapi
        
        api = tradeapi.REST(
            paper_config.api_key,
            paper_config.secret_key,
            paper_config.base_url,
            api_version='v2'
        )
        
        account = api.get_account()
        print(f"✅ Alpaca API: Account ${float(account.portfolio_value):,.2f}")
        return True
        
    except Exception as e:
        print(f"❌ Alpaca API error: {e}")
        return False

def main():
    print("🌟 AstroA Enhanced Paper Trading - API Connection Test")
    print("=" * 60)
    
    results = {
        'Binance': test_binance_api(),
        'Alpha Vantage': test_alpha_vantage_api(),
        'DeepSeek AI': test_deepseek_api(),
        'Alpaca': test_alpaca_api()
    }
    
    print("\n" + "=" * 60)
    print("📊 API Test Results:")
    
    working_apis = 0
    for api_name, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {api_name}")
        if status:
            working_apis += 1
    
    print(f"\n🎯 {working_apis}/{len(results)} APIs working")
    
    if working_apis >= 2:
        print("✅ Sufficient APIs for enhanced paper trading!")
    else:
        print("⚠️  Limited API access - will use database fallback")
    
    print("\n🚀 Ready to start enhanced paper trading!")

if __name__ == "__main__":
    main()
