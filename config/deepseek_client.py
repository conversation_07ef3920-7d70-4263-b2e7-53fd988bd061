"""
DeepSeek AI Client for AstroA
Provides a unified interface for interacting with DeepSeek AI API
"""
import os
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Union, AsyncGenerator
from dataclasses import dataclass
from config.settings import Config

@dataclass
class ChatMessage:
    """Represents a chat message"""
    role: str  # 'system', 'user', 'assistant'
    content: str

class DeepSeekClient:
    """DeepSeek AI API Client for AstroA agents"""

    def __init__(self, api_key: str = None, base_url: str = None):
        self.api_key = api_key or Config.DEEPSEEK_API_KEY
        self.base_url = base_url or Config.DEEPSEEK_BASE_URL

        if not self.api_key:
            raise ValueError("DeepSeek API key is required")

        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    async def chat_completion(
        self,
        messages: List[Union[ChatMessage, Dict]],
        model: str = None,
        temperature: float = None,
        max_tokens: int = None,
        stream: bool = False
    ) -> Union[Dict, AsyncGenerator]:
        """
        Send a chat completion request to DeepSeek API

        Args:
            messages: List of chat messages
            model: Model to use (defaults to config)
            temperature: Sampling temperature (defaults to config)
            max_tokens: Maximum tokens to generate (defaults to config)
            stream: Whether to stream the response

        Returns:
            Dict with completion response or AsyncGenerator for streaming
        """
        # Convert ChatMessage objects to dicts
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, ChatMessage):
                formatted_messages.append({"role": msg.role, "content": msg.content})
            else:
                formatted_messages.append(msg)

        payload = {
            "model": model or Config.DEEPSEEK_MODEL,
            "messages": formatted_messages,
            "temperature": temperature or Config.DEEPSEEK_TEMPERATURE,
            "max_tokens": max_tokens or Config.DEEPSEEK_MAX_TOKENS,
            "stream": stream
        }

        url = f"{self.base_url}/chat/completions"

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=self.headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"DeepSeek API error {response.status}: {error_text}")

                if stream:
                    return self._stream_response(response)
                else:
                    return await response.json()

    async def _stream_response(self, response) -> AsyncGenerator[Dict, None]:
        """Handle streaming response from DeepSeek API"""
        async for line in response.content:
            line = line.decode('utf-8').strip()
            if line.startswith('data: '):
                data = line[6:]  # Remove 'data: ' prefix
                if data == '[DONE]':
                    break
                try:
                    chunk = json.loads(data)
                    yield chunk
                except json.JSONDecodeError:
                    continue

    async def analyze_market_data(self, data: Dict, context: str = "") -> str:
        """
        Specialized method for market data analysis

        Args:
            data: Market data to analyze
            context: Additional context for analysis

        Returns:
            Analysis result as string
        """
        system_prompt = """You are AstroA, an expert cryptocurrency market analyst and AI agent.
        Your role is to analyze market data and provide actionable insights for trading decisions.

        Focus on:
        - Price trends and momentum
        - Volume analysis
        - Risk assessment
        - Key support/resistance levels
        - Market sentiment indicators

        Provide concise, data-driven analysis with specific recommendations."""

        user_prompt = f"""Analyze the following market data:

{json.dumps(data, indent=2)}

Additional context: {context}

Provide a comprehensive analysis with:
1. Current market condition assessment
2. Key trend indicators
3. Risk factors
4. Recommended actions
5. Confidence level (1-10)"""

        messages = [
            ChatMessage("system", system_prompt),
            ChatMessage("user", user_prompt)
        ]

        try:
            response = await self.chat_completion(messages)
            return response['choices'][0]['message']['content']
        except Exception as e:
            return f"Analysis failed: {str(e)}"

    async def generate_trading_signal(self, asset: str, indicators: Dict) -> Dict:
        """
        Generate trading signals for specific assets

        Args:
            asset: Asset symbol (e.g., 'BTC/USDT')
            indicators: Technical indicators data

        Returns:
            Dict with signal, confidence, and reasoning
        """
        system_prompt = """You are AstroA's trading signal generator. Analyze technical indicators
        and generate precise trading signals with confidence scores."""

        user_prompt = f"""Generate a trading signal for {asset} based on these indicators:

{json.dumps(indicators, indent=2)}

Return your response in this exact JSON format:
{{
    "signal": "BUY|SELL|HOLD",
    "confidence": 0.0-1.0,
    "reasoning": "Brief explanation",
    "timeframe": "Short/Medium/Long term",
    "risk_level": "Low/Medium/High"
}}"""

        messages = [
            ChatMessage("system", system_prompt),
            ChatMessage("user", user_prompt)
        ]

        try:
            response = await self.chat_completion(messages, temperature=0.1)
            content = response['choices'][0]['message']['content']

            # Try to parse JSON response
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "signal": "HOLD",
                    "confidence": 0.5,
                    "reasoning": "Unable to parse AI response",
                    "timeframe": "Unknown",
                    "risk_level": "Medium"
                }
        except Exception as e:
            return {
                "signal": "HOLD",
                "confidence": 0.0,
                "reasoning": f"Signal generation failed: {str(e)}",
                "timeframe": "Unknown",
                "risk_level": "High"
            }

    async def analyze_news_sentiment(self, news_items: List[Dict]) -> Dict:
        """
        Analyze news sentiment and market impact

        Args:
            news_items: List of news articles

        Returns:
            Sentiment analysis results
        """
        system_prompt = """You are AstroA's news sentiment analyzer. Analyze cryptocurrency
        news and assess their potential market impact."""

        news_text = "\n".join([f"- {item.get('title', '')}: {item.get('description', '')}"
                              for item in news_items[:10]])  # Limit to 10 items

        user_prompt = f"""Analyze these cryptocurrency news items for sentiment and market impact:

{news_text}

Return analysis in this JSON format:
{{
    "overall_sentiment": "BULLISH|BEARISH|NEUTRAL",
    "sentiment_score": -1.0 to 1.0,
    "market_impact": "HIGH|MEDIUM|LOW",
    "key_themes": ["theme1", "theme2"],
    "summary": "Brief summary of key developments"
}}"""

        messages = [
            ChatMessage("system", system_prompt),
            ChatMessage("user", user_prompt)
        ]

        try:
            response = await self.chat_completion(messages, temperature=0.2)
            content = response['choices'][0]['message']['content']
            return json.loads(content)
        except Exception as e:
            return {
                "overall_sentiment": "NEUTRAL",
                "sentiment_score": 0.0,
                "market_impact": "LOW",
                "key_themes": [],
                "summary": f"Analysis failed: {str(e)}"
            }

# Global client instance
deepseek_client = None

def get_deepseek_client() -> DeepSeekClient:
    """Get or create global DeepSeek client instance"""
    global deepseek_client
    if deepseek_client is None:
        deepseek_client = DeepSeekClient()
    return deepseek_client

# Async context manager for batch operations
class DeepSeekBatch:
    """Context manager for efficient batch operations"""

    def __init__(self, client: DeepSeekClient):
        self.client = client
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()