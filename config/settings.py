"""
AstroA Configuration Settings
Centralized configuration for DeepSeek AI API and other services
"""
import os
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv()

class Config:
    """Main configuration class for AstroA"""

    # Application Settings
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')

    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL', 'postgresql://trading_user:secure_password_123@localhost:5432/mathematical_trading')
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')

    # DeepSeek AI Configuration
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    DEEPSEEK_BASE_URL = os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com')
    DEEPSEEK_MODEL = os.getenv('DEEPSEEK_MODEL', 'deepseek-chat')
    DEEPSEEK_MAX_TOKENS = int(os.getenv('DEEPSEEK_MAX_TOKENS', '4000'))
    DEEPSEEK_TEMPERATURE = float(os.getenv('DEEPSEEK_TEMPERATURE', '0.1'))

    # Exchange API Keys
    BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
    BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')
    COINBASE_API_KEY = os.getenv('COINBASE_API_KEY')
    COINBASE_SECRET_KEY = os.getenv('COINBASE_SECRET_KEY')

    # News & Data APIs
    NEWS_API_KEY = os.getenv('NEWS_API_KEY')
    ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY')
    QUANDL_API_KEY = os.getenv('QUANDL_API_KEY')

    # Trading Configuration
    MAX_POSITION_SIZE = float(os.getenv('MAX_POSITION_SIZE', '0.1'))
    MAX_PORTFOLIO_VOLATILITY = float(os.getenv('MAX_PORTFOLIO_VOLATILITY', '0.2'))
    MAX_DRAWDOWN_LIMIT = float(os.getenv('MAX_DRAWDOWN_LIMIT', '0.15'))
    UPDATE_INTERVAL_SECONDS = int(os.getenv('UPDATE_INTERVAL_SECONDS', '60'))

    # Data Directory Paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / 'data'
    REPORTS_DIR = BASE_DIR / 'reports'
    LOGS_DIR = BASE_DIR / 'logs'

    # Agent Configuration
    AGENT_TIMEOUT = int(os.getenv('AGENT_TIMEOUT', '300'))  # 5 minutes
    MAX_CONCURRENT_AGENTS = int(os.getenv('MAX_CONCURRENT_AGENTS', '10'))

    @classmethod
    def validate_config(cls):
        """Validate required configuration values"""
        required_fields = [
            'DEEPSEEK_API_KEY',
            'DATABASE_URL',
        ]

        missing_fields = []
        for field in required_fields:
            if not getattr(cls, field, None):
                missing_fields.append(field)

        if missing_fields:
            raise ValueError(f"Missing required configuration: {', '.join(missing_fields)}")

        return True

# Create directories if they don't exist
for directory in [Config.DATA_DIR, Config.REPORTS_DIR, Config.LOGS_DIR]:
    directory.mkdir(exist_ok=True)