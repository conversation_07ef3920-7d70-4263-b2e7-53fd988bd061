"""
Paper Trading Configuration for AstroA System
"""
import os
from typing import Dict, List
from dataclasses import dataclass, field
from dotenv import load_dotenv

load_dotenv()

@dataclass
class PaperTradingConfig:
    """Configuration for paper trading environment"""

    # Alpaca API Configuration
    api_key: str = os.getenv('ALPACA_API_KEY', '')
    secret_key: str = os.getenv('ALPACA_SECRET_KEY', '')
    base_url: str = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')

    # Trading Parameters
    initial_cash: float = float(os.getenv('PAPER_TRADING_INITIAL_CASH', '100000'))
    max_positions: int = int(os.getenv('PAPER_TRADING_MAX_POSITIONS', '10'))
    risk_limit: float = float(os.getenv('PAPER_TRADING_RISK_LIMIT', '0.02'))

    # Symbols to trade (crypto focus matching AstroA)
    tradable_symbols: List[str] = field(default_factory=lambda: [
        'BTCUSD',   # Bitcoin
        'ETHUSD',   # Ethereum
        'ADAUSD',   # Cardano
        'SOLUSD',   # Solana
        'DOTUSD',   # Polkadot
        'LINKUSD',  # Chainlink
        'AVAXUSD',  # Avalanche
        'MATICUSD'  # Polygon
    ])

    # Strategy Configuration - Enhanced with your original concept
    strategies_enabled: List[str] = field(default_factory=lambda: [
        'news_sentiment',    # PRIMARY: Your original news-based idea
        'ml_enhanced',       # ML models for prediction
        'mean_reversion',    # Technical analysis backup
        'momentum'           # Technical analysis backup
    ])

    # Risk Management
    max_position_size: float = 0.15  # 15% of portfolio per position
    stop_loss_percentage: float = 0.02  # 2% stop loss
    take_profit_percentage: float = 0.04  # 4% take profit
    daily_loss_limit: float = 0.05  # 5% daily loss limit

    # Data Feed Configuration
    real_time_enabled: bool = os.getenv('REAL_TIME_DATA_ENABLED', 'true').lower() == 'true'
    data_update_interval: int = 60  # seconds

    # Logging
    log_trades: bool = True
    log_signals: bool = True
    log_performance: bool = True

    def validate(self):
        """Validate configuration"""
        if not self.api_key:
            raise ValueError("ALPACA_API_KEY is required for paper trading")
        if not self.secret_key:
            raise ValueError("ALPACA_SECRET_KEY is required for paper trading")
        
        if self.initial_cash <= 0:
            raise ValueError("Initial cash must be positive")
        
        if not self.tradable_symbols:
            raise ValueError("At least one tradable symbol is required")
        
        return True

    def get_trading_config(self) -> Dict:
        """Get trading configuration as dictionary"""
        return {
            'initial_cash': self.initial_cash,
            'max_positions': self.max_positions,
            'risk_limit': self.risk_limit,
            'max_position_size': self.max_position_size,
            'stop_loss_percentage': self.stop_loss_percentage,
            'take_profit_percentage': self.take_profit_percentage,
            'daily_loss_limit': self.daily_loss_limit
        }

# Global configuration instance
paper_config = PaperTradingConfig()
