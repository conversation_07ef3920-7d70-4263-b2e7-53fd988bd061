"""
Real-Time Market Data Feed System
"""

import asyncio
import websockets
import aiohttp
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Callable, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum
import time
import redis
from collections import deque
import threading

class DataProvider(Enum):
    """Supported market data providers"""
    ALPHA_VANTAGE = "alpha_vantage"
    IEX_CLOUD = "iex_cloud"
    FINNHUB = "finnhub"
    POLYGON = "polygon"
    YAHOO_FINANCE = "yahoo_finance"
    BINANCE = "binance"  # For crypto
    ALPACA = "alpaca"

@dataclass
class MarketTick:
    """Individual market data tick"""
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    bid: Optional[float] = None
    ask: Optional[float] = None
    bid_size: Optional[float] = None
    ask_size: Optional[float] = None
    provider: str = ""
    exchange: str = ""

@dataclass
class MarketBar:
    """OHLCV bar data"""
    symbol: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    timeframe: str
    provider: str = ""

class RealTimeMarketData:
    """Real-time market data aggregator and distributor"""

    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379):
        self.providers: Dict[str, 'DataProvider'] = {}
        self.subscribers: List[Callable] = []
        self.active_symbols: set = set()

        # Redis for real-time data distribution
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)

        # Data buffers
        self.tick_buffer: Dict[str, deque] = {}
        self.bar_buffer: Dict[str, deque] = {}

        # Performance tracking
        self.latency_stats = {}
        self.message_rates = {}

        # Status tracking
        self.is_running = False
        self.connection_status = {}

        self.logger = logging.getLogger("RealTimeMarketData")

    async def add_provider(self, provider_name: str, provider_config: Dict[str, Any]):
        """Add a market data provider"""
        try:
            provider = MarketDataFeed(provider_name, provider_config)
            await provider.connect()

            self.providers[provider_name] = provider
            self.connection_status[provider_name] = "connected"

            # Set up data handler
            provider.on_tick = self._handle_tick
            provider.on_bar = self._handle_bar
            provider.on_error = self._handle_provider_error

            self.logger.info(f"Added market data provider: {provider_name}")

        except Exception as e:
            self.logger.error(f"Failed to add provider {provider_name}: {e}")
            self.connection_status[provider_name] = "failed"

    async def subscribe_symbols(self, symbols: List[str], provider_name: Optional[str] = None):
        """Subscribe to real-time data for symbols"""
        try:
            self.active_symbols.update(symbols)

            # Initialize buffers for new symbols
            for symbol in symbols:
                if symbol not in self.tick_buffer:
                    self.tick_buffer[symbol] = deque(maxlen=1000)
                    self.bar_buffer[symbol] = deque(maxlen=500)

            # Subscribe with providers
            if provider_name:
                if provider_name in self.providers:
                    await self.providers[provider_name].subscribe(symbols)
            else:
                # Subscribe with all providers
                for provider in self.providers.values():
                    await provider.subscribe(symbols)

            self.logger.info(f"Subscribed to {len(symbols)} symbols")

        except Exception as e:
            self.logger.error(f"Error subscribing to symbols: {e}")

    def add_subscriber(self, callback: Callable[[MarketTick], None]):
        """Add a callback function for real-time data"""
        self.subscribers.append(callback)

    async def start(self):
        """Start real-time data collection"""
        try:
            self.is_running = True

            # Start all providers
            tasks = []
            for provider in self.providers.values():
                tasks.append(asyncio.create_task(provider.start_streaming()))

            # Start performance monitoring
            tasks.append(asyncio.create_task(self._monitor_performance()))

            # Start data distribution
            tasks.append(asyncio.create_task(self._distribute_data()))

            await asyncio.gather(*tasks)

        except Exception as e:
            self.logger.error(f"Error starting real-time data: {e}")
            self.is_running = False

    async def stop(self):
        """Stop real-time data collection"""
        try:
            self.is_running = False

            # Stop all providers
            for provider in self.providers.values():
                await provider.disconnect()

            self.logger.info("Stopped real-time market data")

        except Exception as e:
            self.logger.error(f"Error stopping real-time data: {e}")

    async def _handle_tick(self, tick: MarketTick):
        """Handle incoming market tick"""
        try:
            # Record reception time for latency calculation
            reception_time = time.time_ns()

            # Add to buffer
            if tick.symbol in self.tick_buffer:
                self.tick_buffer[tick.symbol].append(tick)

            # Publish to Redis
            tick_data = {
                'symbol': tick.symbol,
                'timestamp': tick.timestamp.isoformat(),
                'price': tick.price,
                'volume': tick.volume,
                'bid': tick.bid,
                'ask': tick.ask,
                'provider': tick.provider,
                'reception_time': reception_time
            }

            self.redis_client.publish(f"market_tick:{tick.symbol}", json.dumps(tick_data))

            # Notify subscribers
            for callback in self.subscribers:
                try:
                    await callback(tick)
                except Exception as e:
                    self.logger.error(f"Error in subscriber callback: {e}")

            # Update performance stats
            self._update_latency_stats(tick.provider, reception_time)

        except Exception as e:
            self.logger.error(f"Error handling tick: {e}")

    async def _handle_bar(self, bar: MarketBar):
        """Handle incoming market bar"""
        try:
            # Add to buffer
            if bar.symbol in self.bar_buffer:
                self.bar_buffer[bar.symbol].append(bar)

            # Publish to Redis
            bar_data = {
                'symbol': bar.symbol,
                'timestamp': bar.timestamp.isoformat(),
                'open_price': bar.open_price,
                'high_price': bar.high_price,
                'low_price': bar.low_price,
                'close_price': bar.close_price,
                'volume': bar.volume,
                'timeframe': bar.timeframe,
                'provider': bar.provider
            }

            self.redis_client.publish(f"market_bar:{bar.symbol}", json.dumps(bar_data))

        except Exception as e:
            self.logger.error(f"Error handling bar: {e}")

    def _handle_provider_error(self, provider_name: str, error: Exception):
        """Handle provider errors"""
        self.logger.error(f"Provider {provider_name} error: {error}")
        self.connection_status[provider_name] = "error"

    async def _monitor_performance(self):
        """Monitor data feed performance"""
        while self.is_running:
            try:
                # Calculate message rates
                for symbol in self.active_symbols:
                    if symbol in self.tick_buffer:
                        recent_ticks = [t for t in self.tick_buffer[symbol]
                                      if (datetime.now() - t.timestamp).total_seconds() < 60]
                        self.message_rates[symbol] = len(recent_ticks)

                # Log performance stats
                avg_latency = np.mean(list(self.latency_stats.values())) if self.latency_stats else 0
                total_rate = sum(self.message_rates.values())

                self.logger.info(f"Performance - Avg Latency: {avg_latency:.2f}ms, Total Rate: {total_rate} msg/min")

                await asyncio.sleep(60)  # Monitor every minute

            except Exception as e:
                self.logger.error(f"Error in performance monitoring: {e}")
                await asyncio.sleep(60)

    async def _distribute_data(self):
        """Distribute data to internal systems"""
        while self.is_running:
            try:
                # This could distribute data to trading algorithms,
                # database storage, etc.
                await asyncio.sleep(0.1)

            except Exception as e:
                self.logger.error(f"Error in data distribution: {e}")
                await asyncio.sleep(1)

    def _update_latency_stats(self, provider: str, reception_time: int):
        """Update latency statistics"""
        try:
            # Calculate latency (simplified - would need provider timestamp)
            current_time = time.time_ns()
            latency_ms = (current_time - reception_time) / 1_000_000

            if provider not in self.latency_stats:
                self.latency_stats[provider] = deque(maxlen=100)

            self.latency_stats[provider].append(latency_ms)

        except Exception as e:
            self.logger.error(f"Error updating latency stats: {e}")

    def get_latest_tick(self, symbol: str) -> Optional[MarketTick]:
        """Get latest tick for symbol"""
        if symbol in self.tick_buffer and self.tick_buffer[symbol]:
            return self.tick_buffer[symbol][-1]
        return None

    def get_latest_bar(self, symbol: str) -> Optional[MarketBar]:
        """Get latest bar for symbol"""
        if symbol in self.bar_buffer and self.bar_buffer[symbol]:
            return self.bar_buffer[symbol][-1]
        return None

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'connection_status': self.connection_status,
            'active_symbols': len(self.active_symbols),
            'message_rates': self.message_rates,
            'avg_latency_by_provider': {
                provider: np.mean(list(latencies))
                for provider, latencies in self.latency_stats.items()
            },
            'is_running': self.is_running
        }

class MarketDataFeed:
    """Individual market data provider feed"""

    def __init__(self, provider_name: str, config: Dict[str, Any]):
        self.provider_name = provider_name
        self.config = config
        self.is_connected = False
        self.subscribed_symbols = set()

        # Callbacks
        self.on_tick: Optional[Callable] = None
        self.on_bar: Optional[Callable] = None
        self.on_error: Optional[Callable] = None

        # Connection objects
        self.websocket = None
        self.session = None

        self.logger = logging.getLogger(f"MarketDataFeed.{provider_name}")

    async def connect(self):
        """Connect to the market data provider"""
        try:
            if self.provider_name == "alpha_vantage":
                await self._connect_alpha_vantage()
            elif self.provider_name == "iex_cloud":
                await self._connect_iex_cloud()
            elif self.provider_name == "finnhub":
                await self._connect_finnhub()
            elif self.provider_name == "polygon":
                await self._connect_polygon()
            elif self.provider_name == "binance":
                await self._connect_binance()
            else:
                raise ValueError(f"Unsupported provider: {self.provider_name}")

            self.is_connected = True
            self.logger.info(f"Connected to {self.provider_name}")

        except Exception as e:
            self.logger.error(f"Failed to connect to {self.provider_name}: {e}")
            if self.on_error:
                self.on_error(self.provider_name, e)

    async def subscribe(self, symbols: List[str]):
        """Subscribe to symbols"""
        try:
            self.subscribed_symbols.update(symbols)

            if self.provider_name == "finnhub":
                await self._subscribe_finnhub(symbols)
            elif self.provider_name == "polygon":
                await self._subscribe_polygon(symbols)
            elif self.provider_name == "binance":
                await self._subscribe_binance(symbols)
            # Add other providers as needed

            self.logger.info(f"Subscribed to {len(symbols)} symbols on {self.provider_name}")

        except Exception as e:
            self.logger.error(f"Failed to subscribe on {self.provider_name}: {e}")

    async def start_streaming(self):
        """Start streaming data"""
        while self.is_connected:
            try:
                if self.websocket:
                    await self._handle_websocket_messages()
                else:
                    # Fallback to REST API polling
                    await self._poll_rest_api()

            except Exception as e:
                self.logger.error(f"Streaming error on {self.provider_name}: {e}")
                if self.on_error:
                    self.on_error(self.provider_name, e)

                # Attempt reconnection
                await asyncio.sleep(5)
                await self._reconnect()

    async def disconnect(self):
        """Disconnect from provider"""
        try:
            self.is_connected = False

            if self.websocket:
                await self.websocket.close()

            if self.session:
                await self.session.close()

            self.logger.info(f"Disconnected from {self.provider_name}")

        except Exception as e:
            self.logger.error(f"Error disconnecting from {self.provider_name}: {e}")

    # Provider-specific implementations
    async def _connect_finnhub(self):
        """Connect to Finnhub WebSocket"""
        api_key = self.config.get('api_key')
        if not api_key:
            raise ValueError("Finnhub API key required")

        uri = f"wss://ws.finnhub.io?token={api_key}"
        self.websocket = await websockets.connect(uri)

    async def _subscribe_finnhub(self, symbols: List[str]):
        """Subscribe to Finnhub symbols"""
        for symbol in symbols:
            message = {"type": "subscribe", "symbol": symbol}
            await self.websocket.send(json.dumps(message))

    async def _connect_polygon(self):
        """Connect to Polygon WebSocket"""
        api_key = self.config.get('api_key')
        if not api_key:
            raise ValueError("Polygon API key required")

        uri = f"wss://socket.polygon.io/stocks"
        self.websocket = await websockets.connect(uri)

        # Authenticate
        auth_message = {"action": "auth", "params": api_key}
        await self.websocket.send(json.dumps(auth_message))

    async def _subscribe_polygon(self, symbols: List[str]):
        """Subscribe to Polygon symbols"""
        # Subscribe to trades
        subscribe_message = {
            "action": "subscribe",
            "params": f"T.{',T.'.join(symbols)}"
        }
        await self.websocket.send(json.dumps(subscribe_message))

    async def _connect_binance(self):
        """Connect to Binance WebSocket (for crypto)"""
        uri = "wss://stream.binance.com:9443/ws/stream"
        self.websocket = await websockets.connect(uri)

    async def _subscribe_binance(self, symbols: List[str]):
        """Subscribe to Binance symbols"""
        streams = [f"{symbol.lower()}@ticker" for symbol in symbols]
        subscribe_message = {
            "method": "SUBSCRIBE",
            "params": streams,
            "id": 1
        }
        await self.websocket.send(json.dumps(subscribe_message))

    async def _connect_alpha_vantage(self):
        """Connect to Alpha Vantage (REST only)"""
        self.session = aiohttp.ClientSession()

    async def _connect_iex_cloud(self):
        """Connect to IEX Cloud"""
        self.session = aiohttp.ClientSession()

    async def _handle_websocket_messages(self):
        """Handle incoming WebSocket messages"""
        try:
            message = await self.websocket.recv()
            data = json.loads(message)

            if self.provider_name == "finnhub":
                await self._parse_finnhub_message(data)
            elif self.provider_name == "polygon":
                await self._parse_polygon_message(data)
            elif self.provider_name == "binance":
                await self._parse_binance_message(data)

        except Exception as e:
            self.logger.error(f"Error handling WebSocket message: {e}")

    async def _parse_finnhub_message(self, data: Dict):
        """Parse Finnhub WebSocket message"""
        if data.get("type") == "trade":
            for trade in data.get("data", []):
                tick = MarketTick(
                    symbol=trade["s"],
                    timestamp=datetime.fromtimestamp(trade["t"] / 1000),
                    price=trade["p"],
                    volume=trade["v"],
                    provider=self.provider_name
                )

                if self.on_tick:
                    await self.on_tick(tick)

    async def _parse_polygon_message(self, data: List):
        """Parse Polygon WebSocket message"""
        for item in data:
            if item.get("ev") == "T":  # Trade event
                tick = MarketTick(
                    symbol=item["sym"],
                    timestamp=datetime.fromtimestamp(item["t"] / 1000),
                    price=item["p"],
                    volume=item["s"],
                    provider=self.provider_name
                )

                if self.on_tick:
                    await self.on_tick(tick)

    async def _parse_binance_message(self, data: Dict):
        """Parse Binance WebSocket message"""
        if "data" in data:
            ticker_data = data["data"]
            tick = MarketTick(
                symbol=ticker_data["s"],
                timestamp=datetime.now(),  # Binance doesn't provide trade timestamp in ticker
                price=float(ticker_data["c"]),  # Close price
                volume=float(ticker_data["v"]),  # 24hr volume
                bid=float(ticker_data["b"]),
                ask=float(ticker_data["a"]),
                provider=self.provider_name
            )

            if self.on_tick:
                await self.on_tick(tick)

    async def _poll_rest_api(self):
        """Poll REST API for data (fallback)"""
        try:
            if self.provider_name == "alpha_vantage":
                await self._poll_alpha_vantage()
            elif self.provider_name == "iex_cloud":
                await self._poll_iex_cloud()

            await asyncio.sleep(1)  # Poll every second

        except Exception as e:
            self.logger.error(f"Error polling REST API: {e}")

    async def _poll_alpha_vantage(self):
        """Poll Alpha Vantage REST API"""
        api_key = self.config.get('api_key')

        for symbol in self.subscribed_symbols:
            try:
                url = f"https://www.alphavantage.co/query"
                params = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': symbol,
                    'apikey': api_key
                }

                async with self.session.get(url, params=params) as response:
                    data = await response.json()
                    quote = data.get('Global Quote', {})

                    if quote:
                        tick = MarketTick(
                            symbol=symbol,
                            timestamp=datetime.now(),
                            price=float(quote.get('05. price', 0)),
                            volume=float(quote.get('06. volume', 0)),
                            provider=self.provider_name
                        )

                        if self.on_tick:
                            await self.on_tick(tick)

            except Exception as e:
                self.logger.error(f"Error polling Alpha Vantage for {symbol}: {e}")

    async def _poll_iex_cloud(self):
        """Poll IEX Cloud REST API"""
        api_key = self.config.get('api_key')

        for symbol in self.subscribed_symbols:
            try:
                url = f"https://cloud.iexapis.com/stable/stock/{symbol}/quote"
                params = {'token': api_key}

                async with self.session.get(url, params=params) as response:
                    data = await response.json()

                    tick = MarketTick(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        price=data.get('latestPrice', 0),
                        volume=data.get('volume', 0),
                        bid=data.get('iexBidPrice'),
                        ask=data.get('iexAskPrice'),
                        provider=self.provider_name
                    )

                    if self.on_tick:
                        await self.on_tick(tick)

            except Exception as e:
                self.logger.error(f"Error polling IEX Cloud for {symbol}: {e}")

    async def _reconnect(self):
        """Attempt to reconnect"""
        try:
            await self.disconnect()
            await asyncio.sleep(5)
            await self.connect()

            # Re-subscribe to symbols
            if self.subscribed_symbols:
                await self.subscribe(list(self.subscribed_symbols))

        except Exception as e:
            self.logger.error(f"Failed to reconnect to {self.provider_name}: {e}")

# Example usage and configuration
class MarketDataConfig:
    """Market data configuration helper"""

    @staticmethod
    def get_provider_configs() -> Dict[str, Dict[str, Any]]:
        """Get default provider configurations"""
        return {
            'finnhub': {
                'api_key': 'your_finnhub_api_key',
                'supports_websocket': True,
                'rate_limit': 60  # requests per minute
            },
            'polygon': {
                'api_key': 'your_polygon_api_key',
                'supports_websocket': True,
                'rate_limit': 1000
            },
            'alpha_vantage': {
                'api_key': 'your_alpha_vantage_api_key',
                'supports_websocket': False,
                'rate_limit': 5
            },
            'iex_cloud': {
                'api_key': 'your_iex_api_key',
                'supports_websocket': False,
                'rate_limit': 100
            },
            'binance': {
                'supports_websocket': True,
                'rate_limit': 1200
            }
        }