"""
Failover Manager for System Reliability
"""

import asyncio
import json
import time
import psutil
import threading
from typing import Dict, List, Optional, Callable, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
import subprocess
import socket
import redis
from collections import defaultdict, deque

class ComponentStatus(Enum):
    """Component status states"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILING = "failing"
    FAILED = "failed"
    RECOVERING = "recovering"
    MAINTENANCE = "maintenance"

class FailoverTrigger(Enum):
    """Failover trigger types"""
    HEALTH_CHECK_FAILED = "health_check_failed"
    PERFORMANCE_DEGRADED = "performance_degraded"
    CONNECTION_LOST = "connection_lost"
    RESOURCE_EXHAUSTED = "resource_exhausted"
    MANUAL_TRIGGER = "manual_trigger"
    CASCADE_FAILURE = "cascade_failure"

@dataclass
class ComponentHealth:
    """Component health information"""
    component_id: str
    status: ComponentStatus
    last_check: datetime
    failure_count: int = 0
    recovery_attempts: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    dependencies: Set[str] = field(default_factory=set)

@dataclass
class FailoverEvent:
    """Failover event record"""
    event_id: str
    timestamp: datetime
    component_id: str
    trigger: FailoverTrigger
    primary_instance: str
    backup_instance: str
    success: bool
    duration_ms: int
    metadata: Dict[str, Any] = field(default_factory=dict)

class HealthChecker:
    """Health checking for system components"""

    def __init__(self, check_interval: float = 5.0):
        self.check_interval = check_interval
        self.health_checks: Dict[str, Callable] = {}
        self.component_health: Dict[str, ComponentHealth] = {}
        self.is_running = False
        self.failure_thresholds = {
            'max_failures': 3,
            'failure_window_minutes': 5,
            'response_timeout_seconds': 10
        }
        self.logger = logging.getLogger("HealthChecker")

    def register_component(self, component_id: str, health_check: Callable[[], bool], dependencies: Set[str] = None):
        """Register component for health checking"""
        self.health_checks[component_id] = health_check
        self.component_health[component_id] = ComponentHealth(
            component_id=component_id,
            status=ComponentStatus.HEALTHY,
            last_check=datetime.now(),
            dependencies=dependencies or set()
        )
        self.logger.info(f"Registered component for health checking: {component_id}")

    async def start(self):
        """Start health checking"""
        self.is_running = True
        await self._health_check_loop()

    async def stop(self):
        """Stop health checking"""
        self.is_running = False

    async def _health_check_loop(self):
        """Main health checking loop"""
        while self.is_running:
            try:
                await self._check_all_components()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(self.check_interval)

    async def _check_all_components(self):
        """Check health of all registered components"""
        for component_id in self.health_checks:
            await self._check_component_health(component_id)

    async def _check_component_health(self, component_id: str):
        """Check health of individual component"""
        try:
            health_check = self.health_checks[component_id]
            component_health = self.component_health[component_id]

            # Run health check with timeout
            start_time = time.time()
            try:
                if asyncio.iscoroutinefunction(health_check):
                    is_healthy = await asyncio.wait_for(
                        health_check(),
                        timeout=self.failure_thresholds['response_timeout_seconds']
                    )
                else:
                    # Run in executor for sync functions
                    is_healthy = await asyncio.get_event_loop().run_in_executor(
                        None, health_check
                    )
            except asyncio.TimeoutError:
                is_healthy = False
                self.logger.warning(f"Health check timeout for {component_id}")

            response_time = (time.time() - start_time) * 1000  # ms

            # Update component health
            component_health.last_check = datetime.now()
            component_health.metadata['last_response_time_ms'] = response_time

            if is_healthy:
                if component_health.status != ComponentStatus.HEALTHY:
                    self.logger.info(f"Component {component_id} recovered")
                    component_health.recovery_attempts = 0

                component_health.status = ComponentStatus.HEALTHY
                component_health.failure_count = 0
            else:
                component_health.failure_count += 1

                # Determine status based on failure count
                if component_health.failure_count >= self.failure_thresholds['max_failures']:
                    if component_health.status != ComponentStatus.FAILED:
                        self.logger.error(f"Component {component_id} failed after {component_health.failure_count} failures")
                    component_health.status = ComponentStatus.FAILED
                elif component_health.failure_count >= 2:
                    component_health.status = ComponentStatus.FAILING
                else:
                    component_health.status = ComponentStatus.DEGRADED

        except Exception as e:
            self.logger.error(f"Error checking health of {component_id}: {e}")
            component_health = self.component_health[component_id]
            component_health.failure_count += 1
            component_health.status = ComponentStatus.FAILED

    def get_component_health(self, component_id: str) -> Optional[ComponentHealth]:
        """Get health status of component"""
        return self.component_health.get(component_id)

    def get_all_health(self) -> Dict[str, ComponentHealth]:
        """Get health status of all components"""
        return self.component_health.copy()

    def is_component_healthy(self, component_id: str) -> bool:
        """Check if component is healthy"""
        health = self.get_component_health(component_id)
        return health and health.status == ComponentStatus.HEALTHY

class SystemReliability:
    """System reliability and failover coordination"""

    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379):
        self.health_checker = HealthChecker()
        self.component_instances: Dict[str, List[str]] = defaultdict(list)
        self.active_instances: Dict[str, str] = {}
        self.failover_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        self.failover_history: deque = deque(maxlen=1000)

        # Redis for coordination
        try:
            self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
            self.redis_available = True
        except:
            self.redis_available = False
            self.logger.warning("Redis not available, using local coordination only")

        # Reliability settings
        self.settings = {
            'max_failover_time_ms': 5000,
            'cascade_failure_threshold': 3,
            'auto_recovery_enabled': True,
            'recovery_delay_seconds': 30
        }

        self.logger = logging.getLogger("SystemReliability")

    async def initialize(self):
        """Initialize reliability system"""
        try:
            # Start health checking
            asyncio.create_task(self.health_checker.start())

            # Register system health checks
            await self._register_system_health_checks()

            # Start reliability monitoring
            asyncio.create_task(self._reliability_monitor())

            self.logger.info("System reliability initialized")

        except Exception as e:
            self.logger.error(f"Error initializing reliability system: {e}")

    async def register_component(self,
                               component_id: str,
                               instances: List[str],
                               health_check: Callable,
                               failover_callback: Optional[Callable] = None):
        """Register component with multiple instances for failover"""
        try:
            # Register instances
            self.component_instances[component_id] = instances
            self.active_instances[component_id] = instances[0] if instances else None

            # Register health check
            self.health_checker.register_component(component_id, health_check)

            # Register failover callback
            if failover_callback:
                self.failover_callbacks[component_id].append(failover_callback)

            self.logger.info(f"Registered component {component_id} with {len(instances)} instances")

        except Exception as e:
            self.logger.error(f"Error registering component {component_id}: {e}")

    async def trigger_failover(self,
                             component_id: str,
                             trigger: FailoverTrigger,
                             force: bool = False) -> bool:
        """Trigger failover for component"""
        try:
            if component_id not in self.component_instances:
                self.logger.error(f"Component {component_id} not registered")
                return False

            instances = self.component_instances[component_id]
            current_active = self.active_instances.get(component_id)

            if not current_active or len(instances) < 2:
                self.logger.error(f"No backup instances available for {component_id}")
                return False

            # Select next instance
            current_index = instances.index(current_active)
            next_index = (current_index + 1) % len(instances)
            backup_instance = instances[next_index]

            # Record failover start
            failover_start = time.time()
            event_id = f"{component_id}_{int(failover_start)}"

            self.logger.info(f"Starting failover for {component_id}: {current_active} -> {backup_instance}")

            # Execute failover
            success = await self._execute_failover(component_id, current_active, backup_instance)

            # Record failover completion
            failover_duration = int((time.time() - failover_start) * 1000)

            failover_event = FailoverEvent(
                event_id=event_id,
                timestamp=datetime.now(),
                component_id=component_id,
                trigger=trigger,
                primary_instance=current_active,
                backup_instance=backup_instance,
                success=success,
                duration_ms=failover_duration
            )

            self.failover_history.append(failover_event)

            if success:
                self.active_instances[component_id] = backup_instance
                self.logger.info(f"Failover completed for {component_id} in {failover_duration}ms")

                # Notify via Redis
                if self.redis_available:
                    self.redis_client.publish(
                        f"failover:{component_id}",
                        json.dumps({
                            'active_instance': backup_instance,
                            'timestamp': datetime.now().isoformat()
                        })
                    )
            else:
                self.logger.error(f"Failover failed for {component_id}")

            return success

        except Exception as e:
            self.logger.error(f"Error during failover for {component_id}: {e}")
            return False

    async def _execute_failover(self, component_id: str, primary: str, backup: str) -> bool:
        """Execute the actual failover process"""
        try:
            # Notify callbacks
            for callback in self.failover_callbacks[component_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(component_id, primary, backup)
                    else:
                        callback(component_id, primary, backup)
                except Exception as e:
                    self.logger.error(f"Error in failover callback: {e}")

            # Component-specific failover logic would be implemented here
            # For now, assume successful failover
            return True

        except Exception as e:
            self.logger.error(f"Error executing failover: {e}")
            return False

    async def _register_system_health_checks(self):
        """Register health checks for core system components"""
        # Database health check
        def db_health_check():
            try:
                # This would check actual database connection
                return True
            except:
                return False

        # Redis health check
        def redis_health_check():
            try:
                if self.redis_available:
                    self.redis_client.ping()
                    return True
                return False
            except:
                return False

        # System resource health check
        def system_health_check():
            try:
                cpu_usage = psutil.cpu_percent()
                memory_usage = psutil.virtual_memory().percent
                disk_usage = psutil.disk_usage('/').percent

                return (cpu_usage < 90 and
                       memory_usage < 90 and
                       disk_usage < 95)
            except:
                return False

        # Network connectivity check
        def network_health_check():
            try:
                # Check connectivity to external services
                socket.create_connection(("8.8.8.8", 53), 3)
                return True
            except:
                return False

        # Register health checks
        self.health_checker.register_component("database", db_health_check)
        self.health_checker.register_component("redis", redis_health_check)
        self.health_checker.register_component("system_resources", system_health_check)
        self.health_checker.register_component("network", network_health_check)

    async def _reliability_monitor(self):
        """Monitor system reliability and trigger automatic actions"""
        while True:
            try:
                await self._check_cascade_failures()
                await self._check_recovery_opportunities()
                await self._update_reliability_metrics()

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Error in reliability monitor: {e}")
                await asyncio.sleep(30)

    async def _check_cascade_failures(self):
        """Check for cascade failure conditions"""
        try:
            # Count failed components
            all_health = self.health_checker.get_all_health()
            failed_components = [
                comp_id for comp_id, health in all_health.items()
                if health.status == ComponentStatus.FAILED
            ]

            if len(failed_components) >= self.settings['cascade_failure_threshold']:
                self.logger.critical(f"CASCADE FAILURE DETECTED: {len(failed_components)} components failed")

                # Take emergency actions
                await self._handle_cascade_failure(failed_components)

        except Exception as e:
            self.logger.error(f"Error checking cascade failures: {e}")

    async def _check_recovery_opportunities(self):
        """Check for automatic recovery opportunities"""
        if not self.settings['auto_recovery_enabled']:
            return

        try:
            all_health = self.health_checker.get_all_health()

            for comp_id, health in all_health.items():
                if (health.status == ComponentStatus.FAILED and
                    health.recovery_attempts < 3 and
                    (datetime.now() - health.last_check).total_seconds() > self.settings['recovery_delay_seconds']):

                    self.logger.info(f"Attempting automatic recovery for {comp_id}")
                    await self._attempt_recovery(comp_id)

        except Exception as e:
            self.logger.error(f"Error checking recovery opportunities: {e}")

    async def _handle_cascade_failure(self, failed_components: List[str]):
        """Handle cascade failure scenario"""
        try:
            # Emergency protocols
            self.logger.critical("EXECUTING EMERGENCY PROTOCOLS")

            # Disable automatic trading
            if self.redis_available:
                self.redis_client.set("emergency_stop", "true", ex=3600)

            # Notify all systems
            for callback_list in self.failover_callbacks.values():
                for callback in callback_list:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback("CASCADE_FAILURE", None, None)
                        else:
                            callback("CASCADE_FAILURE", None, None)
                    except Exception as e:
                        self.logger.error(f"Error in cascade failure callback: {e}")

        except Exception as e:
            self.logger.error(f"Error handling cascade failure: {e}")

    async def _attempt_recovery(self, component_id: str):
        """Attempt to recover a failed component"""
        try:
            health = self.health_checker.get_component_health(component_id)
            if not health:
                return

            health.recovery_attempts += 1
            health.status = ComponentStatus.RECOVERING

            # Component-specific recovery logic would go here
            # For now, just wait and recheck
            await asyncio.sleep(5)

            # Force health check
            await self.health_checker._check_component_health(component_id)

            updated_health = self.health_checker.get_component_health(component_id)
            if updated_health and updated_health.status == ComponentStatus.HEALTHY:
                self.logger.info(f"Successfully recovered component {component_id}")
            else:
                self.logger.warning(f"Recovery attempt failed for {component_id}")

        except Exception as e:
            self.logger.error(f"Error attempting recovery for {component_id}: {e}")

    async def _update_reliability_metrics(self):
        """Update system reliability metrics"""
        try:
            all_health = self.health_checker.get_all_health()

            total_components = len(all_health)
            healthy_components = sum(1 for h in all_health.values() if h.status == ComponentStatus.HEALTHY)

            system_health_percentage = (healthy_components / total_components * 100) if total_components > 0 else 0

            # Store metrics
            if self.redis_available:
                metrics = {
                    'system_health_percentage': system_health_percentage,
                    'total_components': total_components,
                    'healthy_components': healthy_components,
                    'failed_components': sum(1 for h in all_health.values() if h.status == ComponentStatus.FAILED),
                    'timestamp': datetime.now().isoformat()
                }

                self.redis_client.set("system_reliability_metrics", json.dumps(metrics), ex=300)

        except Exception as e:
            self.logger.error(f"Error updating reliability metrics: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        all_health = self.health_checker.get_all_health()

        return {
            'overall_status': self._calculate_overall_status(all_health),
            'component_health': {
                comp_id: {
                    'status': health.status.value,
                    'last_check': health.last_check.isoformat(),
                    'failure_count': health.failure_count,
                    'recovery_attempts': health.recovery_attempts
                }
                for comp_id, health in all_health.items()
            },
            'active_instances': self.active_instances,
            'recent_failovers': [
                {
                    'component_id': event.component_id,
                    'timestamp': event.timestamp.isoformat(),
                    'success': event.success,
                    'duration_ms': event.duration_ms
                }
                for event in list(self.failover_history)[-10:]
            ],
            'settings': self.settings
        }

    def _calculate_overall_status(self, all_health: Dict[str, ComponentHealth]) -> str:
        """Calculate overall system status"""
        if not all_health:
            return "unknown"

        statuses = [health.status for health in all_health.values()]

        if ComponentStatus.FAILED in statuses:
            failed_count = statuses.count(ComponentStatus.FAILED)
            if failed_count >= len(statuses) * 0.5:
                return "critical"
            else:
                return "degraded"
        elif ComponentStatus.FAILING in statuses or ComponentStatus.DEGRADED in statuses:
            return "warning"
        else:
            return "healthy"

class FailoverManager:
    """Main failover management interface"""

    def __init__(self):
        self.system_reliability = SystemReliability()
        self.is_initialized = False

    async def initialize(self):
        """Initialize failover manager"""
        await self.system_reliability.initialize()
        self.is_initialized = True

    async def register_service(self, service_name: str, instances: List[str], health_check: Callable):
        """Register a service for failover management"""
        if not self.is_initialized:
            await self.initialize()

        await self.system_reliability.register_component(service_name, instances, health_check)

    async def trigger_failover(self, service_name: str, reason: str = "manual") -> bool:
        """Trigger failover for a service"""
        trigger = FailoverTrigger.MANUAL_TRIGGER
        return await self.system_reliability.trigger_failover(service_name, trigger)

    def get_status(self) -> Dict[str, Any]:
        """Get system status"""
        return self.system_reliability.get_system_status()

    async def cleanup(self):
        """Cleanup resources"""
        await self.system_reliability.health_checker.stop()