"""
Enhanced Real-Time Data Feed Configuration and Manager
Integrates multiple data providers with failover and redundancy
"""

import asyncio
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging
from .market_data_feed import RealTimeMarketData, MarketDataConfig

@dataclass
class DataProviderConfig:
    """Configuration for a data provider"""
    name: str
    api_key: str
    enabled: bool = True
    priority: int = 1  # 1 = highest priority
    rate_limit: int = 60
    supports_websocket: bool = True
    symbols_limit: int = 100
    backup_mode: bool = False

class EnhancedDataFeedManager:
    """Enhanced data feed manager with redundancy and failover"""

    def __init__(self):
        self.providers: Dict[str, DataProviderConfig] = {}
        self.market_data = None
        self.active_symbols = set()
        self.provider_health = {}
        self.failover_enabled = True
        self.logger = logging.getLogger("EnhancedDataFeedManager")

        # Load configuration from environment
        self._load_provider_configs()

    def _load_provider_configs(self):
        """Load provider configurations from environment variables"""

        # Alpha Vantage - Free tier: 25 requests/day, Premium: up to 1200/min
        if os.getenv('ALPHA_VANTAGE_API_KEY'):
            self.providers['alpha_vantage'] = DataProviderConfig(
                name='alpha_vantage',
                api_key=os.getenv('ALPHA_VANTAGE_API_KEY'),
                enabled=True,
                priority=3,
                rate_limit=5,  # Free tier limit
                supports_websocket=False,
                symbols_limit=100
            )

        # IEX Cloud - Flexible pricing
        if os.getenv('IEX_CLOUD_API_KEY'):
            self.providers['iex_cloud'] = DataProviderConfig(
                name='iex_cloud',
                api_key=os.getenv('IEX_CLOUD_API_KEY'),
                enabled=True,
                priority=2,
                rate_limit=100,
                supports_websocket=False,
                symbols_limit=100
            )

        # Finnhub - Free: 60 calls/min, paid plans available
        if os.getenv('FINNHUB_API_KEY'):
            self.providers['finnhub'] = DataProviderConfig(
                name='finnhub',
                api_key=os.getenv('FINNHUB_API_KEY'),
                enabled=True,
                priority=1,
                rate_limit=60,
                supports_websocket=True,
                symbols_limit=50
            )

        # Polygon.io - Various plans, WebSocket support
        if os.getenv('POLYGON_API_KEY'):
            self.providers['polygon'] = DataProviderConfig(
                name='polygon',
                api_key=os.getenv('POLYGON_API_KEY'),
                enabled=True,
                priority=1,
                rate_limit=1000,
                supports_websocket=True,
                symbols_limit=100
            )

        # Binance for crypto (free)
        self.providers['binance'] = DataProviderConfig(
            name='binance',
            api_key='',  # Public endpoint
            enabled=True,
            priority=1,
            rate_limit=1200,
            supports_websocket=True,
            symbols_limit=50
        )

        # Yahoo Finance (via yfinance - free but rate limited)
        self.providers['yahoo_finance'] = DataProviderConfig(
            name='yahoo_finance',
            api_key='',  # No API key needed
            enabled=True,
            priority=4,  # Backup only
            rate_limit=2000,  # Per hour
            supports_websocket=False,
            symbols_limit=200,
            backup_mode=True
        )

    async def initialize(self) -> bool:
        """Initialize the enhanced data feed manager"""
        try:
            self.market_data = RealTimeMarketData()

            # Add available providers in priority order
            sorted_providers = sorted(
                [(name, config) for name, config in self.providers.items() if config.enabled],
                key=lambda x: x[1].priority
            )

            success_count = 0
            for provider_name, config in sorted_providers:
                try:
                    provider_config = {
                        'api_key': config.api_key,
                        'rate_limit': config.rate_limit,
                        'supports_websocket': config.supports_websocket
                    }

                    await self.market_data.add_provider(provider_name, provider_config)
                    self.provider_health[provider_name] = 'healthy'
                    success_count += 1

                    self.logger.info(f"Successfully added provider: {provider_name}")

                except Exception as e:
                    self.logger.error(f"Failed to add provider {provider_name}: {e}")
                    self.provider_health[provider_name] = 'failed'

            if success_count == 0:
                self.logger.error("No data providers successfully initialized")
                return False

            self.logger.info(f"Initialized with {success_count} data providers")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing data feed manager: {e}")
            return False

    async def subscribe_to_symbols(self, symbols: List[str]) -> bool:
        """Subscribe to real-time data for symbols with intelligent provider selection"""
        try:
            self.active_symbols.update(symbols)

            # Group symbols by optimal provider
            provider_assignments = self._assign_symbols_to_providers(symbols)

            for provider_name, provider_symbols in provider_assignments.items():
                if provider_symbols and provider_name in self.providers:
                    try:
                        await self.market_data.subscribe_symbols(provider_symbols, provider_name)
                        self.logger.info(f"Subscribed {len(provider_symbols)} symbols to {provider_name}")
                    except Exception as e:
                        self.logger.error(f"Failed to subscribe symbols to {provider_name}: {e}")
                        # Try fallback provider
                        await self._handle_provider_failure(provider_name, provider_symbols)

            return True

        except Exception as e:
            self.logger.error(f"Error subscribing to symbols: {e}")
            return False

    def _assign_symbols_to_providers(self, symbols: List[str]) -> Dict[str, List[str]]:
        """Intelligently assign symbols to providers based on capabilities and health"""
        assignment = {}

        # Get healthy providers sorted by priority
        healthy_providers = [
            (name, config) for name, config in self.providers.items()
            if config.enabled and self.provider_health.get(name) == 'healthy'
        ]
        healthy_providers.sort(key=lambda x: x[1].priority)

        if not healthy_providers:
            self.logger.warning("No healthy providers available")
            return assignment

        # Assign symbols to providers based on type and capabilities
        crypto_symbols = [s for s in symbols if any(pair in s.upper() for pair in ['BTC', 'ETH', 'USD', '/'])]
        stock_symbols = [s for s in symbols if s not in crypto_symbols]

        # Assign crypto symbols to Binance first
        if crypto_symbols and 'binance' in [p[0] for p in healthy_providers]:
            assignment['binance'] = crypto_symbols

        # Assign stock symbols to best available provider
        if stock_symbols:
            for provider_name, config in healthy_providers:
                if provider_name != 'binance' and not config.backup_mode:
                    # Respect symbol limits
                    symbols_to_assign = stock_symbols[:config.symbols_limit]
                    if symbols_to_assign:
                        assignment[provider_name] = symbols_to_assign
                        stock_symbols = stock_symbols[len(symbols_to_assign):]

                    if not stock_symbols:
                        break

        # If we still have unassigned symbols, use backup providers
        if stock_symbols:
            for provider_name, config in healthy_providers:
                if config.backup_mode:
                    assignment[provider_name] = stock_symbols
                    break

        return assignment

    async def _handle_provider_failure(self, failed_provider: str, symbols: List[str]):
        """Handle provider failure by switching to backup"""
        try:
            self.provider_health[failed_provider] = 'failed'
            self.logger.warning(f"Provider {failed_provider} failed, attempting failover")

            if not self.failover_enabled:
                return

            # Find backup providers
            backup_providers = [
                (name, config) for name, config in self.providers.items()
                if (config.enabled and
                    name != failed_provider and
                    self.provider_health.get(name) == 'healthy')
            ]

            if backup_providers:
                # Use highest priority backup
                backup_providers.sort(key=lambda x: x[1].priority)
                backup_name = backup_providers[0][0]

                try:
                    await self.market_data.subscribe_symbols(symbols, backup_name)
                    self.logger.info(f"Successfully failed over to {backup_name}")
                except Exception as e:
                    self.logger.error(f"Failover to {backup_name} also failed: {e}")
            else:
                self.logger.error("No backup providers available for failover")

        except Exception as e:
            self.logger.error(f"Error handling provider failure: {e}")

    async def start_data_feed(self) -> bool:
        """Start the real-time data feed"""
        try:
            if not self.market_data:
                await self.initialize()

            await self.market_data.start()
            self.logger.info("Real-time data feed started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error starting data feed: {e}")
            return False

    async def stop_data_feed(self):
        """Stop the real-time data feed"""
        try:
            if self.market_data:
                await self.market_data.stop()
            self.logger.info("Real-time data feed stopped")

        except Exception as e:
            self.logger.error(f"Error stopping data feed: {e}")

    def add_data_callback(self, callback):
        """Add callback for real-time data"""
        if self.market_data:
            self.market_data.add_subscriber(callback)

    def get_provider_status(self) -> Dict[str, Any]:
        """Get status of all providers"""
        status = {}
        for name, config in self.providers.items():
            status[name] = {
                'enabled': config.enabled,
                'priority': config.priority,
                'health': self.provider_health.get(name, 'unknown'),
                'rate_limit': config.rate_limit,
                'supports_websocket': config.supports_websocket,
                'backup_mode': config.backup_mode
            }
        return status

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        if self.market_data:
            return self.market_data.get_performance_stats()
        return {}

    def get_latest_data(self, symbol: str):
        """Get latest data for symbol"""
        if self.market_data:
            return self.market_data.get_latest_tick(symbol)
        return None

# Example usage and configuration helper
class DataFeedConfigBuilder:
    """Helper class to build data feed configurations"""

    @staticmethod
    def get_recommended_setup() -> Dict[str, str]:
        """Get recommended data provider setup"""
        return {
            'primary_stock_data': 'Finnhub or Polygon (paid plans for high frequency)',
            'backup_stock_data': 'Alpha Vantage, IEX Cloud',
            'crypto_data': 'Binance (free, excellent WebSocket)',
            'fundamental_data': 'Alpha Vantage, IEX Cloud',
            'backup_free': 'Yahoo Finance (rate limited)'
        }

    @staticmethod
    def get_cost_analysis() -> Dict[str, str]:
        """Get cost analysis for different providers"""
        return {
            'free_tier': {
                'providers': ['Binance (crypto)', 'Yahoo Finance', 'Alpha Vantage (25 req/day)'],
                'limitations': 'Very limited for stock data, good for crypto',
                'cost': '$0/month'
            },
            'budget_tier': {
                'providers': ['Finnhub Basic ($19/month)', 'Alpha Vantage Premium ($49/month)'],
                'capabilities': 'Real-time stocks, decent rate limits',
                'cost': '$20-50/month'
            },
            'professional_tier': {
                'providers': ['Polygon Professional ($399/month)', 'IEX Cloud'],
                'capabilities': 'High-frequency real-time, unlimited symbols',
                'cost': '$400+/month'
            }
        }

    @staticmethod
    def create_sample_env_file() -> str:
        """Create sample .env file with API key placeholders"""
        return """
# Data Provider API Keys
# Obtain these from respective provider websites

# Finnhub (finnhub.io) - Free: 60 calls/min, Paid: $19+/month
FINNHUB_API_KEY=your_finnhub_api_key_here

# Alpha Vantage (alphavantage.co) - Free: 25 requests/day, Paid: $49+/month
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# IEX Cloud (iexcloud.io) - Pay per use, starts at $9/month
IEX_CLOUD_API_KEY=your_iex_cloud_api_key_here

# Polygon.io - Professional: $399/month for real-time
POLYGON_API_KEY=your_polygon_api_key_here

# Binance is free for crypto data (no API key needed for public data)
# Yahoo Finance is free but rate limited (no API key needed)
"""

# Configuration presets for different use cases
TRADING_CONFIGS = {
    'development': {
        'description': 'Free/low-cost setup for development and backtesting',
        'providers': ['binance', 'yahoo_finance', 'alpha_vantage'],
        'expected_cost': '$0-49/month',
        'limitations': 'Rate limited, delayed stock data'
    },
    'day_trading': {
        'description': 'Real-time setup for day trading',
        'providers': ['finnhub', 'polygon', 'binance'],
        'expected_cost': '$50-400/month',
        'limitations': 'Professional grade, real-time data'
    },
    'high_frequency': {
        'description': 'High-frequency trading setup',
        'providers': ['polygon', 'iex_cloud', 'binance'],
        'expected_cost': '$400+/month',
        'limitations': 'Institutional grade, microsecond latency'
    }
}