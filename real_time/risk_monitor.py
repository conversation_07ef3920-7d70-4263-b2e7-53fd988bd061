"""
Real-Time Risk Monitoring and Alert System
Monitors portfolio risk, position limits, drawdowns, and market conditions
"""

import asyncio
import json
import smtplib
import os
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import pandas as pd
import numpy as np
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import redis
import websockets

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertType(Enum):
    """Types of alerts"""
    POSITION_LIMIT = "position_limit"
    PORTFOLIO_RISK = "portfolio_risk"
    DRAWDOWN = "drawdown"
    MARGIN_CALL = "margin_call"
    MARKET_VOLATILITY = "market_volatility"
    CONNECTION_LOSS = "connection_loss"
    SYSTEM_ERROR = "system_error"
    PRICE_MOVEMENT = "price_movement"
    VOLUME_ANOMALY = "volume_anomaly"

@dataclass
class RiskAlert:
    """Risk alert data structure"""
    alert_id: str
    alert_type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    symbol: Optional[str] = None
    current_value: Optional[float] = None
    threshold_value: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    acknowledged: bool = False

@dataclass
class RiskMetrics:
    """Real-time risk metrics"""
    timestamp: datetime
    portfolio_value: float
    total_pnl: float
    daily_pnl: float
    unrealized_pnl: float
    realized_pnl: float
    max_drawdown: float
    current_drawdown: float
    var_95: float  # Value at Risk 95%
    var_99: float  # Value at Risk 99%
    portfolio_beta: float
    sharpe_ratio: float
    volatility: float
    leverage_ratio: float
    margin_usage: float
    buying_power: float

class AlertChannel:
    """Base class for alert delivery channels"""

    async def send_alert(self, alert: RiskAlert) -> bool:
        """Send alert through this channel"""
        raise NotImplementedError

class EmailAlertChannel(AlertChannel):
    """Email alert delivery"""

    def __init__(self, config: Dict[str, str]):
        self.smtp_server = config.get('smtp_server', 'smtp.gmail.com')
        self.smtp_port = int(config.get('smtp_port', 587))
        self.username = config.get('username')
        self.password = config.get('password')
        self.from_email = config.get('from_email')
        self.to_emails = config.get('to_emails', [])
        self.logger = logging.getLogger("EmailAlertChannel")

    async def send_alert(self, alert: RiskAlert) -> bool:
        try:
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.title}"

            # Create HTML body
            html_body = f"""
            <html>
                <body>
                    <h2>Trading System Alert</h2>
                    <table border="1" cellpadding="5">
                        <tr><td><b>Alert Type</b></td><td>{alert.alert_type.value}</td></tr>
                        <tr><td><b>Severity</b></td><td>{alert.severity.value.upper()}</td></tr>
                        <tr><td><b>Time</b></td><td>{alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</td></tr>
                        <tr><td><b>Symbol</b></td><td>{alert.symbol or 'N/A'}</td></tr>
                        <tr><td><b>Current Value</b></td><td>{alert.current_value}</td></tr>
                        <tr><td><b>Threshold</b></td><td>{alert.threshold_value}</td></tr>
                    </table>
                    <br>
                    <p><b>Message:</b></p>
                    <p>{alert.message}</p>
                    <br>
                    <p><i>This is an automated alert from your trading system.</i></p>
                </body>
            </html>
            """

            msg.attach(MIMEText(html_body, 'html'))

            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)

            self.logger.info(f"Email alert sent: {alert.alert_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")
            return False

class SlackAlertChannel(AlertChannel):
    """Slack alert delivery"""

    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
        self.logger = logging.getLogger("SlackAlertChannel")

    async def send_alert(self, alert: RiskAlert) -> bool:
        try:
            import aiohttp

            # Choose color based on severity
            color_map = {
                AlertSeverity.INFO: "#36a64f",      # Green
                AlertSeverity.WARNING: "#ffaa00",   # Orange
                AlertSeverity.ERROR: "#ff0000",     # Red
                AlertSeverity.CRITICAL: "#800000"   # Dark Red
            }

            slack_payload = {
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, "#808080"),
                        "title": f"{alert.severity.value.upper()}: {alert.title}",
                        "text": alert.message,
                        "fields": [
                            {
                                "title": "Alert Type",
                                "value": alert.alert_type.value,
                                "short": True
                            },
                            {
                                "title": "Symbol",
                                "value": alert.symbol or "N/A",
                                "short": True
                            },
                            {
                                "title": "Current Value",
                                "value": str(alert.current_value) if alert.current_value else "N/A",
                                "short": True
                            },
                            {
                                "title": "Threshold",
                                "value": str(alert.threshold_value) if alert.threshold_value else "N/A",
                                "short": True
                            }
                        ],
                        "timestamp": int(alert.timestamp.timestamp())
                    }
                ]
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(self.webhook_url, json=slack_payload) as response:
                    success = response.status == 200
                    if success:
                        self.logger.info(f"Slack alert sent: {alert.alert_id}")
                    else:
                        self.logger.error(f"Slack alert failed: {response.status}")
                    return success

        except Exception as e:
            self.logger.error(f"Failed to send Slack alert: {e}")
            return False

class WebSocketAlertChannel(AlertChannel):
    """WebSocket alert delivery for real-time UI updates"""

    def __init__(self, websocket_url: str):
        self.websocket_url = websocket_url
        self.connected_clients = set()
        self.logger = logging.getLogger("WebSocketAlertChannel")

    async def send_alert(self, alert: RiskAlert) -> bool:
        try:
            alert_data = {
                'type': 'risk_alert',
                'alert_id': alert.alert_id,
                'alert_type': alert.alert_type.value,
                'severity': alert.severity.value,
                'title': alert.title,
                'message': alert.message,
                'symbol': alert.symbol,
                'current_value': alert.current_value,
                'threshold_value': alert.threshold_value,
                'timestamp': alert.timestamp.isoformat(),
                'metadata': alert.metadata
            }

            # Send to all connected clients
            if self.connected_clients:
                disconnected = set()
                for websocket in self.connected_clients:
                    try:
                        await websocket.send(json.dumps(alert_data))
                    except Exception:
                        disconnected.add(websocket)

                # Remove disconnected clients
                self.connected_clients -= disconnected

            return True

        except Exception as e:
            self.logger.error(f"Failed to send WebSocket alert: {e}")
            return False

    async def add_client(self, websocket):
        """Add WebSocket client"""
        self.connected_clients.add(websocket)

    async def remove_client(self, websocket):
        """Remove WebSocket client"""
        self.connected_clients.discard(websocket)

class RealTimeRiskMonitor:
    """Real-time risk monitoring system"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.alert_channels: List[AlertChannel] = []
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.risk_thresholds = config.get('risk_thresholds', {})
        self.monitoring_enabled = True
        self.position_monitor = None
        self.portfolio_monitor = None

        # Redis for real-time data
        redis_url = config.get('redis_url', 'redis://localhost:6379/0')
        self.redis_client = redis.from_url(redis_url, decode_responses=True)

        # Risk metrics storage
        self.risk_history: List[RiskMetrics] = []
        self.max_history_length = config.get('max_history_length', 1000)

        # Alert throttling
        self.alert_throttle = {}
        self.throttle_duration = timedelta(minutes=5)

        self.logger = logging.getLogger("RealTimeRiskMonitor")

    def add_alert_channel(self, channel: AlertChannel):
        """Add alert delivery channel"""
        self.alert_channels.append(channel)

    def setup_email_alerts(self, email_config: Dict[str, str]):
        """Setup email alert channel"""
        email_channel = EmailAlertChannel(email_config)
        self.add_alert_channel(email_channel)

    def setup_slack_alerts(self, webhook_url: str):
        """Setup Slack alert channel"""
        slack_channel = SlackAlertChannel(webhook_url)
        self.add_alert_channel(slack_channel)

    def setup_websocket_alerts(self, websocket_url: str):
        """Setup WebSocket alert channel"""
        ws_channel = WebSocketAlertChannel(websocket_url)
        self.add_alert_channel(ws_channel)
        return ws_channel

    async def start_monitoring(self):
        """Start real-time risk monitoring"""
        try:
            self.monitoring_enabled = True

            # Start monitoring tasks
            tasks = [
                asyncio.create_task(self._monitor_portfolio_risk()),
                asyncio.create_task(self._monitor_position_limits()),
                asyncio.create_task(self._monitor_drawdowns()),
                asyncio.create_task(self._monitor_market_conditions()),
                asyncio.create_task(self._monitor_system_health()),
                asyncio.create_task(self._calculate_risk_metrics()),
                asyncio.create_task(self._cleanup_old_alerts())
            ]

            await asyncio.gather(*tasks)

        except Exception as e:
            self.logger.error(f"Error in risk monitoring: {e}")
            self.monitoring_enabled = False

    async def stop_monitoring(self):
        """Stop risk monitoring"""
        self.monitoring_enabled = False
        self.logger.info("Risk monitoring stopped")

    async def _monitor_portfolio_risk(self):
        """Monitor overall portfolio risk metrics"""
        while self.monitoring_enabled:
            try:
                # Get current portfolio data
                portfolio_data = await self._get_portfolio_data()

                if portfolio_data:
                    # Check portfolio-level thresholds
                    await self._check_portfolio_thresholds(portfolio_data)

                await asyncio.sleep(1)  # Check every second

            except Exception as e:
                self.logger.error(f"Error monitoring portfolio risk: {e}")
                await asyncio.sleep(5)

    async def _monitor_position_limits(self):
        """Monitor individual position limits"""
        while self.monitoring_enabled:
            try:
                # Get current positions
                positions = await self._get_current_positions()

                for symbol, position_data in positions.items():
                    await self._check_position_limits(symbol, position_data)

                await asyncio.sleep(1)

            except Exception as e:
                self.logger.error(f"Error monitoring position limits: {e}")
                await asyncio.sleep(5)

    async def _monitor_drawdowns(self):
        """Monitor portfolio drawdowns"""
        while self.monitoring_enabled:
            try:
                # Calculate current drawdown
                current_value = await self._get_portfolio_value()
                high_water_mark = await self._get_high_water_mark()

                if current_value and high_water_mark:
                    drawdown = (high_water_mark - current_value) / high_water_mark

                    # Check drawdown thresholds
                    max_drawdown = self.risk_thresholds.get('max_drawdown', 0.15)
                    warning_drawdown = self.risk_thresholds.get('warning_drawdown', 0.10)

                    if drawdown >= max_drawdown:
                        await self._create_alert(
                            AlertType.DRAWDOWN,
                            AlertSeverity.CRITICAL,
                            "Maximum Drawdown Exceeded",
                            f"Portfolio drawdown of {drawdown:.2%} exceeds maximum limit of {max_drawdown:.2%}",
                            current_value=drawdown,
                            threshold_value=max_drawdown
                        )
                    elif drawdown >= warning_drawdown:
                        await self._create_alert(
                            AlertType.DRAWDOWN,
                            AlertSeverity.WARNING,
                            "Drawdown Warning",
                            f"Portfolio drawdown of {drawdown:.2%} approaching limit",
                            current_value=drawdown,
                            threshold_value=warning_drawdown
                        )

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Error monitoring drawdowns: {e}")
                await asyncio.sleep(30)

    async def _monitor_market_conditions(self):
        """Monitor market volatility and conditions"""
        while self.monitoring_enabled:
            try:
                # Monitor VIX and market volatility
                market_data = await self._get_market_data()

                if market_data:
                    vix = market_data.get('vix')
                    if vix:
                        vix_threshold = self.risk_thresholds.get('vix_threshold', 30)

                        if vix > vix_threshold:
                            await self._create_alert(
                                AlertType.MARKET_VOLATILITY,
                                AlertSeverity.WARNING,
                                "High Market Volatility",
                                f"VIX at {vix:.2f}, above threshold of {vix_threshold}",
                                current_value=vix,
                                threshold_value=vix_threshold
                            )

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Error monitoring market conditions: {e}")
                await asyncio.sleep(60)

    async def _monitor_system_health(self):
        """Monitor system connectivity and health"""
        while self.monitoring_enabled:
            try:
                # Check data feed connections
                data_feed_status = await self._check_data_feed_health()
                broker_status = await self._check_broker_health()

                # Alert on connection issues
                for service, is_healthy in data_feed_status.items():
                    if not is_healthy:
                        await self._create_alert(
                            AlertType.CONNECTION_LOSS,
                            AlertSeverity.ERROR,
                            f"Data Feed Connection Lost",
                            f"Lost connection to {service} data feed",
                            metadata={'service': service}
                        )

                for broker, is_healthy in broker_status.items():
                    if not is_healthy:
                        await self._create_alert(
                            AlertType.CONNECTION_LOSS,
                            AlertSeverity.CRITICAL,
                            f"Broker Connection Lost",
                            f"Lost connection to {broker} broker",
                            metadata={'broker': broker}
                        )

                await asyncio.sleep(30)

            except Exception as e:
                self.logger.error(f"Error monitoring system health: {e}")
                await asyncio.sleep(30)

    async def _calculate_risk_metrics(self):
        """Calculate and store risk metrics"""
        while self.monitoring_enabled:
            try:
                # Get portfolio data
                portfolio_value = await self._get_portfolio_value()
                positions = await self._get_current_positions()
                pnl_data = await self._get_pnl_data()

                if portfolio_value and positions:
                    # Calculate risk metrics
                    metrics = await self._compute_risk_metrics(
                        portfolio_value, positions, pnl_data
                    )

                    # Store metrics
                    self.risk_history.append(metrics)

                    # Trim history
                    if len(self.risk_history) > self.max_history_length:
                        self.risk_history = self.risk_history[-self.max_history_length:]

                    # Publish to Redis
                    await self._publish_risk_metrics(metrics)

                await asyncio.sleep(5)  # Calculate every 5 seconds

            except Exception as e:
                self.logger.error(f"Error calculating risk metrics: {e}")
                await asyncio.sleep(5)

    async def _cleanup_old_alerts(self):
        """Clean up old acknowledged alerts"""
        while self.monitoring_enabled:
            try:
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(hours=24)

                # Remove old acknowledged alerts
                to_remove = []
                for alert_id, alert in self.active_alerts.items():
                    if alert.acknowledged and alert.timestamp < cutoff_time:
                        to_remove.append(alert_id)

                for alert_id in to_remove:
                    del self.active_alerts[alert_id]

                await asyncio.sleep(3600)  # Clean up every hour

            except Exception as e:
                self.logger.error(f"Error cleaning up alerts: {e}")
                await asyncio.sleep(3600)

    async def _create_alert(self, alert_type: AlertType, severity: AlertSeverity,
                           title: str, message: str, symbol: str = None,
                           current_value: float = None, threshold_value: float = None,
                           metadata: Dict[str, Any] = None):
        """Create and send an alert"""
        try:
            # Check throttling
            throttle_key = f"{alert_type.value}_{symbol or 'global'}"
            current_time = datetime.now()

            if throttle_key in self.alert_throttle:
                last_alert_time = self.alert_throttle[throttle_key]
                if current_time - last_alert_time < self.throttle_duration:
                    return  # Skip throttled alert

            # Create alert
            alert_id = f"{alert_type.value}_{int(current_time.timestamp())}"
            alert = RiskAlert(
                alert_id=alert_id,
                alert_type=alert_type,
                severity=severity,
                title=title,
                message=message,
                symbol=symbol,
                current_value=current_value,
                threshold_value=threshold_value,
                metadata=metadata or {}
            )

            # Store alert
            self.active_alerts[alert_id] = alert

            # Update throttle
            self.alert_throttle[throttle_key] = current_time

            # Send through all channels
            for channel in self.alert_channels:
                try:
                    await channel.send_alert(alert)
                except Exception as e:
                    self.logger.error(f"Error sending alert through channel: {e}")

            self.logger.info(f"Alert created: {alert_id} - {title}")

        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")

    async def _get_portfolio_data(self) -> Dict[str, Any]:
        """Get current portfolio data"""
        try:
            # This would fetch from your portfolio tracking system
            # For now, return simulated data
            return {
                'total_value': 100000,
                'cash': 20000,
                'equity': 80000,
                'margin_used': 40000,
                'buying_power': 160000
            }
        except Exception as e:
            self.logger.error(f"Error getting portfolio data: {e}")
            return {}

    async def _get_current_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get current positions"""
        try:
            # This would fetch from your position tracking system
            return {}
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return {}

    async def _check_portfolio_thresholds(self, portfolio_data: Dict[str, Any]):
        """Check portfolio-level risk thresholds"""
        try:
            # Check leverage ratio
            if 'margin_used' in portfolio_data and 'equity' in portfolio_data:
                leverage_ratio = portfolio_data['margin_used'] / portfolio_data['equity']
                max_leverage = self.risk_thresholds.get('max_leverage', 2.0)

                if leverage_ratio > max_leverage:
                    await self._create_alert(
                        AlertType.PORTFOLIO_RISK,
                        AlertSeverity.WARNING,
                        "High Leverage Ratio",
                        f"Current leverage ratio {leverage_ratio:.2f} exceeds limit of {max_leverage:.2f}",
                        current_value=leverage_ratio,
                        threshold_value=max_leverage
                    )

        except Exception as e:
            self.logger.error(f"Error checking portfolio thresholds: {e}")

    async def _check_position_limits(self, symbol: str, position_data: Dict[str, Any]):
        """Check individual position limits"""
        try:
            position_size = position_data.get('size', 0)
            position_value = position_data.get('market_value', 0)

            # Check position size limits
            max_position_value = self.risk_thresholds.get('max_position_value', 50000)
            max_position_percent = self.risk_thresholds.get('max_position_percent', 0.1)

            portfolio_value = await self._get_portfolio_value()
            if portfolio_value:
                position_percent = position_value / portfolio_value

                if position_percent > max_position_percent:
                    await self._create_alert(
                        AlertType.POSITION_LIMIT,
                        AlertSeverity.WARNING,
                        "Position Size Limit Exceeded",
                        f"Position in {symbol} ({position_percent:.2%}) exceeds limit of {max_position_percent:.2%}",
                        symbol=symbol,
                        current_value=position_percent,
                        threshold_value=max_position_percent
                    )

        except Exception as e:
            self.logger.error(f"Error checking position limits for {symbol}: {e}")

    async def _get_portfolio_value(self) -> float:
        """Get current portfolio value"""
        try:
            portfolio_data = await self._get_portfolio_data()
            return portfolio_data.get('total_value', 0)
        except Exception as e:
            self.logger.error(f"Error getting portfolio value: {e}")
            return 0

    async def _get_high_water_mark(self) -> float:
        """Get portfolio high water mark"""
        try:
            # This would track the highest portfolio value
            # For now, return current value
            return await self._get_portfolio_value()
        except Exception as e:
            self.logger.error(f"Error getting high water mark: {e}")
            return 0

    async def _get_market_data(self) -> Dict[str, Any]:
        """Get market data including VIX"""
        try:
            # This would fetch from your market data feed
            return {'vix': 20.5}  # Simulated
        except Exception as e:
            self.logger.error(f"Error getting market data: {e}")
            return {}

    async def _check_data_feed_health(self) -> Dict[str, bool]:
        """Check data feed health"""
        try:
            # This would check your data feed connections
            return {'alpha_vantage': True, 'finnhub': True}  # Simulated
        except Exception as e:
            self.logger.error(f"Error checking data feed health: {e}")
            return {}

    async def _check_broker_health(self) -> Dict[str, bool]:
        """Check broker connection health"""
        try:
            # This would check your broker connections
            return {'alpaca': True, 'binance': True}  # Simulated
        except Exception as e:
            self.logger.error(f"Error checking broker health: {e}")
            return {}

    async def _get_pnl_data(self) -> Dict[str, Any]:
        """Get P&L data"""
        try:
            # This would fetch from your P&L tracking system
            return {
                'daily_pnl': 1000,
                'unrealized_pnl': 500,
                'realized_pnl': 500
            }
        except Exception as e:
            self.logger.error(f"Error getting P&L data: {e}")
            return {}

    async def _compute_risk_metrics(self, portfolio_value: float,
                                   positions: Dict[str, Any],
                                   pnl_data: Dict[str, Any]) -> RiskMetrics:
        """Compute comprehensive risk metrics"""
        try:
            return RiskMetrics(
                timestamp=datetime.now(),
                portfolio_value=portfolio_value,
                total_pnl=pnl_data.get('daily_pnl', 0),
                daily_pnl=pnl_data.get('daily_pnl', 0),
                unrealized_pnl=pnl_data.get('unrealized_pnl', 0),
                realized_pnl=pnl_data.get('realized_pnl', 0),
                max_drawdown=0.05,  # Would calculate from historical data
                current_drawdown=0.02,
                var_95=portfolio_value * 0.05,  # 5% VaR
                var_99=portfolio_value * 0.10,  # 10% VaR
                portfolio_beta=1.0,
                sharpe_ratio=1.5,
                volatility=0.20,
                leverage_ratio=1.5,
                margin_usage=0.4,
                buying_power=portfolio_value * 2
            )
        except Exception as e:
            self.logger.error(f"Error computing risk metrics: {e}")
            return None

    async def _publish_risk_metrics(self, metrics: RiskMetrics):
        """Publish risk metrics to Redis"""
        try:
            metrics_data = {
                'timestamp': metrics.timestamp.isoformat(),
                'portfolio_value': metrics.portfolio_value,
                'total_pnl': metrics.total_pnl,
                'current_drawdown': metrics.current_drawdown,
                'var_95': metrics.var_95,
                'sharpe_ratio': metrics.sharpe_ratio,
                'volatility': metrics.volatility
            }

            self.redis_client.publish('risk_metrics', json.dumps(metrics_data))
        except Exception as e:
            self.logger.error(f"Error publishing risk metrics: {e}")

    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].acknowledged = True
            self.logger.info(f"Alert acknowledged: {alert_id}")
            return True
        return False

    def get_active_alerts(self) -> List[RiskAlert]:
        """Get all active (unacknowledged) alerts"""
        return [alert for alert in self.active_alerts.values() if not alert.acknowledged]

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get current risk summary"""
        if not self.risk_history:
            return {}

        latest_metrics = self.risk_history[-1]
        active_alerts = self.get_active_alerts()

        return {
            'timestamp': latest_metrics.timestamp.isoformat(),
            'portfolio_value': latest_metrics.portfolio_value,
            'daily_pnl': latest_metrics.daily_pnl,
            'current_drawdown': latest_metrics.current_drawdown,
            'var_95': latest_metrics.var_95,
            'active_alerts_count': len(active_alerts),
            'critical_alerts_count': len([a for a in active_alerts if a.severity == AlertSeverity.CRITICAL]),
            'system_status': 'healthy' if len(active_alerts) == 0 else 'warning'
        }

# Configuration helper
def create_risk_monitor_config() -> Dict[str, Any]:
    """Create default risk monitor configuration"""
    return {
        'risk_thresholds': {
            'max_drawdown': 0.15,           # 15%
            'warning_drawdown': 0.10,       # 10%
            'max_leverage': 2.0,            # 2:1
            'max_position_value': 50000,    # $50K
            'max_position_percent': 0.10,   # 10% of portfolio
            'vix_threshold': 30,            # VIX > 30
            'max_daily_loss': 0.05,         # 5% daily loss
        },
        'email_config': {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': os.getenv('EMAIL_USERNAME'),
            'password': os.getenv('EMAIL_PASSWORD'),
            'from_email': os.getenv('EMAIL_FROM'),
            'to_emails': [os.getenv('ALERT_EMAIL')]
        },
        'slack_webhook': os.getenv('SLACK_WEBHOOK_URL'),
        'redis_url': os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
        'max_history_length': 1000
    }