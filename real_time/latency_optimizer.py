"""
Latency Optimization for Millisecond Trading Execution
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import deque
import logging
import gc
import sys
import os
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor
import uvloop  # High-performance event loop
import numpy as np

@dataclass
class LatencyMeasurement:
    """Latency measurement data point"""
    timestamp: datetime
    component: str
    operation: str
    latency_ns: int
    metadata: Dict[str, Any]

class PerformanceMonitor:
    """Real-time performance monitoring"""

    def __init__(self, measurement_window: int = 1000):
        self.measurement_window = measurement_window
        self.measurements: Dict[str, deque] = {}
        self.performance_callbacks: List[Callable] = []
        self.is_monitoring = False
        self.monitor_interval = 0.1  # 100ms

        # System metrics
        self.cpu_usage_history = deque(maxlen=100)
        self.memory_usage_history = deque(maxlen=100)
        self.network_latency_history = deque(maxlen=100)

        # Performance thresholds
        self.thresholds = {
            'max_latency_ms': 5.0,
            'max_cpu_usage': 80.0,
            'max_memory_usage': 85.0,
            'max_jitter_ms': 1.0
        }

        self.logger = logging.getLogger("PerformanceMonitor")

    def start_monitoring(self):
        """Start performance monitoring"""
        self.is_monitoring = True
        threading.Thread(target=self._monitor_loop, daemon=True).start()
        self.logger.info("Started performance monitoring")

    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_monitoring = False
        self.logger.info("Stopped performance monitoring")

    def record_latency(self, component: str, operation: str, latency_ns: int, **metadata):
        """Record latency measurement"""
        measurement = LatencyMeasurement(
            timestamp=datetime.now(),
            component=component,
            operation=operation,
            latency_ns=latency_ns,
            metadata=metadata
        )

        key = f"{component}.{operation}"
        if key not in self.measurements:
            self.measurements[key] = deque(maxlen=self.measurement_window)

        self.measurements[key].append(measurement)

        # Check thresholds
        latency_ms = latency_ns / 1_000_000
        if latency_ms > self.thresholds['max_latency_ms']:
            self._trigger_alert('high_latency', {
                'component': component,
                'operation': operation,
                'latency_ms': latency_ms,
                'threshold': self.thresholds['max_latency_ms']
            })

    def get_latency_stats(self, component: str, operation: str) -> Dict[str, float]:
        """Get latency statistics for component/operation"""
        key = f"{component}.{operation}"
        if key not in self.measurements or not self.measurements[key]:
            return {}

        latencies = [m.latency_ns / 1_000_000 for m in self.measurements[key]]

        return {
            'count': len(latencies),
            'mean_ms': np.mean(latencies),
            'median_ms': np.median(latencies),
            'p95_ms': np.percentile(latencies, 95),
            'p99_ms': np.percentile(latencies, 99),
            'min_ms': np.min(latencies),
            'max_ms': np.max(latencies),
            'std_ms': np.std(latencies),
            'jitter_ms': np.std(latencies)  # Simplified jitter calculation
        }

    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system statistics"""
        return {
            'cpu_usage': psutil.cpu_percent(),
            'memory_usage': psutil.virtual_memory().percent,
            'disk_io': psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
            'network_io': psutil.net_io_counters()._asdict(),
            'process_count': len(psutil.pids()),
            'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
        }

    def add_performance_callback(self, callback: Callable[[str, Dict], None]):
        """Add performance alert callback"""
        self.performance_callbacks.append(callback)

    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # System metrics
                cpu_usage = psutil.cpu_percent()
                memory_usage = psutil.virtual_memory().percent

                self.cpu_usage_history.append(cpu_usage)
                self.memory_usage_history.append(memory_usage)

                # Check system thresholds
                if cpu_usage > self.thresholds['max_cpu_usage']:
                    self._trigger_alert('high_cpu', {'usage': cpu_usage})

                if memory_usage > self.thresholds['max_memory_usage']:
                    self._trigger_alert('high_memory', {'usage': memory_usage})

                # Check jitter thresholds
                self._check_jitter_thresholds()

                time.sleep(self.monitor_interval)

            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(1)

    def _check_jitter_thresholds(self):
        """Check for high jitter in recent measurements"""
        for key, measurements in self.measurements.items():
            if len(measurements) < 10:
                continue

            recent_measurements = list(measurements)[-10:]
            latencies = [m.latency_ns / 1_000_000 for m in recent_measurements]
            jitter = np.std(latencies)

            if jitter > self.thresholds['max_jitter_ms']:
                component, operation = key.split('.', 1)
                self._trigger_alert('high_jitter', {
                    'component': component,
                    'operation': operation,
                    'jitter_ms': jitter,
                    'threshold': self.thresholds['max_jitter_ms']
                })

    def _trigger_alert(self, alert_type: str, data: Dict[str, Any]):
        """Trigger performance alert"""
        for callback in self.performance_callbacks:
            try:
                callback(alert_type, data)
            except Exception as e:
                self.logger.error(f"Error in performance callback: {e}")

class LatencyOptimizer:
    """System-wide latency optimization"""

    def __init__(self):
        self.performance_monitor = PerformanceMonitor()
        self.optimizations_applied = set()
        self.executor = ThreadPoolExecutor(max_workers=4)

        # Optimization settings
        self.settings = {
            'use_uvloop': True,
            'gc_optimization': True,
            'cpu_affinity': True,
            'memory_optimization': True,
            'network_optimization': True,
            'io_optimization': True
        }

        self.logger = logging.getLogger("LatencyOptimizer")

    async def initialize(self):
        """Initialize latency optimizations"""
        try:
            self.logger.info("Initializing latency optimizations")

            # Apply system optimizations
            if self.settings['use_uvloop']:
                await self._optimize_event_loop()

            if self.settings['gc_optimization']:
                self._optimize_garbage_collection()

            if self.settings['cpu_affinity']:
                self._optimize_cpu_affinity()

            if self.settings['memory_optimization']:
                self._optimize_memory()

            if self.settings['network_optimization']:
                self._optimize_network()

            if self.settings['io_optimization']:
                self._optimize_io()

            # Start performance monitoring
            self.performance_monitor.start_monitoring()
            self.performance_monitor.add_performance_callback(self._handle_performance_alert)

            self.logger.info("Latency optimizations initialized")

        except Exception as e:
            self.logger.error(f"Error initializing optimizations: {e}")

    async def _optimize_event_loop(self):
        """Optimize asyncio event loop"""
        try:
            # Use uvloop for better performance on Linux
            if sys.platform != 'win32':
                import uvloop
                uvloop.install()
                self.optimizations_applied.add('uvloop')
                self.logger.info("UV loop optimization applied")

        except ImportError:
            self.logger.warning("uvloop not available, using default event loop")
        except Exception as e:
            self.logger.error(f"Error optimizing event loop: {e}")

    def _optimize_garbage_collection(self):
        """Optimize Python garbage collection"""
        try:
            # Tune garbage collection for lower latency
            gc.set_threshold(2000, 15, 15)  # Increase thresholds

            # Disable garbage collection during critical sections
            # (to be enabled/disabled dynamically)
            gc.disable()

            self.optimizations_applied.add('gc_optimization')
            self.logger.info("Garbage collection optimization applied")

        except Exception as e:
            self.logger.error(f"Error optimizing garbage collection: {e}")

    def _optimize_cpu_affinity(self):
        """Optimize CPU affinity"""
        try:
            # Set CPU affinity to specific cores for the main process
            process = psutil.Process()
            available_cpus = list(range(psutil.cpu_count()))

            # Use the first half of CPUs for trading processes
            if len(available_cpus) >= 4:
                trading_cpus = available_cpus[:len(available_cpus)//2]
                process.cpu_affinity(trading_cpus)
                self.optimizations_applied.add('cpu_affinity')
                self.logger.info(f"CPU affinity set to cores: {trading_cpus}")

        except Exception as e:
            self.logger.error(f"Error setting CPU affinity: {e}")

    def _optimize_memory(self):
        """Optimize memory usage"""
        try:
            # Set memory-related optimizations
            import mlock
            if hasattr(mlock, 'mlockall'):
                # Lock memory to prevent swapping (Linux)
                mlock.mlockall()
                self.optimizations_applied.add('memory_lock')

            # Pre-allocate memory pools for critical operations
            self._preallocate_memory_pools()

            self.optimizations_applied.add('memory_optimization')
            self.logger.info("Memory optimizations applied")

        except Exception as e:
            self.logger.error(f"Error optimizing memory: {e}")

    def _optimize_network(self):
        """Optimize network settings"""
        try:
            # Network buffer optimizations (platform-specific)
            if sys.platform == 'linux':
                # These would typically be set at the system level
                # Here we just log the recommendations
                self.logger.info("Network optimization recommendations logged")

            self.optimizations_applied.add('network_optimization')

        except Exception as e:
            self.logger.error(f"Error optimizing network: {e}")

    def _optimize_io(self):
        """Optimize I/O operations"""
        try:
            # Set I/O optimization flags
            if hasattr(os, 'O_DIRECT'):
                # Use direct I/O when possible
                pass

            self.optimizations_applied.add('io_optimization')
            self.logger.info("I/O optimizations applied")

        except Exception as e:
            self.logger.error(f"Error optimizing I/O: {e}")

    def _preallocate_memory_pools(self):
        """Pre-allocate memory pools for common operations"""
        try:
            # Pre-allocate common data structures
            self.order_pool = []
            self.tick_pool = []
            self.calculation_buffers = {
                'small': np.zeros(1000, dtype=np.float64),
                'medium': np.zeros(10000, dtype=np.float64),
                'large': np.zeros(100000, dtype=np.float64)
            }

        except Exception as e:
            self.logger.error(f"Error pre-allocating memory: {e}")

    def measure_execution_time(self, func_name: str):
        """Decorator to measure function execution time"""
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper(*args, **kwargs):
                    start_time = time.perf_counter_ns()
                    try:
                        result = await func(*args, **kwargs)
                        return result
                    finally:
                        end_time = time.perf_counter_ns()
                        latency = end_time - start_time
                        self.performance_monitor.record_latency(
                            'function',
                            func_name,
                            latency,
                            args_count=len(args),
                            kwargs_count=len(kwargs)
                        )
                return async_wrapper
            else:
                def sync_wrapper(*args, **kwargs):
                    start_time = time.perf_counter_ns()
                    try:
                        result = func(*args, **kwargs)
                        return result
                    finally:
                        end_time = time.perf_counter_ns()
                        latency = end_time - start_time
                        self.performance_monitor.record_latency(
                            'function',
                            func_name,
                            latency,
                            args_count=len(args),
                            kwargs_count=len(kwargs)
                        )
                return sync_wrapper
        return decorator

    async def optimize_critical_path(self, operation_name: str):
        """Context manager for critical path optimization"""
        class CriticalPathContext:
            def __init__(self, optimizer, operation):
                self.optimizer = optimizer
                self.operation = operation
                self.start_time = None

            async def __aenter__(self):
                self.start_time = time.perf_counter_ns()
                # Disable GC during critical operations
                gc.disable()
                return self

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                # Re-enable GC
                gc.enable()

                # Record timing
                if self.start_time:
                    latency = time.perf_counter_ns() - self.start_time
                    self.optimizer.performance_monitor.record_latency(
                        'critical_path',
                        self.operation,
                        latency
                    )

        return CriticalPathContext(self, operation_name)

    def get_optimization_report(self) -> Dict[str, Any]:
        """Get optimization status report"""
        return {
            'optimizations_applied': list(self.optimizations_applied),
            'performance_stats': {
                key: self.performance_monitor.get_latency_stats(*key.split('.', 1))
                for key in self.performance_monitor.measurements.keys()
            },
            'system_stats': self.performance_monitor.get_system_stats(),
            'thresholds': self.performance_monitor.thresholds,
            'monitor_status': self.performance_monitor.is_monitoring
        }

    def _handle_performance_alert(self, alert_type: str, data: Dict[str, Any]):
        """Handle performance alerts"""
        self.logger.warning(f"Performance alert: {alert_type} - {data}")

        # Take corrective actions based on alert type
        if alert_type == 'high_latency':
            self._handle_high_latency(data)
        elif alert_type == 'high_cpu':
            self._handle_high_cpu(data)
        elif alert_type == 'high_memory':
            self._handle_high_memory(data)
        elif alert_type == 'high_jitter':
            self._handle_high_jitter(data)

    def _handle_high_latency(self, data: Dict[str, Any]):
        """Handle high latency alert"""
        try:
            component = data.get('component')
            operation = data.get('operation')

            # Log detailed information for analysis
            self.logger.warning(f"High latency detected in {component}.{operation}: {data['latency_ms']:.2f}ms")

            # Could trigger automatic optimizations here

        except Exception as e:
            self.logger.error(f"Error handling high latency alert: {e}")

    def _handle_high_cpu(self, data: Dict[str, Any]):
        """Handle high CPU usage alert"""
        try:
            # Force garbage collection
            gc.collect()

            # Could reduce processing intensity
            self.logger.warning(f"High CPU usage: {data['usage']:.1f}%")

        except Exception as e:
            self.logger.error(f"Error handling high CPU alert: {e}")

    def _handle_high_memory(self, data: Dict[str, Any]):
        """Handle high memory usage alert"""
        try:
            # Force garbage collection
            gc.collect()

            # Clear caches if available
            self.logger.warning(f"High memory usage: {data['usage']:.1f}%")

        except Exception as e:
            self.logger.error(f"Error handling high memory alert: {e}")

    def _handle_high_jitter(self, data: Dict[str, Any]):
        """Handle high jitter alert"""
        try:
            component = data.get('component')
            operation = data.get('operation')

            self.logger.warning(f"High jitter in {component}.{operation}: {data['jitter_ms']:.2f}ms")

            # Could trigger jitter reduction measures

        except Exception as e:
            self.logger.error(f"Error handling high jitter alert: {e}")

    async def cleanup(self):
        """Cleanup optimization resources"""
        try:
            self.performance_monitor.stop_monitoring()

            # Re-enable garbage collection
            gc.enable()

            # Shutdown executor
            self.executor.shutdown(wait=True)

            self.logger.info("Latency optimizer cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

# Utility functions for latency measurement
def measure_latency(func):
    """Simple decorator for measuring function latency"""
    def wrapper(*args, **kwargs):
        start = time.perf_counter_ns()
        result = func(*args, **kwargs)
        end = time.perf_counter_ns()
        print(f"{func.__name__} latency: {(end - start) / 1_000_000:.3f}ms")
        return result
    return wrapper

async def measure_async_latency(func):
    """Simple decorator for measuring async function latency"""
    async def wrapper(*args, **kwargs):
        start = time.perf_counter_ns()
        result = await func(*args, **kwargs)
        end = time.perf_counter_ns()
        print(f"{func.__name__} latency: {(end - start) / 1_000_000:.3f}ms")
        return result
    return wrapper

class LatencyBenchmark:
    """Benchmark suite for latency testing"""

    def __init__(self):
        self.results = {}

    async def run_benchmark_suite(self) -> Dict[str, Any]:
        """Run comprehensive latency benchmark"""
        self.results = {}

        # Test basic operations
        await self._benchmark_basic_operations()

        # Test async operations
        await self._benchmark_async_operations()

        # Test data structures
        await self._benchmark_data_structures()

        # Test I/O operations
        await self._benchmark_io_operations()

        return self.results

    async def _benchmark_basic_operations(self):
        """Benchmark basic Python operations"""
        operations = {
            'arithmetic': lambda: sum(range(1000)),
            'list_creation': lambda: list(range(1000)),
            'dict_creation': lambda: {i: i for i in range(1000)},
            'string_concatenation': lambda: ''.join(str(i) for i in range(1000))
        }

        for name, operation in operations.items():
            latencies = []
            for _ in range(100):
                start = time.perf_counter_ns()
                operation()
                end = time.perf_counter_ns()
                latencies.append(end - start)

            self.results[f'basic_{name}'] = {
                'mean_ns': np.mean(latencies),
                'median_ns': np.median(latencies),
                'p95_ns': np.percentile(latencies, 95),
                'p99_ns': np.percentile(latencies, 99)
            }

    async def _benchmark_async_operations(self):
        """Benchmark async operations"""
        async def async_sleep():
            await asyncio.sleep(0.001)  # 1ms sleep

        async def async_task():
            return sum(range(100))

        operations = {
            'async_sleep': async_sleep,
            'async_task': async_task
        }

        for name, operation in operations.items():
            latencies = []
            for _ in range(100):
                start = time.perf_counter_ns()
                await operation()
                end = time.perf_counter_ns()
                latencies.append(end - start)

            self.results[f'async_{name}'] = {
                'mean_ns': np.mean(latencies),
                'median_ns': np.median(latencies),
                'p95_ns': np.percentile(latencies, 95),
                'p99_ns': np.percentile(latencies, 99)
            }

    async def _benchmark_data_structures(self):
        """Benchmark data structure operations"""
        # Test deque vs list performance
        test_data = list(range(1000))

        # Deque operations
        start = time.perf_counter_ns()
        d = deque(test_data)
        for _ in range(100):
            d.append(1)
            d.popleft()
        deque_time = time.perf_counter_ns() - start

        # List operations
        start = time.perf_counter_ns()
        l = list(test_data)
        for _ in range(100):
            l.append(1)
            l.pop(0)
        list_time = time.perf_counter_ns() - start

        self.results['data_structures'] = {
            'deque_operations_ns': deque_time,
            'list_operations_ns': list_time,
            'deque_advantage': list_time / deque_time if deque_time > 0 else 0
        }

    async def _benchmark_io_operations(self):
        """Benchmark I/O operations"""
        # File I/O
        test_data = "test data " * 1000

        start = time.perf_counter_ns()
        with open('/tmp/benchmark_test.txt', 'w') as f:
            f.write(test_data)
        file_write_time = time.perf_counter_ns() - start

        start = time.perf_counter_ns()
        with open('/tmp/benchmark_test.txt', 'r') as f:
            data = f.read()
        file_read_time = time.perf_counter_ns() - start

        # Cleanup
        try:
            os.remove('/tmp/benchmark_test.txt')
        except:
            pass

        self.results['io_operations'] = {
            'file_write_ns': file_write_time,
            'file_read_ns': file_read_time
        }