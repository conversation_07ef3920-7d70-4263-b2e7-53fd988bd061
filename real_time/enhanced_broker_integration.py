"""
Enhanced Broker Integration with Multiple Brokers
Supports Alpaca, Interactive Brokers, TD Ameritrade, and Crypto exchanges
"""

import asyncio
import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import json
import hmac
import hashlib
import base64
import time
from .order_execution_engine import (
    BrokerAdapter, Order, OrderStatus, OrderSide, OrderType, Fill
)

@dataclass
class BrokerCredentials:
    """Broker credentials configuration"""
    name: str
    api_key: str
    secret_key: str
    additional_params: Dict[str, str] = None
    paper_trading: bool = True
    enabled: bool = True

class InteractiveBrokersAdapter(BrokerAdapter):
    """Interactive Brokers TWS API adapter"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("interactive_brokers", config)
        self.client_id = config.get('client_id', 1)
        self.host = config.get('host', '127.0.0.1')
        self.port = config.get('port', 7497 if config.get('paper', True) else 7496)
        self.ib_client = None

    async def connect(self) -> bool:
        try:
            # Note: This would require ib_insync or similar IB API wrapper
            # For now, we'll simulate the connection
            self.logger.info("Interactive Brokers connection simulated")
            self.is_connected = True
            return True
        except Exception as e:
            self.logger.error(f"Error connecting to Interactive Brokers: {e}")
            return False

    async def submit_order(self, order: Order) -> Dict[str, Any]:
        try:
            # Simulate IB order submission
            self.logger.info(f"Simulating IB order submission for {order.symbol}")
            return {
                'status': 'success',
                'broker_order_id': f"IB_{int(time.time())}",
                'message': 'Order submitted to Interactive Brokers'
            }
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    async def cancel_order(self, broker_order_id: str) -> bool:
        # Simulate cancellation
        return True

    async def get_order_status(self, broker_order_id: str) -> Dict[str, Any]:
        # Simulate status retrieval
        return {'status': 'submitted'}

    async def get_positions(self) -> List[Dict[str, Any]]:
        return []

    async def get_account_info(self) -> Dict[str, Any]:
        return {'buying_power': 100000, 'equity': 100000}

class TDAmeritradeBrokerAdapter(BrokerAdapter):
    """TD Ameritrade broker adapter"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("td_ameritrade", config)
        self.base_url = "https://api.tdameritrade.com"
        self.access_token = config.get('access_token')
        self.session = None

    async def connect(self) -> bool:
        try:
            import aiohttp
            self.session = aiohttp.ClientSession(
                headers={'Authorization': f'Bearer {self.access_token}'}
            )

            # Test connection
            async with self.session.get(f"{self.base_url}/v1/accounts") as response:
                if response.status == 200:
                    self.is_connected = True
                    self.logger.info("Connected to TD Ameritrade")
                    return True
                else:
                    self.logger.error(f"Failed to connect to TD Ameritrade: {response.status}")
                    return False

        except Exception as e:
            self.logger.error(f"Error connecting to TD Ameritrade: {e}")
            return False

    async def submit_order(self, order: Order) -> Dict[str, Any]:
        try:
            order_data = {
                'orderType': order.order_type.value.upper(),
                'session': 'NORMAL',
                'duration': 'DAY',
                'orderStrategyType': 'SINGLE',
                'orderLegCollection': [
                    {
                        'instruction': order.side.value.upper(),
                        'quantity': order.quantity,
                        'instrument': {
                            'symbol': order.symbol,
                            'assetType': 'EQUITY'
                        }
                    }
                ]
            }

            if order.price:
                order_data['price'] = order.price

            account_id = self.config.get('account_id')
            url = f"{self.base_url}/v1/accounts/{account_id}/orders"

            async with self.session.post(url, json=order_data) as response:
                if response.status == 201:
                    location = response.headers.get('Location', '')
                    order_id = location.split('/')[-1] if location else str(int(time.time()))

                    return {
                        'status': 'success',
                        'broker_order_id': order_id,
                        'message': 'Order submitted to TD Ameritrade'
                    }
                else:
                    error_data = await response.json()
                    return {
                        'status': 'error',
                        'message': error_data.get('error', 'Unknown error')
                    }

        except Exception as e:
            self.logger.error(f"Error submitting order to TD Ameritrade: {e}")
            return {'status': 'error', 'message': str(e)}

    async def cancel_order(self, broker_order_id: str) -> bool:
        try:
            account_id = self.config.get('account_id')
            url = f"{self.base_url}/v1/accounts/{account_id}/orders/{broker_order_id}"

            async with self.session.delete(url) as response:
                return response.status == 200

        except Exception as e:
            self.logger.error(f"Error cancelling TD Ameritrade order: {e}")
            return False

    async def get_order_status(self, broker_order_id: str) -> Dict[str, Any]:
        try:
            account_id = self.config.get('account_id')
            url = f"{self.base_url}/v1/accounts/{account_id}/orders/{broker_order_id}"

            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {'error': f'HTTP {response.status}'}

        except Exception as e:
            self.logger.error(f"Error getting TD Ameritrade order status: {e}")
            return {'error': str(e)}

    async def get_positions(self) -> List[Dict[str, Any]]:
        try:
            account_id = self.config.get('account_id')
            url = f"{self.base_url}/v1/accounts/{account_id}"
            params = {'fields': 'positions'}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('securitiesAccount', {}).get('positions', [])
                else:
                    return []

        except Exception as e:
            self.logger.error(f"Error getting TD Ameritrade positions: {e}")
            return []

    async def get_account_info(self) -> Dict[str, Any]:
        try:
            account_id = self.config.get('account_id')
            url = f"{self.base_url}/v1/accounts/{account_id}"

            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('securitiesAccount', {})
                else:
                    return {}

        except Exception as e:
            self.logger.error(f"Error getting TD Ameritrade account info: {e}")
            return {}

class EnhancedCCXTAdapter(BrokerAdapter):
    """Enhanced CCXT adapter with better error handling and features"""

    def __init__(self, exchange_name: str, config: Dict[str, Any]):
        super().__init__(f"ccxt_{exchange_name}", config)
        self.exchange_name = exchange_name
        self.exchange = None
        self.supported_exchanges = {
            'binance', 'coinbase', 'kraken', 'huobi', 'okx', 'bybit',
            'bitfinex', 'gemini', 'kucoin', 'ftx'
        }

    async def connect(self) -> bool:
        try:
            import ccxt.async_support as ccxt

            if self.exchange_name not in self.supported_exchanges:
                self.logger.error(f"Unsupported exchange: {self.exchange_name}")
                return False

            # Get exchange class
            exchange_class = getattr(ccxt, self.exchange_name)

            # Initialize with enhanced configuration
            config = {
                'apiKey': self.config.get('api_key'),
                'secret': self.config.get('secret'),
                'enableRateLimit': True,
                'timeout': 30000,
                'options': {
                    'defaultType': 'spot'  # or 'future' for derivatives
                }
            }

            # Exchange-specific configurations
            if self.exchange_name == 'coinbase':
                config['passphrase'] = self.config.get('passphrase')
            elif self.exchange_name == 'binance':
                config['options']['recvWindow'] = 10000

            if self.config.get('sandbox', True):
                config['sandbox'] = True

            self.exchange = exchange_class(config)

            # Test connection
            await self.exchange.load_markets()
            balance = await self.exchange.fetch_balance()

            self.is_connected = True
            self.logger.info(f"Connected to {self.exchange_name} via CCXT")
            return True

        except Exception as e:
            self.logger.error(f"Error connecting to {self.exchange_name}: {e}")
            return False

    async def submit_order(self, order: Order) -> Dict[str, Any]:
        try:
            # Enhanced order parameters
            params = {}

            # Exchange-specific parameters
            if self.exchange_name == 'binance':
                params['newClientOrderId'] = order.order_id

            # Submit order
            result = await self.exchange.create_order(
                symbol=order.symbol,
                type=order.order_type.value,
                side=order.side.value,
                amount=order.quantity,
                price=order.price,
                params=params
            )

            return {
                'status': 'success',
                'broker_order_id': result.get('id'),
                'message': f'Order submitted to {self.exchange_name}',
                'exchange_response': result
            }

        except Exception as e:
            self.logger.error(f"Error submitting order to {self.exchange_name}: {e}")
            return {'status': 'error', 'message': str(e)}

    async def cancel_order(self, broker_order_id: str, symbol: str = None) -> bool:
        try:
            await self.exchange.cancel_order(broker_order_id, symbol)
            return True
        except Exception as e:
            self.logger.error(f"Error cancelling order on {self.exchange_name}: {e}")
            return False

    async def get_order_status(self, broker_order_id: str, symbol: str = None) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_order(broker_order_id, symbol)
        except Exception as e:
            self.logger.error(f"Error getting order status from {self.exchange_name}: {e}")
            return {'error': str(e)}

    async def get_positions(self) -> List[Dict[str, Any]]:
        try:
            if self.exchange.has['fetchPositions']:
                positions = await self.exchange.fetch_positions()
                return [pos for pos in positions if pos['contracts'] > 0]
            else:
                # Fallback: derive positions from balance
                balance = await self.exchange.fetch_balance()
                positions = []
                for currency, amount in balance.items():
                    if currency != 'info' and amount.get('total', 0) > 0:
                        positions.append({
                            'symbol': currency,
                            'contracts': amount['total'],
                            'notional': amount['total'],  # Simplified
                            'side': 'long'
                        })
                return positions
        except Exception as e:
            self.logger.error(f"Error getting positions from {self.exchange_name}: {e}")
            return []

    async def get_account_info(self) -> Dict[str, Any]:
        try:
            balance = await self.exchange.fetch_balance()
            trading_fees = await self.exchange.fetch_trading_fees() if self.exchange.has['fetchTradingFees'] else {}

            return {
                'balance': balance,
                'trading_fees': trading_fees,
                'exchange_info': {
                    'name': self.exchange_name,
                    'countries': self.exchange.countries,
                    'rateLimit': self.exchange.rateLimit,
                    'has': self.exchange.has
                }
            }

        except Exception as e:
            self.logger.error(f"Error getting account info from {self.exchange_name}: {e}")
            return {}

class BrokerManager:
    """Enhanced broker manager with intelligent routing and failover"""

    def __init__(self):
        self.brokers: Dict[str, BrokerAdapter] = {}
        self.broker_configs: Dict[str, BrokerCredentials] = {}
        self.routing_rules = {}
        self.failover_enabled = True
        self.logger = logging.getLogger("BrokerManager")

        # Load broker configurations
        self._load_broker_configs()

    def _load_broker_configs(self):
        """Load broker configurations from environment"""

        # Alpaca
        if os.getenv('ALPACA_API_KEY'):
            self.broker_configs['alpaca'] = BrokerCredentials(
                name='alpaca',
                api_key=os.getenv('ALPACA_API_KEY'),
                secret_key=os.getenv('ALPACA_SECRET_KEY'),
                paper_trading=os.getenv('ALPACA_PAPER', 'True').lower() == 'true'
            )

        # TD Ameritrade
        if os.getenv('TD_AMERITRADE_ACCESS_TOKEN'):
            self.broker_configs['td_ameritrade'] = BrokerCredentials(
                name='td_ameritrade',
                api_key=os.getenv('TD_AMERITRADE_ACCESS_TOKEN'),
                secret_key='',
                additional_params={'account_id': os.getenv('TD_AMERITRADE_ACCOUNT_ID')}
            )

        # Interactive Brokers
        if os.getenv('IB_ENABLE', 'False').lower() == 'true':
            self.broker_configs['interactive_brokers'] = BrokerCredentials(
                name='interactive_brokers',
                api_key='',
                secret_key='',
                additional_params={
                    'client_id': os.getenv('IB_CLIENT_ID', '1'),
                    'host': os.getenv('IB_HOST', '127.0.0.1'),
                    'port': os.getenv('IB_PORT', '7497')
                }
            )

        # Crypto exchanges
        crypto_exchanges = {
            'binance': ('BINANCE_API_KEY', 'BINANCE_SECRET_KEY'),
            'coinbase': ('COINBASE_API_KEY', 'COINBASE_SECRET_KEY', 'COINBASE_PASSPHRASE'),
            'kraken': ('KRAKEN_API_KEY', 'KRAKEN_SECRET_KEY'),
        }

        for exchange, keys in crypto_exchanges.items():
            api_key = os.getenv(keys[0])
            secret_key = os.getenv(keys[1])

            if api_key and secret_key:
                additional_params = {}
                if len(keys) > 2:  # Has passphrase
                    additional_params['passphrase'] = os.getenv(keys[2])

                self.broker_configs[exchange] = BrokerCredentials(
                    name=exchange,
                    api_key=api_key,
                    secret_key=secret_key,
                    additional_params=additional_params
                )

    async def initialize_brokers(self) -> Dict[str, bool]:
        """Initialize all configured brokers"""
        results = {}

        for name, config in self.broker_configs.items():
            if not config.enabled:
                continue

            try:
                # Create appropriate adapter
                if name == 'alpaca':
                    from .order_execution_engine import AlpacaBrokerAdapter
                    adapter = AlpacaBrokerAdapter({
                        'api_key': config.api_key,
                        'secret_key': config.secret_key,
                        'paper': config.paper_trading
                    })

                elif name == 'td_ameritrade':
                    adapter = TDAmeritradeBrokerAdapter({
                        'access_token': config.api_key,
                        'account_id': config.additional_params.get('account_id')
                    })

                elif name == 'interactive_brokers':
                    adapter = InteractiveBrokersAdapter(config.additional_params or {})

                elif name in ['binance', 'coinbase', 'kraken', 'huobi', 'okx']:
                    adapter_config = {
                        'api_key': config.api_key,
                        'secret': config.secret_key,
                        'sandbox': config.paper_trading
                    }
                    if config.additional_params:
                        adapter_config.update(config.additional_params)

                    adapter = EnhancedCCXTAdapter(name, adapter_config)

                else:
                    self.logger.warning(f"Unknown broker type: {name}")
                    results[name] = False
                    continue

                # Connect to broker
                success = await adapter.connect()
                if success:
                    self.brokers[name] = adapter
                    self.logger.info(f"Successfully initialized broker: {name}")
                else:
                    self.logger.error(f"Failed to initialize broker: {name}")

                results[name] = success

            except Exception as e:
                self.logger.error(f"Error initializing broker {name}: {e}")
                results[name] = False

        return results

    def setup_routing_rules(self, rules: Dict[str, Any]):
        """Setup intelligent order routing rules"""
        self.routing_rules = rules

        # Example rules:
        # {
        #     'stocks': ['alpaca', 'td_ameritrade', 'interactive_brokers'],
        #     'crypto': ['binance', 'coinbase', 'kraken'],
        #     'forex': ['interactive_brokers'],
        #     'preferred_crypto_exchange': 'binance',
        #     'preferred_stock_broker': 'alpaca'
        # }

    def select_broker(self, symbol: str, order_type: str = None) -> Optional[BrokerAdapter]:
        """Intelligently select broker based on symbol and routing rules"""
        try:
            # Determine asset type
            if any(pair in symbol.upper() for pair in ['BTC', 'ETH', 'USD', '/', 'USDT']):
                asset_type = 'crypto'
            elif any(curr in symbol.upper() for curr in ['EUR', 'GBP', 'JPY']):
                asset_type = 'forex'
            else:
                asset_type = 'stocks'

            # Get preferred brokers for asset type
            preferred_brokers = self.routing_rules.get(asset_type, [])

            if not preferred_brokers:
                # Fallback to any available broker
                preferred_brokers = list(self.brokers.keys())

            # Find first available broker
            for broker_name in preferred_brokers:
                if broker_name in self.brokers and self.brokers[broker_name].is_connected:
                    return self.brokers[broker_name]

            # No preferred broker available, try any connected broker
            for broker in self.brokers.values():
                if broker.is_connected:
                    return broker

            return None

        except Exception as e:
            self.logger.error(f"Error selecting broker: {e}")
            return None

    async def route_order(self, order: Order) -> Dict[str, Any]:
        """Route order to appropriate broker with failover"""
        try:
            # Select primary broker
            broker = self.select_broker(order.symbol, order.order_type.value)

            if not broker:
                return {
                    'status': 'error',
                    'message': 'No suitable broker available'
                }

            # Attempt to submit order
            result = await broker.submit_order(order)

            # Handle failover if primary fails
            if result['status'] == 'error' and self.failover_enabled:
                self.logger.warning(f"Primary broker failed, attempting failover")

                # Try other brokers
                for fallback_broker in self.brokers.values():
                    if (fallback_broker.is_connected and
                        fallback_broker != broker):

                        try:
                            result = await fallback_broker.submit_order(order)
                            if result['status'] == 'success':
                                result['broker_used'] = fallback_broker.broker_name
                                self.logger.info(f"Failover successful to {fallback_broker.broker_name}")
                                break
                        except Exception as e:
                            self.logger.error(f"Failover attempt failed: {e}")
                            continue

            return result

        except Exception as e:
            self.logger.error(f"Error routing order: {e}")
            return {'status': 'error', 'message': str(e)}

    def get_all_positions(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get positions from all brokers"""
        all_positions = {}

        for name, broker in self.brokers.items():
            if broker.is_connected:
                try:
                    positions = asyncio.create_task(broker.get_positions())
                    all_positions[name] = positions
                except Exception as e:
                    self.logger.error(f"Error getting positions from {name}: {e}")
                    all_positions[name] = []

        return all_positions

    def get_broker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all brokers"""
        status = {}

        for name, broker in self.brokers.items():
            status[name] = {
                'connected': broker.is_connected,
                'type': broker.broker_name,
                'paper_trading': self.broker_configs[name].paper_trading
            }

        return status

    async def disconnect_all(self):
        """Disconnect from all brokers"""
        for broker in self.brokers.values():
            try:
                await broker.disconnect()
            except Exception as e:
                self.logger.error(f"Error disconnecting broker: {e}")

        self.brokers.clear()
        self.logger.info("Disconnected from all brokers")

# Usage example
async def setup_enhanced_trading_system():
    """Example setup for enhanced trading system"""

    # Initialize broker manager
    broker_manager = BrokerManager()

    # Setup routing rules
    routing_rules = {
        'stocks': ['alpaca', 'td_ameritrade', 'interactive_brokers'],
        'crypto': ['binance', 'coinbase', 'kraken'],
        'forex': ['interactive_brokers'],
        'options': ['td_ameritrade', 'interactive_brokers']
    }

    broker_manager.setup_routing_rules(routing_rules)

    # Initialize brokers
    results = await broker_manager.initialize_brokers()

    print("Broker initialization results:")
    for broker, success in results.items():
        print(f"  {broker}: {'✓' if success else '✗'}")

    return broker_manager