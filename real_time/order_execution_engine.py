"""
Order Execution Engine for Live Trading
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
import uuid
from collections import deque
import threading
import websockets
import aiohttp
import ccxt.async_support as ccxt

class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"

class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """Order statuses"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"

class TimeInForce(Enum):
    """Time in force options"""
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate Or Cancel
    FOK = "fok"  # Fill Or Kill
    DAY = "day"  # Good for Day

@dataclass
class Order:
    """Trading order"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.GTC
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    remaining_quantity: float = field(init=False)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    submitted_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    broker_order_id: Optional[str] = None
    strategy_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        self.remaining_quantity = self.quantity

@dataclass
class Fill:
    """Order fill information"""
    fill_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    commission: float = 0.0
    broker_fill_id: Optional[str] = None

class BrokerAdapter:
    """Base class for broker adapters"""

    def __init__(self, broker_name: str, config: Dict[str, Any]):
        self.broker_name = broker_name
        self.config = config
        self.is_connected = False
        self.logger = logging.getLogger(f"BrokerAdapter.{broker_name}")

    async def connect(self) -> bool:
        """Connect to broker"""
        raise NotImplementedError

    async def disconnect(self):
        """Disconnect from broker"""
        raise NotImplementedError

    async def submit_order(self, order: Order) -> Dict[str, Any]:
        """Submit order to broker"""
        raise NotImplementedError

    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        raise NotImplementedError

    async def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """Get order status"""
        raise NotImplementedError

    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions"""
        raise NotImplementedError

    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        raise NotImplementedError

class AlpacaBrokerAdapter(BrokerAdapter):
    """Alpaca broker adapter"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("alpaca", config)
        self.base_url = "https://paper-api.alpaca.markets" if config.get('paper', True) else "https://api.alpaca.markets"
        self.session = None
        self.headers = {
            'APCA-API-KEY-ID': config.get('api_key'),
            'APCA-API-SECRET-KEY': config.get('secret_key')
        }

    async def connect(self) -> bool:
        try:
            self.session = aiohttp.ClientSession(headers=self.headers)

            # Test connection
            async with self.session.get(f"{self.base_url}/v2/account") as response:
                if response.status == 200:
                    self.is_connected = True
                    self.logger.info("Connected to Alpaca")
                    return True
                else:
                    self.logger.error(f"Failed to connect to Alpaca: {response.status}")
                    return False

        except Exception as e:
            self.logger.error(f"Error connecting to Alpaca: {e}")
            return False

    async def disconnect(self):
        if self.session:
            await self.session.close()
        self.is_connected = False

    async def submit_order(self, order: Order) -> Dict[str, Any]:
        try:
            order_data = {
                'symbol': order.symbol,
                'qty': str(order.quantity),
                'side': order.side.value,
                'type': order.order_type.value,
                'time_in_force': order.time_in_force.value
            }

            if order.price:
                order_data['limit_price'] = str(order.price)
            if order.stop_price:
                order_data['stop_price'] = str(order.stop_price)

            async with self.session.post(f"{self.base_url}/v2/orders", json=order_data) as response:
                result = await response.json()

                if response.status == 201:
                    return {
                        'status': 'success',
                        'broker_order_id': result['id'],
                        'message': 'Order submitted successfully'
                    }
                else:
                    return {
                        'status': 'error',
                        'message': result.get('message', 'Unknown error')
                    }

        except Exception as e:
            self.logger.error(f"Error submitting order: {e}")
            return {'status': 'error', 'message': str(e)}

    async def cancel_order(self, broker_order_id: str) -> bool:
        try:
            async with self.session.delete(f"{self.base_url}/v2/orders/{broker_order_id}") as response:
                return response.status == 204

        except Exception as e:
            self.logger.error(f"Error cancelling order: {e}")
            return False

    async def get_order_status(self, broker_order_id: str) -> Dict[str, Any]:
        try:
            async with self.session.get(f"{self.base_url}/v2/orders/{broker_order_id}") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {'error': f'HTTP {response.status}'}

        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return {'error': str(e)}

    async def get_positions(self) -> List[Dict[str, Any]]:
        try:
            async with self.session.get(f"{self.base_url}/v2/positions") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return []

        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    async def get_account_info(self) -> Dict[str, Any]:
        try:
            async with self.session.get(f"{self.base_url}/v2/account") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {}

        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return {}

class CCXTBrokerAdapter(BrokerAdapter):
    """CCXT-based broker adapter for crypto exchanges"""

    def __init__(self, exchange_name: str, config: Dict[str, Any]):
        super().__init__(f"ccxt_{exchange_name}", config)
        self.exchange_name = exchange_name
        self.exchange = None

    async def connect(self) -> bool:
        try:
            # Get exchange class
            exchange_class = getattr(ccxt, self.exchange_name)

            # Initialize exchange
            self.exchange = exchange_class({
                'apiKey': self.config.get('api_key'),
                'secret': self.config.get('secret'),
                'password': self.config.get('passphrase'),  # For some exchanges
                'sandbox': self.config.get('sandbox', True),
                'enableRateLimit': True,
            })

            # Test connection
            await self.exchange.load_markets()
            self.is_connected = True
            self.logger.info(f"Connected to {self.exchange_name}")
            return True

        except Exception as e:
            self.logger.error(f"Error connecting to {self.exchange_name}: {e}")
            return False

    async def disconnect(self):
        if self.exchange:
            await self.exchange.close()
        self.is_connected = False

    async def submit_order(self, order: Order) -> Dict[str, Any]:
        try:
            # Convert order to CCXT format
            order_type = order.order_type.value
            side = order.side.value
            amount = order.quantity
            price = order.price

            result = await self.exchange.create_order(
                symbol=order.symbol,
                type=order_type,
                side=side,
                amount=amount,
                price=price
            )

            return {
                'status': 'success',
                'broker_order_id': result['id'],
                'message': 'Order submitted successfully'
            }

        except Exception as e:
            self.logger.error(f"Error submitting order: {e}")
            return {'status': 'error', 'message': str(e)}

    async def cancel_order(self, broker_order_id: str, symbol: str = None) -> bool:
        try:
            await self.exchange.cancel_order(broker_order_id, symbol)
            return True

        except Exception as e:
            self.logger.error(f"Error cancelling order: {e}")
            return False

    async def get_order_status(self, broker_order_id: str, symbol: str = None) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_order(broker_order_id, symbol)

        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return {'error': str(e)}

    async def get_positions(self) -> List[Dict[str, Any]]:
        try:
            positions = await self.exchange.fetch_positions()
            return [pos for pos in positions if pos['contracts'] > 0]

        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    async def get_account_info(self) -> Dict[str, Any]:
        try:
            return await self.exchange.fetch_balance()

        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return {}

class OrderManager:
    """Manages order lifecycle and state"""

    def __init__(self):
        self.orders: Dict[str, Order] = {}
        self.fills: Dict[str, List[Fill]] = {}
        self.order_callbacks: List[Callable] = []
        self.fill_callbacks: List[Callable] = []
        self.logger = logging.getLogger("OrderManager")

    def add_order(self, order: Order):
        """Add order to management"""
        self.orders[order.order_id] = order
        self.fills[order.order_id] = []
        self.logger.info(f"Added order {order.order_id} for {order.symbol}")

    def update_order_status(self, order_id: str, status: OrderStatus, **kwargs):
        """Update order status"""
        if order_id in self.orders:
            order = self.orders[order_id]
            old_status = order.status
            order.status = status
            order.updated_at = datetime.now()

            # Update additional fields
            for key, value in kwargs.items():
                if hasattr(order, key):
                    setattr(order, key, value)

            # Set status-specific timestamps
            if status == OrderStatus.SUBMITTED and old_status == OrderStatus.PENDING:
                order.submitted_at = datetime.now()
            elif status == OrderStatus.FILLED:
                order.filled_at = datetime.now()

            # Notify callbacks
            for callback in self.order_callbacks:
                try:
                    callback(order, old_status)
                except Exception as e:
                    self.logger.error(f"Error in order callback: {e}")

            self.logger.info(f"Order {order_id} status updated: {old_status.value} -> {status.value}")

    def add_fill(self, fill: Fill):
        """Add fill to order"""
        if fill.order_id in self.orders:
            order = self.orders[fill.order_id]
            self.fills[fill.order_id].append(fill)

            # Update order fill information
            order.filled_quantity += fill.quantity
            order.remaining_quantity = order.quantity - order.filled_quantity

            # Calculate average fill price
            total_value = sum(f.quantity * f.price for f in self.fills[fill.order_id])
            order.avg_fill_price = total_value / order.filled_quantity if order.filled_quantity > 0 else 0

            # Update order status
            if order.remaining_quantity <= 0:
                self.update_order_status(fill.order_id, OrderStatus.FILLED)
            elif order.filled_quantity > 0:
                self.update_order_status(fill.order_id, OrderStatus.PARTIALLY_FILLED)

            # Notify fill callbacks
            for callback in self.fill_callbacks:
                try:
                    callback(fill, order)
                except Exception as e:
                    self.logger.error(f"Error in fill callback: {e}")

            self.logger.info(f"Fill added for order {fill.order_id}: {fill.quantity} @ {fill.price}")

    def get_order(self, order_id: str) -> Optional[Order]:
        """Get order by ID"""
        return self.orders.get(order_id)

    def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """Get orders for symbol"""
        return [order for order in self.orders.values() if order.symbol == symbol]

    def get_active_orders(self) -> List[Order]:
        """Get all active orders"""
        active_statuses = {OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED}
        return [order for order in self.orders.values() if order.status in active_statuses]

    def get_order_fills(self, order_id: str) -> List[Fill]:
        """Get fills for order"""
        return self.fills.get(order_id, [])

    def add_order_callback(self, callback: Callable[[Order, OrderStatus], None]):
        """Add order status change callback"""
        self.order_callbacks.append(callback)

    def add_fill_callback(self, callback: Callable[[Fill, Order], None]):
        """Add fill callback"""
        self.fill_callbacks.append(callback)

class OrderExecutionEngine:
    """Main order execution engine"""

    def __init__(self):
        self.brokers: Dict[str, BrokerAdapter] = {}
        self.order_manager = OrderManager()
        self.is_running = False
        self.order_queue = asyncio.Queue()
        self.cancel_queue = asyncio.Queue()

        # Execution settings
        self.settings = {
            'max_order_rate': 10,  # orders per second
            'order_timeout': 30,   # seconds
            'retry_attempts': 3,
            'retry_delay': 1,      # seconds
            'status_poll_interval': 5  # seconds
        }

        # Performance tracking
        self.execution_stats = {
            'orders_submitted': 0,
            'orders_filled': 0,
            'orders_cancelled': 0,
            'orders_rejected': 0,
            'avg_execution_time': 0,
            'fill_rate': 0
        }

        self.logger = logging.getLogger("OrderExecutionEngine")

    async def add_broker(self, broker_name: str, adapter: BrokerAdapter) -> bool:
        """Add broker adapter"""
        try:
            success = await adapter.connect()
            if success:
                self.brokers[broker_name] = adapter
                self.logger.info(f"Added broker: {broker_name}")
                return True
            else:
                self.logger.error(f"Failed to connect broker: {broker_name}")
                return False

        except Exception as e:
            self.logger.error(f"Error adding broker {broker_name}: {e}")
            return False

    async def submit_order(self, order: Order, broker_name: Optional[str] = None) -> str:
        """Submit order for execution"""
        try:
            # Generate order ID if not provided
            if not order.order_id:
                order.order_id = str(uuid.uuid4())

            # Add to order manager
            self.order_manager.add_order(order)

            # Queue for execution
            await self.order_queue.put((order, broker_name))

            self.logger.info(f"Order {order.order_id} queued for execution")
            return order.order_id

        except Exception as e:
            self.logger.error(f"Error submitting order: {e}")
            raise

    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        try:
            await self.cancel_queue.put(order_id)
            self.logger.info(f"Order {order_id} queued for cancellation")
            return True

        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False

    async def start(self):
        """Start order execution engine"""
        try:
            self.is_running = True

            # Start execution tasks
            tasks = [
                asyncio.create_task(self._process_order_queue()),
                asyncio.create_task(self._process_cancel_queue()),
                asyncio.create_task(self._monitor_orders()),
                asyncio.create_task(self._update_statistics())
            ]

            await asyncio.gather(*tasks)

        except Exception as e:
            self.logger.error(f"Error in execution engine: {e}")
            self.is_running = False

    async def stop(self):
        """Stop execution engine"""
        try:
            self.is_running = False

            # Disconnect all brokers
            for broker in self.brokers.values():
                await broker.disconnect()

            self.logger.info("Order execution engine stopped")

        except Exception as e:
            self.logger.error(f"Error stopping execution engine: {e}")

    async def _process_order_queue(self):
        """Process order submission queue"""
        while self.is_running:
            try:
                # Rate limiting
                await asyncio.sleep(1.0 / self.settings['max_order_rate'])

                # Get next order
                order, broker_name = await asyncio.wait_for(
                    self.order_queue.get(),
                    timeout=1.0
                )

                # Execute order
                await self._execute_order(order, broker_name)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing order queue: {e}")
                await asyncio.sleep(1)

    async def _process_cancel_queue(self):
        """Process order cancellation queue"""
        while self.is_running:
            try:
                order_id = await asyncio.wait_for(
                    self.cancel_queue.get(),
                    timeout=1.0
                )

                await self._cancel_order_internal(order_id)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing cancel queue: {e}")
                await asyncio.sleep(1)

    async def _execute_order(self, order: Order, broker_name: Optional[str]):
        """Execute individual order"""
        execution_start = time.time()

        try:
            # Select broker
            broker = self._select_broker(broker_name, order.symbol)
            if not broker:
                self.order_manager.update_order_status(
                    order.order_id,
                    OrderStatus.REJECTED,
                    metadata={'error': 'No suitable broker available'}
                )
                return

            # Submit to broker
            for attempt in range(self.settings['retry_attempts']):
                try:
                    result = await broker.submit_order(order)

                    if result['status'] == 'success':
                        self.order_manager.update_order_status(
                            order.order_id,
                            OrderStatus.SUBMITTED,
                            broker_order_id=result.get('broker_order_id')
                        )

                        # Update statistics
                        execution_time = time.time() - execution_start
                        self.execution_stats['orders_submitted'] += 1
                        self._update_avg_execution_time(execution_time)

                        break
                    else:
                        if attempt == self.settings['retry_attempts'] - 1:
                            # Final attempt failed
                            self.order_manager.update_order_status(
                                order.order_id,
                                OrderStatus.REJECTED,
                                metadata={'error': result.get('message', 'Unknown error')}
                            )
                            self.execution_stats['orders_rejected'] += 1
                        else:
                            # Retry
                            await asyncio.sleep(self.settings['retry_delay'])

                except Exception as e:
                    self.logger.error(f"Error executing order attempt {attempt + 1}: {e}")
                    if attempt == self.settings['retry_attempts'] - 1:
                        self.order_manager.update_order_status(
                            order.order_id,
                            OrderStatus.REJECTED,
                            metadata={'error': str(e)}
                        )
                        self.execution_stats['orders_rejected'] += 1

        except Exception as e:
            self.logger.error(f"Error in order execution: {e}")
            self.order_manager.update_order_status(
                order.order_id,
                OrderStatus.REJECTED,
                metadata={'error': str(e)}
            )

    async def _cancel_order_internal(self, order_id: str):
        """Internal order cancellation"""
        try:
            order = self.order_manager.get_order(order_id)
            if not order or not order.broker_order_id:
                self.logger.warning(f"Cannot cancel order {order_id}: not found or no broker ID")
                return

            # Find broker
            broker = None
            for b in self.brokers.values():
                if b.is_connected:
                    broker = b
                    break

            if broker:
                success = await broker.cancel_order(order.broker_order_id)
                if success:
                    self.order_manager.update_order_status(order_id, OrderStatus.CANCELLED)
                    self.execution_stats['orders_cancelled'] += 1
                    self.logger.info(f"Order {order_id} cancelled successfully")
                else:
                    self.logger.error(f"Failed to cancel order {order_id}")

        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")

    async def _monitor_orders(self):
        """Monitor order status"""
        while self.is_running:
            try:
                active_orders = self.order_manager.get_active_orders()

                for order in active_orders:
                    if order.broker_order_id:
                        await self._update_order_status(order)

                await asyncio.sleep(self.settings['status_poll_interval'])

            except Exception as e:
                self.logger.error(f"Error monitoring orders: {e}")
                await asyncio.sleep(self.settings['status_poll_interval'])

    async def _update_order_status(self, order: Order):
        """Update order status from broker"""
        try:
            # Find broker for this order
            broker = self._get_broker_for_order(order)
            if not broker:
                return

            # Get status from broker
            status_data = await broker.get_order_status(order.broker_order_id)

            if 'error' not in status_data:
                # Parse broker status and update order
                self._parse_broker_status(order, status_data)

        except Exception as e:
            self.logger.error(f"Error updating order status for {order.order_id}: {e}")

    def _select_broker(self, preferred_broker: Optional[str], symbol: str) -> Optional[BrokerAdapter]:
        """Select best broker for order"""
        if preferred_broker and preferred_broker in self.brokers:
            broker = self.brokers[preferred_broker]
            if broker.is_connected:
                return broker

        # Return first connected broker
        for broker in self.brokers.values():
            if broker.is_connected:
                return broker

        return None

    def _get_broker_for_order(self, order: Order) -> Optional[BrokerAdapter]:
        """Get broker that executed the order"""
        # For now, return first connected broker
        # In practice, you'd track which broker executed each order
        for broker in self.brokers.values():
            if broker.is_connected:
                return broker
        return None

    def _parse_broker_status(self, order: Order, status_data: Dict[str, Any]):
        """Parse broker status and update order"""
        # This would be broker-specific
        # For now, implement generic parsing
        pass

    def _update_avg_execution_time(self, execution_time: float):
        """Update average execution time"""
        current_avg = self.execution_stats['avg_execution_time']
        count = self.execution_stats['orders_submitted']

        if count == 1:
            self.execution_stats['avg_execution_time'] = execution_time
        else:
            self.execution_stats['avg_execution_time'] = (current_avg * (count - 1) + execution_time) / count

    async def _update_statistics(self):
        """Update execution statistics"""
        while self.is_running:
            try:
                # Calculate fill rate
                total_orders = (self.execution_stats['orders_filled'] +
                              self.execution_stats['orders_cancelled'] +
                              self.execution_stats['orders_rejected'])

                if total_orders > 0:
                    self.execution_stats['fill_rate'] = (
                        self.execution_stats['orders_filled'] / total_orders
                    )

                await asyncio.sleep(60)  # Update every minute

            except Exception as e:
                self.logger.error(f"Error updating statistics: {e}")
                await asyncio.sleep(60)

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return self.execution_stats.copy()

    def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        order = self.order_manager.get_order(order_id)
        if order:
            return {
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side.value,
                'type': order.order_type.value,
                'quantity': order.quantity,
                'filled_quantity': order.filled_quantity,
                'remaining_quantity': order.remaining_quantity,
                'status': order.status.value,
                'avg_fill_price': order.avg_fill_price,
                'created_at': order.created_at.isoformat(),
                'updated_at': order.updated_at.isoformat()
            }
        return None