"""
Live Performance Tracking and P&L System
Real-time calculation of portfolio performance, P&L, and trading metrics
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta, date
from enum import Enum
import logging
import pandas as pd
import numpy as np
import redis
from collections import defaultdict, deque
import psycopg2
from psycopg2.extras import RealDictCursor

class PnLType(Enum):
    """Types of P&L calculations"""
    REALIZED = "realized"
    UNREALIZED = "unrealized"
    TOTAL = "total"

class PerformancePeriod(Enum):
    """Performance calculation periods"""
    INTRADAY = "intraday"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    INCEPTION = "inception"

@dataclass
class Position:
    """Trading position data"""
    symbol: str
    quantity: float
    average_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    side: str  # 'long' or 'short'
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class Trade:
    """Individual trade record"""
    trade_id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    price: float
    commission: float
    timestamp: datetime
    strategy_id: Optional[str] = None

@dataclass
class PnLSnapshot:
    """P&L snapshot at a point in time"""
    timestamp: datetime
    total_equity: float
    cash_balance: float
    market_value: float
    realized_pnl: float
    unrealized_pnl: float
    total_pnl: float
    daily_pnl: float
    commission_paid: float
    positions_count: int
    winning_trades: int
    losing_trades: int
    total_trades: int

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    period: str
    start_date: datetime
    end_date: datetime
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    max_drawdown_duration: int
    win_rate: float
    profit_factor: float
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_commission: float
    calmar_ratio: float
    beta: Optional[float] = None
    alpha: Optional[float] = None

class LivePnLTracker:
    """Real-time P&L and performance tracking system"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_config = config.get('database', {})
        self.redis_config = config.get('redis', {})

        # Database connection
        self.db_connection = None
        self.redis_client = None

        # Position tracking
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.pnl_history: deque = deque(maxlen=10000)

        # Performance metrics cache
        self.performance_cache: Dict[str, PerformanceMetrics] = {}
        self.last_calculation_time = {}

        # Real-time data
        self.market_prices: Dict[str, float] = {}
        self.benchmark_prices: Dict[str, float] = {}

        # Configuration
        self.benchmark_symbol = config.get('benchmark_symbol', 'SPY')
        self.risk_free_rate = config.get('risk_free_rate', 0.02)  # 2%
        self.calculation_interval = config.get('calculation_interval', 1)  # seconds

        # Performance tracking
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.drawdown_curve: List[Tuple[datetime, float]] = []
        self.daily_returns: List[float] = []

        # Initial portfolio state
        self.initial_capital = config.get('initial_capital', 100000)
        self.current_equity = self.initial_capital
        self.cash_balance = self.initial_capital
        self.high_water_mark = self.initial_capital

        self.logger = logging.getLogger("LivePnLTracker")

    async def initialize(self) -> bool:
        """Initialize the P&L tracking system"""
        try:
            # Connect to database
            await self._connect_database()

            # Connect to Redis
            await self._connect_redis()

            # Load existing positions and trades
            await self._load_existing_data()

            # Initialize performance tracking
            await self._initialize_performance_tracking()

            self.logger.info("Live P&L tracker initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing P&L tracker: {e}")
            return False

    async def _connect_database(self):
        """Connect to PostgreSQL database"""
        try:
            self.db_connection = psycopg2.connect(
                host=self.db_config.get('host', 'localhost'),
                database=self.db_config.get('database', 'trading'),
                user=self.db_config.get('user', 'trading_user'),
                password=self.db_config.get('password', ''),
                port=self.db_config.get('port', 5432)
            )
            self.logger.info("Connected to PostgreSQL database")

        except Exception as e:
            self.logger.error(f"Error connecting to database: {e}")
            raise

    async def _connect_redis(self):
        """Connect to Redis"""
        try:
            redis_url = self.redis_config.get('url', 'redis://localhost:6379/0')
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            await self.redis_client.ping()
            self.logger.info("Connected to Redis")

        except Exception as e:
            self.logger.error(f"Error connecting to Redis: {e}")
            raise

    async def _load_existing_data(self):
        """Load existing positions and trades from database"""
        try:
            with self.db_connection.cursor(cursor_factory=RealDictCursor) as cursor:
                # Load current positions
                cursor.execute("""
                    SELECT symbol, quantity, average_cost, side, last_updated
                    FROM positions
                    WHERE quantity != 0
                """)

                for row in cursor.fetchall():
                    position = Position(
                        symbol=row['symbol'],
                        quantity=row['quantity'],
                        average_cost=row['average_cost'],
                        current_price=0,  # Will be updated from market data
                        market_value=0,
                        unrealized_pnl=0,
                        realized_pnl=0,
                        side=row['side']
                    )
                    self.positions[row['symbol']] = position

                # Load recent trades
                cursor.execute("""
                    SELECT trade_id, symbol, side, quantity, price, commission, timestamp, strategy_id
                    FROM trades
                    WHERE timestamp >= %s
                    ORDER BY timestamp DESC
                """, (datetime.now() - timedelta(days=30),))

                for row in cursor.fetchall():
                    trade = Trade(
                        trade_id=row['trade_id'],
                        symbol=row['symbol'],
                        side=row['side'],
                        quantity=row['quantity'],
                        price=row['price'],
                        commission=row['commission'],
                        timestamp=row['timestamp'],
                        strategy_id=row['strategy_id']
                    )
                    self.trades.append(trade)

            self.logger.info(f"Loaded {len(self.positions)} positions and {len(self.trades)} recent trades")

        except Exception as e:
            self.logger.error(f"Error loading existing data: {e}")

    async def _initialize_performance_tracking(self):
        """Initialize performance tracking structures"""
        try:
            # Load historical equity curve
            with self.db_connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT timestamp, total_equity
                    FROM portfolio_snapshots
                    WHERE timestamp >= %s
                    ORDER BY timestamp
                """, (datetime.now() - timedelta(days=365),))

                for row in cursor.fetchall():
                    self.equity_curve.append((row['timestamp'], row['total_equity']))

            # Calculate initial high water mark
            if self.equity_curve:
                self.high_water_mark = max(equity for _, equity in self.equity_curve)
            else:
                self.high_water_mark = self.initial_capital

            self.logger.info("Performance tracking initialized")

        except Exception as e:
            self.logger.error(f"Error initializing performance tracking: {e}")

    async def start_tracking(self):
        """Start real-time P&L tracking"""
        try:
            # Start tracking tasks
            tasks = [
                asyncio.create_task(self._update_market_prices()),
                asyncio.create_task(self._calculate_pnl()),
                asyncio.create_task(self._update_performance_metrics()),
                asyncio.create_task(self._save_snapshots()),
                asyncio.create_task(self._publish_updates())
            ]

            await asyncio.gather(*tasks)

        except Exception as e:
            self.logger.error(f"Error in P&L tracking: {e}")

    async def _update_market_prices(self):
        """Update market prices from data feed"""
        while True:
            try:
                # Subscribe to market data updates via Redis
                pubsub = self.redis_client.pubsub()
                await pubsub.subscribe('market_tick:*')

                async for message in pubsub.listen():
                    if message['type'] == 'message':
                        try:
                            # Parse market data
                            channel = message['channel']
                            symbol = channel.split(':')[1]
                            data = json.loads(message['data'])

                            # Update price
                            self.market_prices[symbol] = float(data['price'])

                            # Update position if we hold it
                            if symbol in self.positions:
                                await self._update_position_pnl(symbol)

                        except Exception as e:
                            self.logger.error(f"Error processing market tick: {e}")

            except Exception as e:
                self.logger.error(f"Error updating market prices: {e}")
                await asyncio.sleep(5)

    async def _update_position_pnl(self, symbol: str):
        """Update P&L for a specific position"""
        try:
            if symbol not in self.positions or symbol not in self.market_prices:
                return

            position = self.positions[symbol]
            current_price = self.market_prices[symbol]

            # Update position data
            position.current_price = current_price
            position.market_value = position.quantity * current_price

            # Calculate unrealized P&L
            if position.side == 'long':
                position.unrealized_pnl = (current_price - position.average_cost) * position.quantity
            else:  # short
                position.unrealized_pnl = (position.average_cost - current_price) * position.quantity

            position.timestamp = datetime.now()

        except Exception as e:
            self.logger.error(f"Error updating position P&L for {symbol}: {e}")

    async def _calculate_pnl(self):
        """Calculate overall portfolio P&L"""
        while True:
            try:
                # Calculate totals
                total_market_value = sum(pos.market_value for pos in self.positions.values())
                total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
                total_realized_pnl = sum(trade.quantity * trade.price for trade in self.trades
                                       if trade.timestamp.date() == date.today())

                # Update equity
                self.current_equity = self.cash_balance + total_market_value
                total_pnl = total_unrealized_pnl + total_realized_pnl

                # Calculate daily P&L
                start_of_day_equity = self._get_start_of_day_equity()
                daily_pnl = self.current_equity - start_of_day_equity

                # Create snapshot
                snapshot = PnLSnapshot(
                    timestamp=datetime.now(),
                    total_equity=self.current_equity,
                    cash_balance=self.cash_balance,
                    market_value=total_market_value,
                    realized_pnl=total_realized_pnl,
                    unrealized_pnl=total_unrealized_pnl,
                    total_pnl=total_pnl,
                    daily_pnl=daily_pnl,
                    commission_paid=sum(trade.commission for trade in self.trades
                                      if trade.timestamp.date() == date.today()),
                    positions_count=len([p for p in self.positions.values() if p.quantity != 0]),
                    winning_trades=len([t for t in self.trades if self._is_winning_trade(t)]),
                    losing_trades=len([t for t in self.trades if self._is_losing_trade(t)]),
                    total_trades=len(self.trades)
                )

                self.pnl_history.append(snapshot)

                # Update equity curve
                self.equity_curve.append((snapshot.timestamp, self.current_equity))

                # Update high water mark
                if self.current_equity > self.high_water_mark:
                    self.high_water_mark = self.current_equity

                await asyncio.sleep(self.calculation_interval)

            except Exception as e:
                self.logger.error(f"Error calculating P&L: {e}")
                await asyncio.sleep(self.calculation_interval)

    async def _update_performance_metrics(self):
        """Update performance metrics for different periods"""
        while True:
            try:
                current_time = datetime.now()

                # Update metrics for different periods
                periods = [
                    PerformancePeriod.INTRADAY,
                    PerformancePeriod.DAILY,
                    PerformancePeriod.WEEKLY,
                    PerformancePeriod.MONTHLY
                ]

                for period in periods:
                    # Check if update is needed
                    last_update = self.last_calculation_time.get(period.value, datetime.min)
                    update_interval = self._get_update_interval(period)

                    if current_time - last_update >= update_interval:
                        metrics = await self._calculate_performance_metrics(period)
                        if metrics:
                            self.performance_cache[period.value] = metrics
                            self.last_calculation_time[period.value] = current_time

                await asyncio.sleep(60)  # Update every minute

            except Exception as e:
                self.logger.error(f"Error updating performance metrics: {e}")
                await asyncio.sleep(60)

    def _get_update_interval(self, period: PerformancePeriod) -> timedelta:
        """Get update interval for performance period"""
        intervals = {
            PerformancePeriod.INTRADAY: timedelta(seconds=30),
            PerformancePeriod.DAILY: timedelta(minutes=5),
            PerformancePeriod.WEEKLY: timedelta(minutes=15),
            PerformancePeriod.MONTHLY: timedelta(hours=1)
        }
        return intervals.get(period, timedelta(minutes=5))

    async def _calculate_performance_metrics(self, period: PerformancePeriod) -> Optional[PerformanceMetrics]:
        """Calculate performance metrics for a specific period"""
        try:
            # Get date range for period
            end_date = datetime.now()
            start_date = self._get_period_start_date(period, end_date)

            # Get equity curve for period
            period_equity = [
                (timestamp, equity) for timestamp, equity in self.equity_curve
                if start_date <= timestamp <= end_date
            ]

            if len(period_equity) < 2:
                return None

            # Calculate returns
            initial_value = period_equity[0][1]
            final_value = period_equity[-1][1]
            total_return = (final_value - initial_value) / initial_value

            # Calculate period length in years
            period_days = (end_date - start_date).days
            years = period_days / 365.25 if period_days > 0 else 1

            # Annualized return
            annualized_return = (1 + total_return) ** (1 / years) - 1

            # Calculate daily returns
            returns = []
            for i in range(1, len(period_equity)):
                prev_value = period_equity[i-1][1]
                curr_value = period_equity[i][1]
                daily_return = (curr_value - prev_value) / prev_value
                returns.append(daily_return)

            # Volatility (annualized)
            volatility = np.std(returns) * np.sqrt(252) if returns else 0

            # Sharpe ratio
            excess_return = annualized_return - self.risk_free_rate
            sharpe_ratio = excess_return / volatility if volatility > 0 else 0

            # Sortino ratio (downside deviation)
            downside_returns = [r for r in returns if r < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(252) if downside_returns else 0
            sortino_ratio = excess_return / downside_deviation if downside_deviation > 0 else 0

            # Maximum drawdown
            max_dd, max_dd_duration = self._calculate_max_drawdown(period_equity)

            # Trading statistics
            period_trades = [
                trade for trade in self.trades
                if start_date <= trade.timestamp <= end_date
            ]

            total_trades = len(period_trades)
            winning_trades = len([t for t in period_trades if self._is_winning_trade(t)])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Profit factor
            gross_profit = sum(self._get_trade_pnl(t) for t in period_trades if self._is_winning_trade(t))
            gross_loss = abs(sum(self._get_trade_pnl(t) for t in period_trades if self._is_losing_trade(t)))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

            # Average win/loss
            wins = [self._get_trade_pnl(t) for t in period_trades if self._is_winning_trade(t)]
            losses = [self._get_trade_pnl(t) for t in period_trades if self._is_losing_trade(t)]

            average_win = np.mean(wins) if wins else 0
            average_loss = np.mean(losses) if losses else 0
            largest_win = max(wins) if wins else 0
            largest_loss = min(losses) if losses else 0

            # Total commission
            total_commission = sum(trade.commission for trade in period_trades)

            # Calmar ratio
            calmar_ratio = annualized_return / abs(max_dd) if max_dd != 0 else 0

            return PerformanceMetrics(
                period=period.value,
                start_date=start_date,
                end_date=end_date,
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_dd,
                max_drawdown_duration=max_dd_duration,
                win_rate=win_rate,
                profit_factor=profit_factor,
                average_win=average_win,
                average_loss=average_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                total_commission=total_commission,
                calmar_ratio=calmar_ratio
            )

        except Exception as e:
            self.logger.error(f"Error calculating performance metrics for {period.value}: {e}")
            return None

    def _get_period_start_date(self, period: PerformancePeriod, end_date: datetime) -> datetime:
        """Get start date for a performance period"""
        if period == PerformancePeriod.INTRADAY:
            return end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif period == PerformancePeriod.DAILY:
            return end_date - timedelta(days=1)
        elif period == PerformancePeriod.WEEKLY:
            return end_date - timedelta(weeks=1)
        elif period == PerformancePeriod.MONTHLY:
            return end_date - timedelta(days=30)
        elif period == PerformancePeriod.QUARTERLY:
            return end_date - timedelta(days=90)
        elif period == PerformancePeriod.YEARLY:
            return end_date - timedelta(days=365)
        else:  # INCEPTION
            return self.equity_curve[0][0] if self.equity_curve else end_date

    def _calculate_max_drawdown(self, equity_curve: List[Tuple[datetime, float]]) -> Tuple[float, int]:
        """Calculate maximum drawdown and duration"""
        if len(equity_curve) < 2:
            return 0, 0

        max_dd = 0
        max_dd_duration = 0
        peak = equity_curve[0][1]
        peak_time = equity_curve[0][0]
        dd_start = None

        for timestamp, equity in equity_curve:
            if equity > peak:
                peak = equity
                peak_time = timestamp
                dd_start = None
            else:
                drawdown = (peak - equity) / peak
                if drawdown > max_dd:
                    max_dd = drawdown

                if dd_start is None:
                    dd_start = peak_time

                if dd_start:
                    dd_duration = (timestamp - dd_start).days
                    if dd_duration > max_dd_duration:
                        max_dd_duration = dd_duration

        return max_dd, max_dd_duration

    def _is_winning_trade(self, trade: Trade) -> bool:
        """Check if a trade is winning"""
        pnl = self._get_trade_pnl(trade)
        return pnl > 0

    def _is_losing_trade(self, trade: Trade) -> bool:
        """Check if a trade is losing"""
        pnl = self._get_trade_pnl(trade)
        return pnl < 0

    def _get_trade_pnl(self, trade: Trade) -> float:
        """Get P&L for a trade (simplified)"""
        # This is a simplified calculation
        # In practice, you'd need to match buys/sells to calculate realized P&L
        return trade.quantity * trade.price - trade.commission

    def _get_start_of_day_equity(self) -> float:
        """Get equity at start of trading day"""
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

        # Find the last snapshot before today
        for timestamp, equity in reversed(self.equity_curve):
            if timestamp < today_start:
                return equity

        return self.initial_capital

    async def _save_snapshots(self):
        """Save P&L snapshots to database"""
        while True:
            try:
                if self.pnl_history:
                    latest_snapshot = self.pnl_history[-1]

                    with self.db_connection.cursor() as cursor:
                        cursor.execute("""
                            INSERT INTO portfolio_snapshots
                            (timestamp, total_equity, cash_balance, market_value,
                             realized_pnl, unrealized_pnl, total_pnl, daily_pnl,
                             commission_paid, positions_count)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (timestamp) DO UPDATE SET
                                total_equity = EXCLUDED.total_equity,
                                cash_balance = EXCLUDED.cash_balance,
                                market_value = EXCLUDED.market_value,
                                realized_pnl = EXCLUDED.realized_pnl,
                                unrealized_pnl = EXCLUDED.unrealized_pnl,
                                total_pnl = EXCLUDED.total_pnl,
                                daily_pnl = EXCLUDED.daily_pnl,
                                commission_paid = EXCLUDED.commission_paid,
                                positions_count = EXCLUDED.positions_count
                        """, (
                            latest_snapshot.timestamp,
                            latest_snapshot.total_equity,
                            latest_snapshot.cash_balance,
                            latest_snapshot.market_value,
                            latest_snapshot.realized_pnl,
                            latest_snapshot.unrealized_pnl,
                            latest_snapshot.total_pnl,
                            latest_snapshot.daily_pnl,
                            latest_snapshot.commission_paid,
                            latest_snapshot.positions_count
                        ))

                        self.db_connection.commit()

                await asyncio.sleep(30)  # Save every 30 seconds

            except Exception as e:
                self.logger.error(f"Error saving snapshots: {e}")
                await asyncio.sleep(30)

    async def _publish_updates(self):
        """Publish P&L updates to Redis"""
        while True:
            try:
                if self.pnl_history:
                    latest_snapshot = self.pnl_history[-1]

                    # Publish P&L update
                    pnl_data = {
                        'timestamp': latest_snapshot.timestamp.isoformat(),
                        'total_equity': latest_snapshot.total_equity,
                        'daily_pnl': latest_snapshot.daily_pnl,
                        'unrealized_pnl': latest_snapshot.unrealized_pnl,
                        'realized_pnl': latest_snapshot.realized_pnl,
                        'total_pnl': latest_snapshot.total_pnl,
                        'positions_count': latest_snapshot.positions_count,
                        'winning_trades': latest_snapshot.winning_trades,
                        'losing_trades': latest_snapshot.losing_trades
                    }

                    await self.redis_client.publish('pnl_update', json.dumps(pnl_data))

                    # Publish performance metrics
                    if self.performance_cache:
                        await self.redis_client.publish('performance_update',
                                                      json.dumps(self.performance_cache))

                await asyncio.sleep(1)  # Publish every second

            except Exception as e:
                self.logger.error(f"Error publishing updates: {e}")
                await asyncio.sleep(1)

    async def record_trade(self, trade: Trade):
        """Record a new trade"""
        try:
            self.trades.append(trade)

            # Update position
            await self._update_position_from_trade(trade)

            # Save to database
            with self.db_connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO trades
                    (trade_id, symbol, side, quantity, price, commission, timestamp, strategy_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    trade.trade_id, trade.symbol, trade.side, trade.quantity,
                    trade.price, trade.commission, trade.timestamp, trade.strategy_id
                ))

                self.db_connection.commit()

            self.logger.info(f"Recorded trade: {trade.trade_id}")

        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")

    async def _update_position_from_trade(self, trade: Trade):
        """Update position from new trade"""
        try:
            if trade.symbol not in self.positions:
                self.positions[trade.symbol] = Position(
                    symbol=trade.symbol,
                    quantity=0,
                    average_cost=0,
                    current_price=trade.price,
                    market_value=0,
                    unrealized_pnl=0,
                    realized_pnl=0,
                    side='long'
                )

            position = self.positions[trade.symbol]

            if trade.side == 'buy':
                # Update average cost
                total_cost = position.quantity * position.average_cost + trade.quantity * trade.price
                total_quantity = position.quantity + trade.quantity

                if total_quantity > 0:
                    position.average_cost = total_cost / total_quantity

                position.quantity = total_quantity
                position.side = 'long' if position.quantity > 0 else 'short'

                # Update cash balance
                self.cash_balance -= trade.quantity * trade.price + trade.commission

            elif trade.side == 'sell':
                # Calculate realized P&L
                if position.quantity > 0:
                    realized_pnl = (trade.price - position.average_cost) * min(trade.quantity, position.quantity)
                    position.realized_pnl += realized_pnl

                position.quantity -= trade.quantity
                position.side = 'long' if position.quantity > 0 else 'short'

                # Update cash balance
                self.cash_balance += trade.quantity * trade.price - trade.commission

            # Update market value and unrealized P&L
            if trade.symbol in self.market_prices:
                await self._update_position_pnl(trade.symbol)

        except Exception as e:
            self.logger.error(f"Error updating position from trade: {e}")

    def get_current_pnl(self) -> Optional[PnLSnapshot]:
        """Get current P&L snapshot"""
        return self.pnl_history[-1] if self.pnl_history else None

    def get_performance_metrics(self, period: PerformancePeriod) -> Optional[PerformanceMetrics]:
        """Get performance metrics for a period"""
        return self.performance_cache.get(period.value)

    def get_positions_summary(self) -> Dict[str, Any]:
        """Get summary of current positions"""
        active_positions = {symbol: pos for symbol, pos in self.positions.items() if pos.quantity != 0}

        return {
            'total_positions': len(active_positions),
            'total_market_value': sum(pos.market_value for pos in active_positions.values()),
            'total_unrealized_pnl': sum(pos.unrealized_pnl for pos in active_positions.values()),
            'positions': {
                symbol: {
                    'quantity': pos.quantity,
                    'average_cost': pos.average_cost,
                    'current_price': pos.current_price,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'pnl_percent': (pos.unrealized_pnl / (pos.quantity * pos.average_cost)) * 100
                    if pos.quantity * pos.average_cost != 0 else 0
                } for symbol, pos in active_positions.items()
            }
        }

    def get_trading_summary(self, period: PerformancePeriod = PerformancePeriod.DAILY) -> Dict[str, Any]:
        """Get trading summary for a period"""
        end_date = datetime.now()
        start_date = self._get_period_start_date(period, end_date)

        period_trades = [
            trade for trade in self.trades
            if start_date <= trade.timestamp <= end_date
        ]

        return {
            'period': period.value,
            'total_trades': len(period_trades),
            'total_volume': sum(trade.quantity * trade.price for trade in period_trades),
            'total_commission': sum(trade.commission for trade in period_trades),
            'symbols_traded': len(set(trade.symbol for trade in period_trades)),
            'buy_trades': len([t for t in period_trades if t.side == 'buy']),
            'sell_trades': len([t for t in period_trades if t.side == 'sell'])
        }