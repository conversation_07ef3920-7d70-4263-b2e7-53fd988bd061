-- Setup script for Mathematical Trading System database
DO $$
BEGIN
    -- Create user if it doesn't exist
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'trading_user') THEN
        CREATE USER trading_user WITH PASSWORD 'hejhej';
    ELSE
        ALTER USER trading_user WITH PASSWORD 'hejhej';
    END IF;

    -- Grant necessary privileges
    ALTER USER trading_user CREATEDB;
    GRANT ALL PRIVILEGES ON DATABASE mathematical_trading TO trading_user;
END
$$;

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE mathematical_trading OWNER trading_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'mathematical_trading')\gexec