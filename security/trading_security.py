"""
Comprehensive Security Hardening for Live Trading System
Includes authentication, authorization, encryption, API security, and audit logging
"""

import asyncio
import os
import secrets
import hashlib
import hmac
import base64
import jwt
import time
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import cryptography
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import bcrypt
import ipaddress
from functools import wraps
import redis
import psycopg2

class SecurityLevel(Enum):
    """Security access levels"""
    PUBLIC = "public"
    AUTHENTICATED = "authenticated"
    TRADER = "trader"
    ADMIN = "admin"
    SYSTEM = "system"

class AuditAction(Enum):
    """Audit action types"""
    LOGIN = "login"
    LOGOUT = "logout"
    TRADE_SUBMIT = "trade_submit"
    TRADE_CANCEL = "trade_cancel"
    POSITION_MODIFY = "position_modify"
    CONFIG_CHANGE = "config_change"
    ADMIN_ACTION = "admin_action"
    SECURITY_ALERT = "security_alert"
    DATA_ACCESS = "data_access"
    API_CALL = "api_call"

@dataclass
class SecurityConfig:
    """Security configuration"""
    jwt_secret: str
    jwt_expiration_hours: int = 24
    api_rate_limit_per_minute: int = 60
    max_failed_login_attempts: int = 5
    account_lockout_duration_minutes: int = 30
    require_2fa: bool = True
    allowed_ip_ranges: List[str] = field(default_factory=list)
    password_min_length: int = 12
    password_require_special: bool = True
    session_timeout_minutes: int = 60
    audit_retention_days: int = 365

@dataclass
class User:
    """User account"""
    user_id: str
    username: str
    email: str
    password_hash: str
    security_level: SecurityLevel
    is_active: bool = True
    is_2fa_enabled: bool = False
    totp_secret: Optional[str] = None
    failed_login_attempts: int = 0
    last_login: Optional[datetime] = None
    account_locked_until: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_password_change: Optional[datetime] = None

@dataclass
class AuditEvent:
    """Audit log event"""
    event_id: str
    user_id: Optional[str]
    action: AuditAction
    resource: str
    details: Dict[str, Any]
    ip_address: str
    user_agent: str
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    risk_score: int = 0

class EncryptionManager:
    """Handles encryption and decryption of sensitive data"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.master_key = self._get_or_create_master_key()
        self.cipher_suite = Fernet(self.master_key)
        self.logger = logging.getLogger("EncryptionManager")

    def _get_or_create_master_key(self) -> bytes:
        """Get or create master encryption key"""
        key_file = self.config.get('master_key_file', '/etc/trading/master.key')

        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            os.makedirs(os.path.dirname(key_file), exist_ok=True)

            # Save with restricted permissions
            with open(key_file, 'wb') as f:
                f.write(key)
            os.chmod(key_file, 0o600)

            self.logger.info(f"Generated new master key: {key_file}")
            return key

    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.b64encode(encrypted_data).decode()
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            raise

    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            raise

    def encrypt_api_key(self, api_key: str) -> str:
        """Encrypt API key for storage"""
        return self.encrypt(api_key)

    def decrypt_api_key(self, encrypted_api_key: str) -> str:
        """Decrypt API key for use"""
        return self.decrypt(encrypted_api_key)

    def hash_password(self, password: str) -> str:
        """Hash password with salt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception:
            return False

class AuthenticationManager:
    """Handles user authentication and session management"""

    def __init__(self, config: SecurityConfig, encryption_manager: EncryptionManager):
        self.config = config
        self.encryption_manager = encryption_manager
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.rate_limiter: Dict[str, List[datetime]] = {}
        self.logger = logging.getLogger("AuthenticationManager")

        # Database connection for user management
        self.db_connection = None

    async def authenticate_user(self, username: str, password: str,
                               ip_address: str, user_agent: str, totp_code: str = None) -> Optional[Dict[str, Any]]:
        """Authenticate user with username/password and optional 2FA"""
        try:
            # Rate limiting
            if not self._check_rate_limit(ip_address):
                self.logger.warning(f"Rate limit exceeded for IP: {ip_address}")
                return None

            # Get user from database
            user = await self._get_user_by_username(username)

            if not user:
                self.logger.warning(f"Authentication failed - user not found: {username}")
                return None

            # Check if account is locked
            if user.account_locked_until and datetime.now() < user.account_locked_until:
                self.logger.warning(f"Authentication failed - account locked: {username}")
                return None

            # Check if user is active
            if not user.is_active:
                self.logger.warning(f"Authentication failed - account inactive: {username}")
                return None

            # Verify password
            if not self.encryption_manager.verify_password(password, user.password_hash):
                await self._handle_failed_login(user)
                self.logger.warning(f"Authentication failed - invalid password: {username}")
                return None

            # Check 2FA if enabled
            if user.is_2fa_enabled:
                if not totp_code:
                    self.logger.warning(f"Authentication failed - 2FA code required: {username}")
                    return None

                if not self._verify_totp(user.totp_secret, totp_code):
                    self.logger.warning(f"Authentication failed - invalid 2FA code: {username}")
                    return None

            # Check IP restrictions
            if not self._check_ip_allowed(ip_address):
                self.logger.warning(f"Authentication failed - IP not allowed: {ip_address}")
                return None

            # Success - reset failed attempts and create session
            await self._reset_failed_attempts(user)
            session_token = await self._create_session(user, ip_address, user_agent)

            # Update last login
            await self._update_last_login(user)

            self.logger.info(f"User authenticated successfully: {username}")

            return {
                'session_token': session_token,
                'user_id': user.user_id,
                'username': user.username,
                'security_level': user.security_level.value,
                'expires_at': (datetime.now() + timedelta(hours=self.config.jwt_expiration_hours)).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return None

    async def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Validate session token"""
        try:
            # Decode JWT token
            payload = jwt.decode(
                session_token,
                self.config.jwt_secret,
                algorithms=['HS256']
            )

            user_id = payload['user_id']
            session_id = payload['session_id']

            # Check if session exists and is valid
            if session_id not in self.active_sessions:
                return None

            session = self.active_sessions[session_id]

            # Check session timeout
            last_activity = session['last_activity']
            if datetime.now() - last_activity > timedelta(minutes=self.config.session_timeout_minutes):
                await self._invalidate_session(session_id)
                return None

            # Update last activity
            session['last_activity'] = datetime.now()

            # Get user details
            user = await self._get_user_by_id(user_id)
            if not user or not user.is_active:
                await self._invalidate_session(session_id)
                return None

            return {
                'user_id': user.user_id,
                'username': user.username,
                'security_level': user.security_level.value,
                'session_id': session_id
            }

        except jwt.ExpiredSignatureError:
            self.logger.warning("Session token expired")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("Invalid session token")
            return None
        except Exception as e:
            self.logger.error(f"Session validation error: {e}")
            return None

    async def _create_session(self, user: User, ip_address: str, user_agent: str) -> str:
        """Create new session"""
        session_id = secrets.token_urlsafe(32)

        # Create JWT token
        payload = {
            'user_id': user.user_id,
            'session_id': session_id,
            'iat': int(time.time()),
            'exp': int(time.time()) + (self.config.jwt_expiration_hours * 3600)
        }

        token = jwt.encode(payload, self.config.jwt_secret, algorithm='HS256')

        # Store session
        self.active_sessions[session_id] = {
            'user_id': user.user_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'created_at': datetime.now(),
            'last_activity': datetime.now()
        }

        return token

    async def _invalidate_session(self, session_id: str):
        """Invalidate session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]

    def _check_rate_limit(self, identifier: str) -> bool:
        """Check rate limit for IP or user"""
        current_time = datetime.now()
        window_start = current_time - timedelta(minutes=1)

        if identifier not in self.rate_limiter:
            self.rate_limiter[identifier] = []

        # Clean old requests
        self.rate_limiter[identifier] = [
            req_time for req_time in self.rate_limiter[identifier]
            if req_time > window_start
        ]

        # Check limit
        if len(self.rate_limiter[identifier]) >= self.config.api_rate_limit_per_minute:
            return False

        # Add current request
        self.rate_limiter[identifier].append(current_time)
        return True

    def _check_ip_allowed(self, ip_address: str) -> bool:
        """Check if IP address is allowed"""
        if not self.config.allowed_ip_ranges:
            return True  # No restrictions

        try:
            ip = ipaddress.ip_address(ip_address)
            for ip_range in self.config.allowed_ip_ranges:
                if ip in ipaddress.ip_network(ip_range):
                    return True
            return False
        except Exception:
            return False

    def _verify_totp(self, secret: str, code: str) -> bool:
        """Verify TOTP code"""
        # This would use a TOTP library like pyotp
        # For now, we'll simulate verification
        return len(code) == 6 and code.isdigit()

    async def _handle_failed_login(self, user: User):
        """Handle failed login attempt"""
        user.failed_login_attempts += 1

        if user.failed_login_attempts >= self.config.max_failed_login_attempts:
            user.account_locked_until = datetime.now() + timedelta(
                minutes=self.config.account_lockout_duration_minutes
            )

        await self._update_user(user)

    async def _reset_failed_attempts(self, user: User):
        """Reset failed login attempts"""
        user.failed_login_attempts = 0
        user.account_locked_until = None
        await self._update_user(user)

    async def _get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username from database"""
        # This would query your user database
        # For now, return a mock user
        return User(
            user_id="user_1",
            username=username,
            email=f"{username}@example.com",
            password_hash=self.encryption_manager.hash_password("password123"),
            security_level=SecurityLevel.TRADER
        )

    async def _get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID from database"""
        # This would query your user database
        return None

    async def _update_user(self, user: User):
        """Update user in database"""
        # This would update your user database
        pass

    async def _update_last_login(self, user: User):
        """Update last login timestamp"""
        user.last_login = datetime.now()
        await self._update_user(user)

class AuthorizationManager:
    """Handles role-based access control and permissions"""

    def __init__(self):
        self.permissions: Dict[SecurityLevel, List[str]] = {
            SecurityLevel.PUBLIC: [
                'read_public_data'
            ],
            SecurityLevel.AUTHENTICATED: [
                'read_public_data',
                'read_user_profile'
            ],
            SecurityLevel.TRADER: [
                'read_public_data',
                'read_user_profile',
                'read_market_data',
                'read_positions',
                'submit_orders',
                'cancel_orders',
                'read_trade_history'
            ],
            SecurityLevel.ADMIN: [
                'read_public_data',
                'read_user_profile',
                'read_market_data',
                'read_positions',
                'submit_orders',
                'cancel_orders',
                'read_trade_history',
                'manage_users',
                'modify_system_config',
                'read_audit_logs'
            ],
            SecurityLevel.SYSTEM: [
                '*'  # All permissions
            ]
        }

        self.logger = logging.getLogger("AuthorizationManager")

    def check_permission(self, security_level: SecurityLevel, required_permission: str) -> bool:
        """Check if user has required permission"""
        try:
            user_permissions = self.permissions.get(security_level, [])

            # System level has all permissions
            if '*' in user_permissions:
                return True

            return required_permission in user_permissions

        except Exception as e:
            self.logger.error(f"Permission check error: {e}")
            return False

    def require_permission(self, permission: str):
        """Decorator to require specific permission"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Get user context (this would be injected by middleware)
                user_context = kwargs.get('user_context')

                if not user_context:
                    raise PermissionError("Authentication required")

                security_level = SecurityLevel(user_context['security_level'])

                if not self.check_permission(security_level, permission):
                    raise PermissionError(f"Permission denied: {permission}")

                return await func(*args, **kwargs)

            return wrapper
        return decorator

class AuditLogger:
    """Comprehensive audit logging system"""

    def __init__(self, config: SecurityConfig):
        self.config = config
        self.audit_events: List[AuditEvent] = []
        self.db_connection = None
        self.logger = logging.getLogger("AuditLogger")

    async def log_event(self, user_id: Optional[str], action: AuditAction,
                       resource: str, details: Dict[str, Any],
                       ip_address: str, user_agent: str,
                       success: bool = True, risk_score: int = 0):
        """Log audit event"""
        try:
            event = AuditEvent(
                event_id=secrets.token_urlsafe(16),
                user_id=user_id,
                action=action,
                resource=resource,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                success=success,
                risk_score=risk_score
            )

            # Store in memory buffer
            self.audit_events.append(event)

            # Persist to database
            await self._persist_audit_event(event)

            # Log high-risk events immediately
            if risk_score > 7:
                self.logger.critical(f"High-risk audit event: {event.action.value} by {user_id}")

        except Exception as e:
            self.logger.error(f"Failed to log audit event: {e}")

    async def _persist_audit_event(self, event: AuditEvent):
        """Persist audit event to database"""
        try:
            # This would insert into your audit log table
            pass
        except Exception as e:
            self.logger.error(f"Failed to persist audit event: {e}")

    def log_login(self, user_id: str, ip_address: str, user_agent: str, success: bool):
        """Log login attempt"""
        asyncio.create_task(self.log_event(
            user_id=user_id,
            action=AuditAction.LOGIN,
            resource="authentication",
            details={'login_method': 'password'},
            ip_address=ip_address,
            user_agent=user_agent,
            success=success,
            risk_score=3 if success else 6
        ))

    def log_trade(self, user_id: str, trade_details: Dict[str, Any],
                  ip_address: str, user_agent: str):
        """Log trade submission"""
        asyncio.create_task(self.log_event(
            user_id=user_id,
            action=AuditAction.TRADE_SUBMIT,
            resource="trading",
            details=trade_details,
            ip_address=ip_address,
            user_agent=user_agent,
            success=True,
            risk_score=4
        ))

    def log_admin_action(self, user_id: str, action_details: Dict[str, Any],
                        ip_address: str, user_agent: str):
        """Log administrative action"""
        asyncio.create_task(self.log_event(
            user_id=user_id,
            action=AuditAction.ADMIN_ACTION,
            resource="system",
            details=action_details,
            ip_address=ip_address,
            user_agent=user_agent,
            success=True,
            risk_score=7
        ))

class APISecurityManager:
    """API security and protection"""

    def __init__(self, config: SecurityConfig):
        self.config = config
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        self.request_signatures: Dict[str, datetime] = {}
        self.logger = logging.getLogger("APISecurityManager")

    def generate_api_key(self, user_id: str, permissions: List[str]) -> Dict[str, str]:
        """Generate API key for user"""
        api_key = secrets.token_urlsafe(32)
        api_secret = secrets.token_urlsafe(64)

        self.api_keys[api_key] = {
            'user_id': user_id,
            'secret': api_secret,
            'permissions': permissions,
            'created_at': datetime.now(),
            'last_used': None,
            'usage_count': 0
        }

        return {
            'api_key': api_key,
            'api_secret': api_secret
        }

    def validate_api_request(self, api_key: str, signature: str,
                           timestamp: str, request_data: str) -> Optional[Dict[str, Any]]:
        """Validate API request with signature"""
        try:
            if api_key not in self.api_keys:
                return None

            key_info = self.api_keys[api_key]
            api_secret = key_info['secret']

            # Check timestamp to prevent replay attacks
            request_time = datetime.fromtimestamp(int(timestamp))
            if datetime.now() - request_time > timedelta(minutes=5):
                self.logger.warning(f"API request too old: {timestamp}")
                return None

            # Verify signature
            expected_signature = self._calculate_signature(
                api_secret, timestamp, request_data
            )

            if not hmac.compare_digest(signature, expected_signature):
                self.logger.warning(f"Invalid API signature for key: {api_key}")
                return None

            # Update usage
            key_info['last_used'] = datetime.now()
            key_info['usage_count'] += 1

            return {
                'user_id': key_info['user_id'],
                'permissions': key_info['permissions']
            }

        except Exception as e:
            self.logger.error(f"API validation error: {e}")
            return None

    def _calculate_signature(self, secret: str, timestamp: str, data: str) -> str:
        """Calculate HMAC signature"""
        message = f"{timestamp}{data}"
        signature = hmac.new(
            secret.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        return signature

class SecurityMonitor:
    """Real-time security monitoring and threat detection"""

    def __init__(self, config: SecurityConfig):
        self.config = config
        self.threat_patterns = {
            'brute_force': {'failed_logins': 10, 'time_window': 300},  # 10 failures in 5 minutes
            'suspicious_api': {'requests_per_minute': 100},
            'geo_anomaly': {'new_location': True},
            'privilege_escalation': {'admin_actions': 5, 'time_window': 60}
        }

        self.security_events: List[Dict[str, Any]] = []
        self.logger = logging.getLogger("SecurityMonitor")

    async def analyze_security_event(self, event: Dict[str, Any]) -> List[str]:
        """Analyze event for security threats"""
        threats = []

        try:
            # Check for brute force attacks
            if await self._detect_brute_force(event):
                threats.append('brute_force_attack')

            # Check for suspicious API usage
            if await self._detect_suspicious_api(event):
                threats.append('suspicious_api_usage')

            # Check for privilege escalation
            if await self._detect_privilege_escalation(event):
                threats.append('privilege_escalation')

            if threats:
                self.logger.warning(f"Security threats detected: {threats}")

        except Exception as e:
            self.logger.error(f"Security analysis error: {e}")

        return threats

    async def _detect_brute_force(self, event: Dict[str, Any]) -> bool:
        """Detect brute force attacks"""
        if event.get('action') != 'login' or event.get('success'):
            return False

        # Count failed logins from same IP in time window
        ip_address = event.get('ip_address')
        cutoff_time = datetime.now() - timedelta(seconds=300)

        failed_count = sum(
            1 for e in self.security_events
            if (e.get('ip_address') == ip_address and
                e.get('action') == 'login' and
                not e.get('success') and
                e.get('timestamp', datetime.min) > cutoff_time)
        )

        return failed_count >= 10

    async def _detect_suspicious_api(self, event: Dict[str, Any]) -> bool:
        """Detect suspicious API usage"""
        if event.get('action') != 'api_call':
            return False

        # Count API calls from same source in last minute
        api_key = event.get('api_key')
        cutoff_time = datetime.now() - timedelta(minutes=1)

        api_count = sum(
            1 for e in self.security_events
            if (e.get('api_key') == api_key and
                e.get('action') == 'api_call' and
                e.get('timestamp', datetime.min) > cutoff_time)
        )

        return api_count >= 100

    async def _detect_privilege_escalation(self, event: Dict[str, Any]) -> bool:
        """Detect privilege escalation attempts"""
        if event.get('action') != 'admin_action':
            return False

        # Count admin actions from same user in time window
        user_id = event.get('user_id')
        cutoff_time = datetime.now() - timedelta(minutes=1)

        admin_count = sum(
            1 for e in self.security_events
            if (e.get('user_id') == user_id and
                e.get('action') == 'admin_action' and
                e.get('timestamp', datetime.min) > cutoff_time)
        )

        return admin_count >= 5

class TradingSecurityManager:
    """Main security manager for trading system"""

    def __init__(self, config: SecurityConfig):
        self.config = config
        self.encryption_manager = EncryptionManager(config.__dict__)
        self.auth_manager = AuthenticationManager(config, self.encryption_manager)
        self.authz_manager = AuthorizationManager()
        self.audit_logger = AuditLogger(config)
        self.api_security = APISecurityManager(config)
        self.security_monitor = SecurityMonitor(config)

        self.logger = logging.getLogger("TradingSecurityManager")

    async def initialize(self):
        """Initialize security subsystems"""
        try:
            # Connect to databases
            await self._connect_databases()

            # Load existing data
            await self._load_security_data()

            self.logger.info("Trading security manager initialized")

        except Exception as e:
            self.logger.error(f"Security initialization failed: {e}")
            raise

    async def _connect_databases(self):
        """Connect to security databases"""
        # This would connect to your databases
        pass

    async def _load_security_data(self):
        """Load existing security data"""
        # This would load users, sessions, etc. from database
        pass

    def get_security_middleware(self):
        """Get security middleware for web framework"""
        async def security_middleware(request, handler):
            # Extract authentication info
            auth_header = request.headers.get('Authorization')
            api_key = request.headers.get('X-API-Key')

            user_context = None

            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header[7:]
                user_context = await self.auth_manager.validate_session(token)

            elif api_key:
                signature = request.headers.get('X-Signature')
                timestamp = request.headers.get('X-Timestamp')
                request_data = await request.text()

                api_context = self.api_security.validate_api_request(
                    api_key, signature, timestamp, request_data
                )

                if api_context:
                    user_context = {
                        'user_id': api_context['user_id'],
                        'security_level': 'trader',  # API keys are trader level
                        'permissions': api_context['permissions']
                    }

            # Add user context to request
            request['user_context'] = user_context

            # Log API call
            if user_context:
                await self.audit_logger.log_event(
                    user_id=user_context['user_id'],
                    action=AuditAction.API_CALL,
                    resource=request.path,
                    details={'method': request.method},
                    ip_address=request.remote,
                    user_agent=request.headers.get('User-Agent', ''),
                    success=True
                )

            return await handler(request)

        return security_middleware

    def create_secure_config(self) -> SecurityConfig:
        """Create secure configuration"""
        return SecurityConfig(
            jwt_secret=secrets.token_urlsafe(64),
            jwt_expiration_hours=24,
            api_rate_limit_per_minute=60,
            max_failed_login_attempts=5,
            account_lockout_duration_minutes=30,
            require_2fa=True,
            allowed_ip_ranges=['10.0.0.0/8', '**********/12', '***********/16'],
            password_min_length=12,
            password_require_special=True,
            session_timeout_minutes=60,
            audit_retention_days=365
        )

# Security configuration and setup
def create_security_config() -> SecurityConfig:
    """Create production security configuration"""
    return SecurityConfig(
        jwt_secret=os.getenv('JWT_SECRET', secrets.token_urlsafe(64)),
        jwt_expiration_hours=int(os.getenv('JWT_EXPIRATION_HOURS', '24')),
        api_rate_limit_per_minute=int(os.getenv('API_RATE_LIMIT', '60')),
        max_failed_login_attempts=int(os.getenv('MAX_FAILED_LOGIN_ATTEMPTS', '5')),
        account_lockout_duration_minutes=int(os.getenv('ACCOUNT_LOCKOUT_MINUTES', '30')),
        require_2fa=os.getenv('REQUIRE_2FA', 'true').lower() == 'true',
        allowed_ip_ranges=os.getenv('ALLOWED_IP_RANGES', '').split(',') if os.getenv('ALLOWED_IP_RANGES') else [],
        password_min_length=int(os.getenv('PASSWORD_MIN_LENGTH', '12')),
        password_require_special=os.getenv('PASSWORD_REQUIRE_SPECIAL', 'true').lower() == 'true',
        session_timeout_minutes=int(os.getenv('SESSION_TIMEOUT_MINUTES', '60')),
        audit_retention_days=int(os.getenv('AUDIT_RETENTION_DAYS', '365'))
    )

# Usage example
async def setup_trading_security():
    """Setup comprehensive trading security"""
    config = create_security_config()
    security_manager = TradingSecurityManager(config)
    await security_manager.initialize()
    return security_manager