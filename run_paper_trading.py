#!/usr/bin/env python3
"""
AstroA Paper Trading Launcher
Entry point for starting paper trading sessions
"""

import asyncio
import argparse
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agents.paper_trading.paper_trading_engine import PaperTradingEngine
from config.settings import Config
from config.paper_trading_config import paper_config

class PaperTradingLauncher:
    """Launcher for paper trading sessions"""

    def __init__(self):
        self.engine = None
        self.setup_logging()

    def setup_logging(self):
        """Setup logging for paper trading"""
        log_file = f"logs/paper_trading_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        if self.engine:
            asyncio.create_task(self.engine.stop_paper_trading())

    async def start_session(self, duration_hours: int = None):
        """Start a paper trading session"""
        print("🌟 Starting AstroA Paper Trading Session")
        print("=" * 50)
        print(f"💰 Initial Cash: ${paper_config.initial_cash:,.2f}")
        print(f"📊 Max Positions: {paper_config.max_positions}")
        print(f"🎯 Tradable Symbols: {', '.join(paper_config.tradable_symbols[:5])}...")
        print(f"⚡ Update Interval: {paper_config.data_update_interval}s")
        print("=" * 50)

        self.engine = PaperTradingEngine()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        try:
            if duration_hours:
                print(f"📅 Session duration: {duration_hours} hours")
                # Start session with timeout
                await asyncio.wait_for(
                    self.engine.start_paper_trading(),
                    timeout=duration_hours * 3600
                )
            else:
                print("📅 Session duration: Unlimited (Ctrl+C to stop)")
                await self.engine.start_paper_trading()

        except asyncio.TimeoutError:
            print(f"⏰ Session completed after {duration_hours} hours")
            await self.engine.stop_paper_trading()
        except KeyboardInterrupt:
            print("\n🛑 Session interrupted by user")
            await self.engine.stop_paper_trading()
        except Exception as e:
            print(f"❌ Session error: {str(e)}")
            if self.engine:
                await self.engine.stop_paper_trading()
            raise

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='AstroA Paper Trading System')
    parser.add_argument(
        '--duration',
        type=int,
        help='Session duration in hours (default: unlimited)'
    )
    parser.add_argument(
        '--strategies',
        nargs='+',
        default=['mean_reversion', 'momentum'],
        help='Strategies to run (default: mean_reversion momentum)'
    )
    parser.add_argument(
        '--symbols',
        nargs='+',
        help='Override default trading symbols'
    )
    parser.add_argument(
        '--test-config',
        action='store_true',
        help='Test configuration and exit'
    )

    args = parser.parse_args()

    # Test configuration if requested
    if args.test_config:
        try:
            paper_config.validate()
            print("✅ Configuration is valid")
            print(f"📊 Trading config: {paper_config.get_trading_config()}")
            return
        except Exception as e:
            print(f"❌ Configuration error: {e}")
            sys.exit(1)

    # Override symbols if provided
    if args.symbols:
        paper_config.tradable_symbols = args.symbols
        print(f"🎯 Using custom symbols: {args.symbols}")

    # Create launcher
    launcher = PaperTradingLauncher()

    # Start session
    try:
        asyncio.run(launcher.start_session(args.duration))
    except KeyboardInterrupt:
        print("🛑 Paper trading session terminated")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
