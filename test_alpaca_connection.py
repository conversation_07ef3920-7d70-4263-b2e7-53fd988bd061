#!/usr/bin/env python3
"""
Test Alpaca Paper Trading Connection
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import alpaca_trade_api as tradeapi
    from config.paper_trading_config import paper_config
    
    print("🌟 Testing Alpaca Paper Trading Connection")
    print("=" * 50)
    
    # Initialize API
    api = tradeapi.REST(
        paper_config.api_key,
        paper_config.secret_key,
        paper_config.base_url,
        api_version='v2'
    )
    
    # Test account connection
    print("📡 Connecting to Alpaca...")
    account = api.get_account()
    
    print("✅ Connection successful!")
    print(f"📊 Account Status: {account.status}")
    print(f"💰 Portfolio Value: ${float(account.portfolio_value):,.2f}")
    print(f"💵 Buying Power: ${float(account.buying_power):,.2f}")
    print(f"💸 Cash: ${float(account.cash):,.2f}")
    print(f"🔒 Day Trading Buying Power: ${float(account.daytrading_buying_power):,.2f}")
    
    # Test market data
    print("\n📈 Testing market data access...")
    try:
        # Get latest quote for Bitcoin
        quote = api.get_latest_crypto_quote("BTCUSD")
        print(f"✅ BTC/USD Quote: ${quote.bid_price:.2f} / ${quote.ask_price:.2f}")
    except Exception as e:
        print(f"⚠️  Crypto quotes not available: {e}")
    
    # Test positions
    print("\n📋 Current positions:")
    positions = api.list_positions()
    if positions:
        for position in positions:
            print(f"  {position.symbol}: {position.qty} shares @ ${float(position.avg_cost):.2f}")
    else:
        print("  No open positions")
    
    # Test orders
    print("\n📝 Recent orders:")
    orders = api.list_orders(status='all', limit=5)
    if orders:
        for order in orders:
            print(f"  {order.symbol}: {order.side} {order.qty} @ {order.order_type} - {order.status}")
    else:
        print("  No recent orders")
    
    print("\n🎉 Alpaca connection test completed successfully!")
    print("🚀 Ready for paper trading!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Run: pip install alpaca-trade-api")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
    print("💡 Check your API keys and internet connection")
    sys.exit(1)
